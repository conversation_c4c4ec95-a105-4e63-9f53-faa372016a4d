<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.attendance.dao.AttendanceStatisticsDao">

    <!-- 调用考勤状态统计存储过程 -->
    <select id="getAttendanceStatusStatistics" statementType="CALLABLE" resultType="map">
        {CALL SP_ATTENDANCE_STATUS_STATISTICS(
            #{month, mode=IN, jdbcType=VARCHAR},
            #{pj0101, mode=IN, jdbcType=NUMERIC},
            #{personType, mode=IN, jdbcType=VARCHAR},
            #{result, mode=OUT, jdbcType=CURSOR, resultMap=attendanceResultMap}
        )}
    </select>

    <!-- 调用工时统计存储过程 -->
    <select id="getAttendanceWorkhoursStatistics" statementType="CALLABLE" resultType="map">
        {CALL SP_ATTENDANCE_WORKHOURS_STATISTICS(
            #{month, mode=IN, jdbcType=VARCHAR},
            #{pj0101, mode=IN, jdbcType=NUMERIC},
            #{personType, mode=IN, jdbcType=VARCHAR},
            #{result, mode=OUT, jdbcType=CURSOR, resultMap=workhoursResultMap}
        )}
    </select>

    <!-- 调用综合考勤统计存储过程 -->
    <select id="getAttendanceComprehensiveStatistics" statementType="CALLABLE" resultType="map">
        {CALL SP_ATTENDANCE_COMPREHENSIVE_STATISTICS(
            #{month, mode=IN, jdbcType=VARCHAR},
            #{pj0101, mode=IN, jdbcType=NUMERIC},
            #{personType, mode=IN, jdbcType=VARCHAR},
            #{result, mode=OUT, jdbcType=CURSOR, resultMap=comprehensiveResultMap}
        )}
    </select>

    <!-- 获取指定月份的天数 -->
    <select id="getDaysInMonth" resultType="integer">
        SELECT TO_NUMBER(TO_CHAR(LAST_DAY(TO_DATE(#{month} || '-01', 'YYYY-MM-DD')), 'DD')) AS days_in_month
        FROM DUAL
    </select>

    <!-- 获取人员基本信息 -->
    <select id="getPersonInfo" resultType="map">
        SELECT DISTINCT 
            person_name,
            user_id,
            person_type,
            pj0101
        FROM B_KQ02
        WHERE TO_CHAR(checkdate, 'YYYY-MM') = #{month}
        <if test="pj0101 != null">
            AND pj0101 = #{pj0101}
        </if>
        <if test="personType != null and personType != ''">
            AND person_type = #{personType}
        </if>
        <if test="personName != null and personName != ''">
            AND person_name LIKE '%' || #{personName} || '%'
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        ORDER BY person_name, user_id
    </select>

    <!-- 验证项目是否存在 -->
    <select id="checkProjectExists" resultType="boolean">
        SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
        FROM B_KQ02
        WHERE pj0101 = #{pj0101}
        AND ROWNUM = 1
    </select>

    <!-- 获取考勤原始数据 -->
    <select id="getAttendanceRawData" resultType="map">
        SELECT 
            kq0201,
            user_id,
            person_name,
            person_type,
            pj0101,
            checkdate,
            direction,
            attendtype,
            TO_CHAR(checkdate, 'YYYY-MM-DD HH24:MI:SS') as checkdate_str,
            TO_CHAR(checkdate, 'DD') as day_of_month
        FROM B_KQ02
        WHERE TO_CHAR(checkdate, 'YYYY-MM') = #{month}
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="pj0101 != null">
            AND pj0101 = #{pj0101}
        </if>
        ORDER BY user_id, checkdate
    </select>

    <!-- 获取月度考勤汇总统计 -->
    <select id="getMonthlyAttendanceSummary" resultType="map">
        SELECT 
            COUNT(DISTINCT user_id) as total_persons,
            COUNT(DISTINCT TO_CHAR(checkdate, 'YYYY-MM-DD')) as total_attendance_days,
            COUNT(*) as total_records,
            COUNT(DISTINCT CASE WHEN person_type = '1' THEN user_id END) as worker_count,
            COUNT(DISTINCT CASE WHEN person_type = '2' THEN user_id END) as manager_count,
            TO_NUMBER(TO_CHAR(LAST_DAY(TO_DATE(#{month} || '-01', 'YYYY-MM-DD')), 'DD')) as days_in_month
        FROM B_KQ02
        WHERE TO_CHAR(checkdate, 'YYYY-MM') = #{month}
        <if test="pj0101 != null">
            AND pj0101 = #{pj0101}
        </if>
        <if test="personType != null and personType != ''">
            AND person_type = #{personType}
        </if>
    </select>

    <!-- 结果映射 - 考勤状态 -->
    <resultMap id="attendanceResultMap" type="map">
        <result property="person_name" column="PERSON_NAME"/>
        <result property="user_id" column="USER_ID"/>
        <!-- 动态日期列将由存储过程返回 -->
    </resultMap>

    <!-- 结果映射 - 工时统计 -->
    <resultMap id="workhoursResultMap" type="map">
        <result property="person_name" column="PERSON_NAME"/>
        <result property="user_id" column="USER_ID"/>
        <!-- 动态工时列将由存储过程返回 -->
    </resultMap>

    <!-- 结果映射 - 综合统计 -->
    <resultMap id="comprehensiveResultMap" type="map">
        <result property="person_name" column="PERSON_NAME"/>
        <result property="user_id" column="USER_ID"/>
        <!-- 动态综合列将由存储过程返回 -->
    </resultMap>

</mapper>
