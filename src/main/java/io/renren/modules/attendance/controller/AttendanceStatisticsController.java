package io.renren.modules.attendance.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.utils.Result;
import io.renren.modules.attendance.dto.AttendanceQueryDTO;
import io.renren.modules.attendance.dto.AttendanceStatisticsDTO;
import io.renren.modules.attendance.service.AttendanceStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 考勤统计控制器
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03
 */
@Slf4j
@RestController
@RequestMapping("/attendance/statistics")
@Api(tags = "考勤统计管理")
public class AttendanceStatisticsController {

    @Autowired
    private AttendanceStatisticsService attendanceStatisticsService;

    @GetMapping("/query")
    @ApiOperation("考勤统计查询")
    @LogOperation("考勤统计查询")
    public Result<List<AttendanceStatisticsDTO>> queryAttendanceStatistics(
            @ApiParam("查询参数") @Valid AttendanceQueryDTO queryDTO) {

        try {
            List<AttendanceStatisticsDTO> result = attendanceStatisticsService.getAttendanceStatistics(queryDTO);
            return new Result<List<AttendanceStatisticsDTO>>().ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("考勤统计查询参数错误: {}", e.getMessage());
            return new Result<List<AttendanceStatisticsDTO>>().error(400, e.getMessage());
        } catch (Exception e) {
            log.error("考勤统计查询失败", e);
            return new Result<List<AttendanceStatisticsDTO>>().error("考勤统计查询失败");
        }
    }

    @("/status")
    @ApiOperation("考勤状态统计")
    @LogOperation("考勤状态统计")
    public Result<List<AttendanceStatisticsDTO>> queryAttendanceStatus(
            @ApiParam("查询参数") @Valid AttendanceQueryDTO queryDTO) {

        try {
            List<AttendanceStatisticsDTO> result = attendanceStatisticsService.getAttendanceStatusStatistics(queryDTO);
            return new Result<List<AttendanceStatisticsDTO>>().ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("考勤状态统计查询参数错误: {}", e.getMessage());
            return new Result<List<AttendanceStatisticsDTO>>().error(400, e.getMessage());
        } catch (Exception e) {
            log.error("考勤状态统计查询失败", e);
            return new Result<List<AttendanceStatisticsDTO>>().error("考勤状态统计查询失败");
        }
    }

    @GetMapping("/workhours")
    @ApiOperation("工时统计")
    @LogOperation("工时统计")
    public Result<List<AttendanceStatisticsDTO>> queryWorkhours(
            @ApiParam("查询参数") @Valid AttendanceQueryDTO queryDTO) {

        try {
            List<AttendanceStatisticsDTO> result = attendanceStatisticsService.getWorkhoursStatistics(queryDTO);
            return new Result<List<AttendanceStatisticsDTO>>().ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("工时统计查询参数错误: {}", e.getMessage());
            return new Result<List<AttendanceStatisticsDTO>>().error(400, e.getMessage());
        } catch (Exception e) {
            log.error("工时统计查询失败", e);
            return new Result<List<AttendanceStatisticsDTO>>().error("工时统计查询失败");
        }
    }

    @GetMapping("/comprehensive")
    @ApiOperation("综合考勤统计")
    @LogOperation("综合考勤统计")
    public Result<List<AttendanceStatisticsDTO>> queryComprehensive(
            @ApiParam("查询参数") @Valid AttendanceQueryDTO queryDTO) {

        try {
            List<AttendanceStatisticsDTO> result = attendanceStatisticsService.getComprehensiveStatistics(queryDTO);
            return new Result<List<AttendanceStatisticsDTO>>().ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("综合考勤统计查询参数错误: {}", e.getMessage());
            return new Result<List<AttendanceStatisticsDTO>>().error(400, e.getMessage());
        } catch (Exception e) {
            log.error("综合考勤统计查询失败", e);
            return new Result<List<AttendanceStatisticsDTO>>().error("综合考勤统计查询失败");
        }
    }

    @GetMapping("/summary")
    @ApiOperation("月度考勤汇总")
    @LogOperation("月度考勤汇总")
    public Result<Map<String, Object>> getMonthlyAttendanceSummary(
            @ApiParam("查询参数") @Valid AttendanceQueryDTO queryDTO) {

        try {
            Map<String, Object> result = attendanceStatisticsService.getMonthlyAttendanceSummary(queryDTO);
            return new Result<Map<String, Object>>().ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("月度考勤汇总查询参数错误: {}", e.getMessage());
            return new Result<Map<String, Object>>().error(400, e.getMessage());
        } catch (Exception e) {
            log.error("月度考勤汇总查询失败", e);
            return new Result<Map<String, Object>>().error("月度考勤汇总查询失败");
        }
    }

    @GetMapping("/export")
    @ApiOperation("导出考勤统计Excel")
    @LogOperation("导出考勤统计Excel")
    public ResponseEntity<byte[]> exportAttendanceStatistics(
            @ApiParam("查询参数") @Valid AttendanceQueryDTO queryDTO) {

        try {
            byte[] excelData = attendanceStatisticsService.exportAttendanceStatistics(queryDTO);

            String fileName = String.format("考勤统计_%s.xlsx", queryDTO.getMonth());
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", encodedFileName);
            headers.setContentLength(excelData.length);

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (IllegalArgumentException e) {
            log.warn("导出考勤统计参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("导出考勤统计失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping("/query")
    @ApiOperation("考勤统计查询（POST方式）")
    @LogOperation("考勤统计查询（POST方式）")
    public Result<List<AttendanceStatisticsDTO>> queryAttendanceStatisticsPost(
            @ApiParam("查询参数") @Valid @RequestBody AttendanceQueryDTO queryDTO) {

        try {
            List<AttendanceStatisticsDTO> result = attendanceStatisticsService.getAttendanceStatistics(queryDTO);
            return new Result<List<AttendanceStatisticsDTO>>().ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("考勤统计查询参数错误: {}", e.getMessage());
            return new Result<List<AttendanceStatisticsDTO>>().error(400, e.getMessage());
        } catch (Exception e) {
            log.error("考勤统计查询失败", e);
            return new Result<List<AttendanceStatisticsDTO>>().error("考勤统计查询失败");
        }
    }

    @GetMapping("/days/{month}")
    @ApiOperation("获取指定月份天数")
    public Result<Integer> getDaysInMonth(
            @ApiParam("月份（YYYY-MM格式）") @PathVariable String month) {

        try {
            Integer days = attendanceStatisticsService.getDaysInMonth(month);
            return new Result<Integer>().ok(days);
        } catch (Exception e) {
            log.error("获取月份天数失败", e);
            return new Result<Integer>().error("获取月份天数失败");
        }
    }

    @GetMapping("/validate")
    @ApiOperation("验证查询参数")
    public Result<String> validateQueryParams(
            @ApiParam("查询参数") @Valid AttendanceQueryDTO queryDTO) {

        try {
            attendanceStatisticsService.validateQueryParams(queryDTO);
            return new Result<String>().ok("参数验证通过");
        } catch (IllegalArgumentException e) {
            log.warn("参数验证失败: {}", e.getMessage());
            return new Result<String>().error(400, e.getMessage());
        } catch (Exception e) {
            log.error("参数验证异常", e);
            return new Result<String>().error("参数验证异常");
        }
    }
}
