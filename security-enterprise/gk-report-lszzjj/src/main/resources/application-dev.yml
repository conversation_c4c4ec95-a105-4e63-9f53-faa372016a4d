spring:
  datasource:
    druid:
      driver-class-name: oracle.jdbc.OracleDriver
      url: **************************************
      username: gk_report_lszj
      password: gk_report_lszj
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  #RabbitMQ 基本配置
  rabbitmq:
    host: **********
    port: 5672
    username: xygk
    password: xygk@123
    virtual-host: /
    connection-timeout: 15000
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 3
          max-interval: 10000
  redis:
    database: 8
    host: **********
    port: 6379
    password:    # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
#凉山住建局数据上报配置
lszjj:
  httpUrl: http://*************:17101
  userName: 18111597020
  password: 18111597020
  loginId: ZTFHX202250619002