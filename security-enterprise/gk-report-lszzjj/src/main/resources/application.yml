server:
  undertow:
    threads:
      worker: 64
      io: 8
    # 每块buffer的空间大小,越小的空间被利用越充分，不要设置太大，以免影响其他应用，合适即可
    buffer-size: 1024
    # 是否分配的直接内存(NIO直接分配的堆外内存)
    direct-buffers: true
  servlet:
    context-path: /
    session:
      cookie:
        http-only: true
  port: 10022
# oracle
spring:
  # 环境 dev|test|prod
  profiles:
    active: dev
  messages:
    encoding: UTF-8
    basename: i18n/messages
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
renren:
  redis:
    open: true  # 是否开启redis缓存  true开启   false关闭
# 附件储存方式：1、本地储存；2、附件服务器储存 ，默认附件服务器存储
upload:
  isOpenLocal: false
#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: ID_WORKER
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      #驼峰下划线转换
      column-underline: true
      #db-type: mysql
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
