spring:
  datasource:
    druid:
      driver-class-name: oracle.jdbc.OracleDriver
      url: **************************************
      username: gk_report_lszj
      password: gk_report_lszj
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
#凉山州住建局数据上报配置
lszjj:
  httpUrl: http://11218.169.39:17101/
  userName: 18111597020
  passWord: 18111597020
  loginId: ZTFHX202250619002