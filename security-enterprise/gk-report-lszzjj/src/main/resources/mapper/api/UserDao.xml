<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.report.dao.UserDao">

    <select id="getUserByMobile" resultType="io.renren.api.report.entity.UserEntity">
        select * from tb_user where mobile = #{value}
    </select>

    <select id="getUserByUserId" resultType="io.renren.api.report.entity.UserEntity">
        select * from tb_user where id = #{value}
    </select>
</mapper>