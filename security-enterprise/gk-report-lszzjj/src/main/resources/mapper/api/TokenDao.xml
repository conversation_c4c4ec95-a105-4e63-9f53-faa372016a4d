<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.report.dao.TokenDao">

    <select id="getByToken" resultType="io.renren.api.report.entity.TokenEntity">
        select * from tb_token where token = #{value}
    </select>

    <select id="getByUserId" resultType="io.renren.api.report.entity.TokenEntity">
        select * from tb_token where user_id = #{value}
    </select>

</mapper>