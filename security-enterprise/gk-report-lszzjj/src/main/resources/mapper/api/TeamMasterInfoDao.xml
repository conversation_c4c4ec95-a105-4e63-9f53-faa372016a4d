<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.TeamMasterInfoDao">

    <resultMap type="io.renren.api.recept.entity.TeamMasterInfoEntity" id="teamMasterInfoMap">
        <result property="tm0101" column="TM0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="cp0201" column="cp0201"/>
        <result property="name" column="NAME"/>
        <result property="leaderName" column="LEADERNAME"/>
        <result property="leaderIdentification" column="LEADERIDENTIFICATION"/>
        <result property="leaderPhone" column="LEADERPHONE"/>
        <result property="type" column="TYPE"/>
        <result property="groupCode" column="GROUPCODE"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="reportStatus" column="REPORT_STATUS"/>
        <result property="errorCause" column="ERROR_CAUSE"/>
        <result property="reportDate" column="REPORT_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="selectByPj0101" resultType="io.renren.api.recept.entity.TeamMasterInfoEntity">
        select
            t.name,
            t.group_code,
            t.tm0101
        from TEAM_MASTER_INFO t
        where t.pj0101 = #{pj0101}
            and t.cp0201 = #{cp0201}
            and t.TYPE = '1'
    </select>
    <select id="selectTeamInfoByReportStatus" resultType="io.renren.api.report.dto.TeamMasterInfo">
        select
            t.GROUP_CODE groupCode,
            t.tm0101,
            p.project_code projectcode,
            c.code as companycode,
            t.name,
            t.leader_name as leadername,
            t.leader_identification as leaderidentification,
            t.leader_phone as leaderphone,
            t.type
        from TEAM_MASTER_INFO t, PROJECT_INFO p, PROJECT_COMPANY_INFO c
        where t.PJ0101 = p.PJ0101
            and c.PJ0101 = t.PJ0101
            and c.CP0201 = t.CP0201
            and p.PROJECT_CODE is not null
            and t.REPORT_STATUS = '0'
            and c.REPORT_STATUS = '1'
    </select>

</mapper>