<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.ProjectWorkerInfoDao">

    <resultMap type="io.renren.api.recept.entity.ProjectWorkerInfoEntity" id="projectWorkerInfoMap">
        <result property="userid" column="USERID"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="tm0101" column="tm0101"/>
        <result property="name" column="NAME"/>
        <result property="identification" column="IDENTIFICATION"/>
        <result property="startExpiration" column="STARTEXPIRATION"/>
        <result property="endExpiration" column="ENDEXPIRATION"/>
        <result property="issuingUnit" column="ISSUINGUNIT"/>
        <result property="address" column="ADDRESS"/>
        <result property="ethnic" column="ETHNIC"/>
        <result property="phone" column="PHONE"/>
        <result property="photo" column="PHOTO"/>
        <result property="recentPhoto" column="RECENTPHOTO"/>
        <result property="identificationCopyFront" column="IDENTIFICATIONCOPYFRONT"/>
        <result property="identificationCopyBack" column="IDENTIFICATIONCOPYBACK"/>
        <result property="politicalStatus" column="POLITICALSTATUS"/>
        <result property="degreeName" column="DEGREENAME"/>
        <result property="urgentContact" column="URGENTCONTACT"/>
        <result property="urgentTel" column="URGENTTEL"/>
        <result property="postType" column="POSTTYPE"/>
        <result property="isLeader" column="ISLEADER"/>
        <result property="workTypeCode" column="WORKTYPECODE"/>
        <result property="enterDate" column="ENTERDATE"/>
        <result property="exitDate" column="EXITDATE"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="reportStatus" column="REPORT_STATUS"/>
        <result property="errorCause" column="ERROR_CAUSE"/>
        <result property="reportDate" column="REPORT_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="selectWorkerInfoByReportStatus" resultType="io.renren.api.report.dto.WorkerInfo">
        select
            w.userid,
            p.project_code as projectCode,
            c.code as companyCode,
            t.group_code as groupCode,
            w.name,
            w.identification,
            w.start_expiration as startExpiration,
            w.end_expiration as endExpiration,
            w.issuing_unit as issuingUnit,
            w.address,
            w.ethnic,
            w.phone,
            w.photo,
            w.recent_photo as recentPhoto,
            w.identification_copy_front as identificationCopyFront,
            w.identification_copy_back as identificationCopyBack,
            w.political_status as politicalStatus,
            w.degree_name as degreeName,
            w.urgent_contact as urgentContact,
            w.urgent_tel as urgentTel,
            w.post_type as postType,
            w.is_leader as isLeader,
            w.work_type_code as workTypeCode,
            w.enter_date as enterDate,
            w.exit_date as exitDate,
            w.birth_date as birthDate,
            w.blood_type as bloodType,
            w.bank_code as bankCode,
            w.bank_name as bankName
        from PROJECT_WORKER_INFO w,
            PROJECT_COMPANY_INFO c,
            TEAM_MASTER_INFO t,
            PROJECT_INFO p
        where p.PJ0101 = c.PJ0101
            and w.PJ0101 = p.PJ0101
            and w.TM0101 = t.TM0101
            and t.CP0201 = c.CP0201
            and p.PROJECT_CODE is not null
            and t.REPORT_STATUS = '1'
            and c.REPORT_STATUS = '1'
            and w.REPORT_STATUS = '0'
    </select>

</mapper>