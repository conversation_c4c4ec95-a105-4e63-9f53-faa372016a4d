<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.WorkerAttendanceInfoDao">
    <resultMap type="io.renren.api.recept.entity.WorkerAttendanceInfoEntity" id="workerAttendanceInfoMap">
        <result property="kq0201" column="KQ0201"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="swipeTime" column="SWIPETIME"/>
        <result property="swipeType" column="SWIPETYPE"/>
        <result property="image" column="IMAGE"/>
        <result property="attendType" column="ATTENDTYPE"/>
        <result property="curnumber" column="CURNUMBER"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="reportStatus" column="REPORT_STATUS"/>
        <result property="errorCause" column="ERROR_CAUSE"/>
        <result property="reportDate" column="REPORT_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="selectWorkerAttendanceInfoByReportStatus" resultType="io.renren.api.report.dto.WorkerAttendanceInfo">
        select *
        from (
                 select a.kq0201,
                        a.kq0201             curnumber,
                        p.project_code    as projectCode,
                        w.identification,
                        a.swipe_time      as swipeTime,
                        a.swipe_type      as swipeType,
                        a.image,
                        a.attend_type     as attendType,
                        a.device_code     as deviceCode,
                        a.work_point_name as workPointName,
                        a.temperature
                 from WORKER_ATTENDANCE_INFO a,
                      PROJECT_INFO p,
                      TEAM_MASTER_INFO t,
                      PROJECT_WORKER_INFO w
                 where p.PJ0101 = a.PJ0101
                   and p.PJ0101 = t.PJ0101
                   and t.TM0101 = w.TM0101
                   and w.USERID = a.USER_ID
                   and p.PROJECT_CODE is not null
                   and t.REPORT_STATUS = '1'
                   and w.REPORT_STATUS = '1'
                   and a.REPORT_STATUS = '0'
                   and a.SWIPE_TIME &lt; sysdate - 10 / 1440
                 order by a.kq0201)
        where ROWNUM &lt;= 50
    </select>

    <select id="getAsyncRequestCode" resultType="io.renren.api.report.dto.AsyncResultDTO">
        select distinct (async_request_code) asyncRequestCode,
                        project_code         projectId
        from (select w.async_request_code,
                     p.project_code,
                     rownum
              from WORKER_ATTENDANCE_INFO w,
                   PROJECT_INFO p
              where w.REPORT_STATUS = '1'
                and p.PJ0101 = w.PJ0101
                and w.REPORT_DATE &lt; sysdate - 10 / 1440
              order by w.REPORT_DATE desc)
        where rownum &lt;= 100
    </select>

    <update id="updateByAsyncRetrun">
        update WORKER_ATTENDANCE_INFO w
        set REPORT_STATUS = '3',
            ERROR_CAUSE   = #{message},
            update_date   = sysdate
        where ASYNC_REQUEST_CODE = #{asyncRequestCode}
          and REPORT_STATUS = '1'
    </update>

    <update id="updateBatchByIds">
        update WORKER_ATTENDANCE_INFO t
        set t.report_status=#{reportStatus},
            t.error_cause=#{errorCause},
            t.async_request_code=#{asyncRequestCode},
            t.report_date = sysdate
        where t.kq0201 in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>