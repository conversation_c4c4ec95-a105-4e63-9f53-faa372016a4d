<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.ProjectCompanyInfoDao">
    <select id="selectCompanyInfoByReportStatus" resultType="io.renren.api.report.dto.CompanyInfo">
        select a.PROJECT_CODE             as projectCode,
               b.NAME,
               b.cp0201,
               b.cp0201,
               b.pj0101,
               b.name,
               b.code,
               b.type,
               b.business_scope           as businessScope,
               b.register_date            as registerDate,
               b.establish_date           as establishDate,
               b.legal_man                as legalMan,
               b.legal_man_id_card_number as legalManIdCardNumber,
               b.legal_man_phone          as legalManPhone,
               b.reg_capital              as regCapital,
               b.fact_reg_capital         as factRegCapital,
               b.address,
               b.zip_code                 as zipCode,
               b.office_phone             as officePhone,
               b.link_man                 as linkMan,
               b.link_phone               as linkphone,
               b.email,
               b.enter_date               as enterdate,
               b.exit_date                as exitDate,
               b.company_code             as companyCode
        from PROJECT_INFO a,
             PROJECT_COMPANY_INFO b
        where a.PJ0101 = b.PJ0101
          and a.PROJECT_CODE is not null
          and b.REPORT_STATUS = '0'
    </select>
    <select id="getCorpType" resultType="java.lang.String">
        select
            d.ORIGINAL_NAME
        from PROJECT_COMPANY_INFO c,
            DICT_INFO d
        where c.TYPE = d.ORIGINAL_VALUE
            and c.CP0201 = #{cp0201}
            and d.FIELD_TYPE = 'CORPTYPE'
    </select>
</mapper>