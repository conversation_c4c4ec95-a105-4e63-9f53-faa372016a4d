package io.renren.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.renren.common.redis.RedisUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Token管理工具类
 * 负责获取、缓存和刷新第三方系统的访问令牌
 *
 * <AUTHOR>
 * @date 2025/6/20 13:42
 */
@Component
public class TokenUtils {
    /**
     * 日志
     */
    private static final Logger logger = LoggerFactory.getLogger(TokenUtils.class);
    /**
     * 第三方系统地址
     */
    @Value("${lszjj.httpUrl}")
    private String baseUrl;
    /**
     * 第三方系统用户名
     */
    @Value("${lszjj.userName}")
    private String userName;
    /**
     * 第三方系统密码
     */
    @Value("${lszjj.password}")
    private String password;
    /**
     * 超时时间（毫秒）
     */
    @Value("${lszjj.timeout:5000}")
    private int timeoutMs;

    @Resource
    private RedisUtils redisUtils;

    /**
     * 缓存中的token的key
     */
    private static final String TOKEN_KEY = "lszjj:token";
    /**
     * 登录接口路径
     */
    private static final String LOGIN_PATH = "/login/apiLogin";
    /**
     * 登录成功code
     */
    private static final int SUCCESS_CODE = 200;
    /**
     * 默认的请求超时时间（毫秒）
     */
    private static final int DEFAULT_TIMEOUT = 5000;

    // 防止并发刷新token的锁
    private final ReentrantLock refreshLock = new ReentrantLock();

    /**
     * 应用启动时初始化token
     */
    @PostConstruct
    public void init() {
        logger.info("初始化Token,开始获取访问令牌");
        refreshAndStoreToken();
    }

    /**
     * 获取有效的访问令牌
     * 如果缓存中不存在或已过期，会自动刷新
     *
     * @return 访问令牌，获取失败返回null
     */
    public String getToken() {
        // 先尝试从缓存获取
        String cachedToken = getCachedToken();
        if (StringUtils.hasText(cachedToken)) {
            return cachedToken;
        }
        // 缓存中没有token，需要刷新
        return refreshTokenIfNeeded();
    }

    /**
     * 从缓存中获取token
     */
    private String getCachedToken() {
        try {
            if (redisUtils.hasKey(TOKEN_KEY)) {
                Object tokenObj = redisUtils.get(TOKEN_KEY);
                return tokenObj != null ? tokenObj.toString() : null;
            }
        } catch (Exception e) {
            logger.warn("从缓存获取token失败", e);
        }
        return null;
    }

    /**
     * 需要时刷新token（带锁保护，防止并发刷新）
     */
    private String refreshTokenIfNeeded() {
        if (refreshLock.tryLock()) {
            try {
                // 双重检查，可能其他线程已经刷新了
                String cachedToken = getCachedToken();
                if (StringUtils.hasText(cachedToken)) {
                    return cachedToken;
                }
                // 执行刷新
                return refreshAndStoreToken();
            } finally {
                refreshLock.unlock();
            }
        } else {
            // 如果获取锁失败，说明有其他线程在刷新，等待一下再尝试获取缓存的token
            try {
                Thread.sleep(100);
                return getCachedToken();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.warn("等待token刷新时被中断", e);
                return null;
            }
        }
    }

    /**
     * 刷新并存储token到缓存
     *
     * @return 新获取的token，失败返回null
     */
    private String refreshAndStoreToken() {
        try {
            // 参数校验
            if (!validateConfig()) {
                return null;
            }
            // 构建请求参数
            Map<String, Object> params = buildLoginParams();
            // 发送登录请求
            String loginUrl = baseUrl + LOGIN_PATH;
            String responseBody = HttpUtil.post(loginUrl, params, timeoutMs > 0 ? timeoutMs : DEFAULT_TIMEOUT);
            // 处理响应
            return processLoginResponse(responseBody);
        } catch (Exception e) {
            logger.error("刷新token异常", e);
            return null;
        }
    }

    /**
     * 验证配置参数
     */
    private boolean validateConfig() {
        if (!StringUtils.hasText(baseUrl)) {
            logger.error("baseUrl配置为空");
            return false;
        }
        if (!StringUtils.hasText(userName)) {
            logger.error("userName配置为空");
            return false;
        }
        if (!StringUtils.hasText(password)) {
            logger.error("password配置为空");
            return false;
        }
        return true;
    }

    /**
     * 构建登录请求参数
     */
    private Map<String, Object> buildLoginParams() {
        Map<String, Object> params = new HashMap<>(2);
        params.put("username", userName);
        params.put("password", password);
        return params;
    }

    /**
     * 处理登录响应
     */
    private String processLoginResponse(String responseBody) {
        if (!StringUtils.hasText(responseBody)) {
            logger.error("登录接口响应体为空");
            return null;
        }
        JSONObject responseJson;
        try {
            responseJson = JSON.parseObject(responseBody);
        } catch (Exception e) {
            logger.error("解析登录响应JSON失败，响应内容：{}", responseBody, e);
            return null;
        }
        if (responseJson == null) {
            logger.error("响应JSON解析结果为null");
            return null;
        }
        int code = responseJson.getIntValue("code");
        if (SUCCESS_CODE != code) {
            logger.error("登录失败，错误码：{}，响应：{}", code, responseBody);
            return null;
        }
        JSONObject dataObj = JSON.parseObject(responseJson.toJSONString()).getJSONObject("data");
        JSONObject tokenObj = dataObj != null ? dataObj.getJSONObject("token") : null;
        String accessToken = tokenObj != null ? tokenObj.getString("accessToken") : null;
        if (!StringUtils.hasText(accessToken)) {
            logger.error("登录成功但token为空，响应：{}", responseBody);
            return null;
        }
        // 存储到缓存
        try {
            redisUtils.set(TOKEN_KEY, accessToken, RedisUtils.HOUR_SIX_EXPIRE);
            return accessToken;
        } catch (Exception e) {
            logger.error("token缓存失败", e);
            // 即使缓存失败，也返回token供本次使用
            return accessToken;
        }
    }
}