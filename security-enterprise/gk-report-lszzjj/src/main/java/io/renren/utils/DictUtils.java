package io.renren.utils;

import io.renren.api.recept.dao.DictInfoDao;
import io.renren.api.recept.dto.DictInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/17 15:12
 */
@Component
public class DictUtils {
    /**
     * 日志
     */
    private static final Logger log = LoggerFactory.getLogger(DictUtils.class);
    /**
     * 数据字典集合
     */
    public static List<DictInfo> dictList = new ArrayList<>();
    @Resource
    private DictInfoDao dictInfoDao;

    @PostConstruct
    public void init() {
        log.info("————开始初始化数据字典————");
        //查询数据字典
        dictList = dictInfoDao.selectListByAll();
        log.info("————结束初始化数据字典————");
    }

}
