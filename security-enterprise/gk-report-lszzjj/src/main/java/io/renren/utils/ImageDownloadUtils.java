package io.renren.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import io.renren.common.file.FileProper;
import io.renren.common.minio.MinIoUtil;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 图片下载工具类
 *
 * <AUTHOR>
 * @Date 2020-12-23 9:26
 */
@Component
public class ImageDownloadUtils {
    /**
     * 考勤图片下载地址文件夹
     */
    private static final String PATH = "attendance";
    private static String filePath;
    private static FileProper fileProper;

    public ImageDownloadUtils(FileProper properties) {
        ImageDownloadUtils.filePath = properties.getPath();
        fileProper = properties;
    }

    /**
     * 根据URL下载考勤图片
     *
     * @param url        url地址
     * @param formatData 考勤时间
     * @return 文件储存处理之后的路径
     */
    public static String downPicture(String url, String formatData) {
        Boolean isOpenLocal = fileProper.getIsOpenLocal();
        if (isOpenLocal) {
            String path = filePath + PATH + File.separator + formatData + File.separator;
            FileUtil.mkdir(path);
            File file = HttpUtil.downloadFileFromUrl(url, FileUtil.file(path), 1000);
            if (file.exists()) {
                return "/" + PATH + "/" + formatData + "/" + file.getName();
            }
            return url;
        }
        byte[] bytes = HttpUtil.downloadBytes(url);
        if (bytes.length > 0) {
            return MinIoUtil.upload(bytes, StrUtil.EMPTY);
        }
        return url;
    }
}
