package io.renren.interceptor;

import io.renren.annotation.Dict;
import io.renren.api.recept.dto.DictInfo;
import io.renren.utils.DictUtils;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2023/7/18 16:38
 */
@Component
@Intercepts(
        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class})
)
public class AnnotationInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object resultObject = invocation.proceed();
        if (Objects.isNull(resultObject)) {
            return null;
        }
        if (resultObject instanceof ArrayList) {
            ArrayList resultList = (ArrayList) resultObject;
            if (!resultList.isEmpty()) {
                for (Object result : resultList) {
                    //取出当前类所有字段
                    Field[] declaredFields = result.getClass().getDeclaredFields();
                    for (Field declaredField : declaredFields) {
                        Dict annotation = declaredField.getAnnotation(Dict.class);
                        if (annotation != null) {
                            //取出注解的属性
                            String fieldType = annotation.fieldType();
                            declaredField.setAccessible(true);
                            String object = (String) declaredField.get(result);
                            DictInfo dict = new DictInfo();
                            DictUtils.dictList.stream().filter(dictInfo -> dictInfo.getFieldType().equals(fieldType) && dictInfo.getOriginalValue().equals(object))
                                    .findFirst().ifPresent(p -> dict.setTranscodeValue(p.getTranscodeValue()));
                            declaredField.set(result, dict.getTranscodeValue());
                        }

                    }
                }

            }

        }
        return resultObject;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
