package io.renren.api.report.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目班组信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-31
 */
@Data
@ApiModel(value = "五冶上报班组信息")
public class TeamMasterInfo {

	@ApiModelProperty(value = "项目code")
	@JSONField(serialize = false)
	private String projectCode;

	@ApiModelProperty(value = "企业统一社会信用代码")
	private String companyCode;

	@ApiModelProperty(value = "班组名称")
	private String name;

	@ApiModelProperty(value = "班组长姓名")
	private String leaderName;

	@ApiModelProperty(value = "班组长身份证号")
	private String leaderIdentification;

	@ApiModelProperty(value = "班组长联系电话")
	private String leaderPhone;

	@ApiModelProperty(value = "班组类型（0：建筑工人班组，1：管理人员班组）")
	private String type;

	@ApiModelProperty(value = "五冶返回班组编码，班组上传成功返回的编码，后期支持修改班组信息时，可以用来修改班组信息五冶返回班组编码，班组上传成功返回的编码，后期支持修改班组信息时，可以用来修改班组信息")
	private String groupCode;

	@ApiModelProperty(value = "工种编码")
	private String workTypeCode;

	@JSONField(serialize = false)
	private Long tm0101;
}