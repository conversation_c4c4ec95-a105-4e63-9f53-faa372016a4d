//package io.renren.api.recept.policy;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import io.renren.api.recept.entity.ProjectCompanyInfoEntity;
//import io.renren.api.recept.service.ProjectCompanyInfoService;
//import io.renren.common.constant.Constant;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.Date;
//
/// **
// * 企业数据处理
// *
// * <AUTHOR>
// * @date 2023/7/14 11:06
// */
//@Component
//public class CompanyInfoPolicy implements DataStrategy {
//    @Resource
//    private ProjectCompanyInfoService projectCompanyInfoService;
//    /**
//     * 成功状态
//     */
//    private final static String SUCCESS_STATE = "1";
//
//    @Override
//    public String dataType() {
//        return Constant.COMPANY;
//    }
//
//    @Override
//    public void processData(JSONObject data) {
//        //组合JSON字符串
//        JSONObject jsonObject = data.getJSONObject("cp01");
//        jsonObject.putAll(data.getJSONObject("cp02"));
//        ProjectCompanyInfoEntity projectCompanyInfoEntity = JSON.toJavaObject(jsonObject, ProjectCompanyInfoEntity.class);
//        //查询数据是否存在
//        ProjectCompanyInfoEntity existingEntity = projectCompanyInfoService.selectById(projectCompanyInfoEntity.getCp0201());
//        //判断企业信息是否存在
//        if (existingEntity != null) {
//            // 如果企业信息存在而且已上报成功,则更新数据重新上报
//            projectCompanyInfoEntity.setReportStatus("0");
//            projectCompanyInfoEntity.setUpdateDate(new Date());
//            projectCompanyInfoService.updateById(projectCompanyInfoEntity);
//        } else {
//            // 如果企业信息不存在,则直接插入数据
//            projectCompanyInfoService.insert(projectCompanyInfoEntity);
//        }
//    }
//}
