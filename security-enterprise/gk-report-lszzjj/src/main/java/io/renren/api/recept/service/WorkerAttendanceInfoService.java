package io.renren.api.recept.service;

import io.renren.api.recept.entity.WorkerAttendanceInfoEntity;
import io.renren.api.report.dto.AsyncResultDTO;
import io.renren.api.report.dto.WorkerAttendanceInfo;
import io.renren.common.service.BaseService;

import java.util.List;

/**
 * 人员考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-26
 */
public interface WorkerAttendanceInfoService extends BaseService<WorkerAttendanceInfoEntity> {

    /**
     * 查询待上报考勤记录
     *
     * @return List<WorkerAttendanceInfo>
     */
    List<WorkerAttendanceInfo> selectWorkerAttendanceInfoByReportStatus();

    /**
     * 查询处理中的考勤处理编号
     *
     * @return List<AsyncResultDTO>
     */
    List<AsyncResultDTO> getAsyncRequestCode();

    /**
     * 批量失败该异步查询编号并且处于待处理的考勤
     *
     * @param asyncRequestCode 异步查询code
     * @param message          处理结果消息
     */
    void updateByAsyncRetrun(String asyncRequestCode, String message);

    /**
     * @param ids              kq0201
     * @param reportStatus     上报状态
     * @param errorCause       错误信息
     * @param asyncRequestCode 第三方编码
     */
    void updateBatchByIds(List<Long> ids, String reportStatus, String errorCause, String asyncRequestCode);
}