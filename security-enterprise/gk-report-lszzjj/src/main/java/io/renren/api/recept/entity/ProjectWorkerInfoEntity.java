package io.renren.api.recept.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-31
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("PROJECT_WORKER_INFO")
public class ProjectWorkerInfoEntity implements Serializable{

	private static final long serialVersionUID = 1L;

    /**
     * 用户编号
     */
    @TableId
	@JSONField(name = "ps0201")
	private Long userid;

	/**
	 * 五冶返回工人code
	 */
	private Long usercode;

    /**
     * 项目编号
     */
	private Long pj0101;

    /**
     * 班组编号
     */
	private Long tm0101;

    /**
     * 人员姓名
     */
	private String name;

    /**
     * 身份证号
     */
	@JSONField(name = "idcardnumber")
	private String identification;

    /**
     * 身份证有效期的开始日期，格式 yyyy-MM-dd
     */
	@JSONField(name = "startdate")
	private Date startExpiration;

    /**
     * 身份证有效期的结束日期，格式 yyyy-MM-dd，长期有效取值：2099-12-31
     */
	@JSONField(name = "expirydate")
	private Date endExpiration;

    /**
     * 签发机关
     */
	@JSONField(name = "grantorg")
	private String issuingUnit;

    /**
     * 住址
     */
	private String address;

    /**
     * 民族（dic）
     */
	@JSONField(name = "nation")
	private String ethnic;

    /**
     * 联系电话
     */
	@JSONField(name = "cellphone")
	private String phone;

    /**
     * 身份证图片
     */
	@JSONField(name = "issuecardpicurl")
	private String photo;

    /**
     * 近照，url
     */
	private String recentPhoto;

    /**
     * 身份证复印件正面照，url
     */
	@JSONField(name = "positiveidcardimageurl")
	private String identificationCopyFront;

    /**
     * 身份证复印件反面照，url
     */
	@JSONField(name = "negativeidcardimageurl")
	private String identificationCopyBack;

    /**
     * 政治面貌（dic）
     */
	@JSONField(name = "politicstype")
	private String politicalStatus;

    /**
     * 文化程度(dic)
     */
	@JSONField(name = "cultureleveltype")
	private String degreeName;

    /**
     * 紧急联系人
     */
	@JSONField(name = "urgentlinkman")
	private String urgentContact;

    /**
     * 紧急联系人电话
     */
	@JSONField(name = "urgentlinkmanphone")
	private String urgentTel;

    /**
     * 人员角色(dic)(0:工人，1:管理人员)
     */
	private String postType;

    /**
     * 是否班组长(dic)
     */
	@JSONField(name = "isteamleader")
	private String isLeader;

    /**
     * 工种/岗位编码(dic)
     */
	@JSONField(name = "worktypecode")
	private String workTypeCode;

    /**
     * 进场（也称入职）日期，格式：yyyy-MM-dd
     */
	@JSONField(name = "entrytime")
	private Date enterDate;

    /**
     * 退场（也称离职）日期，进场时，这个值为空；退场时，必填，格式：yyyy-MM-d
     */
	@JSONField(name = "exittime")
	private Date exitDate;

    /**
     * 出生日期
     */
	@JSONField(name = "birthday")
	private Date birthDate;

    /**
     * 血型
     */
	private String bloodType;

    /**
     * 银行卡号
     */
	@JSONField(name = "payrollbankcardnumber")
	private String bankCode;

    /**
     * 银行名称
     */
	@JSONField(name = "payrollbankname")
	private String bankName;

    /**
     * 上报状态(0待上报,1上报成功,2上报失败)
     */
	private String reportStatus;

    /**
     * 错误原因
     */
	private String errorCause;

    /**
     * 上报时间
     */
	private Date reportDate;

	/**
     * 创建时间
     */
	private Date createDate;

	/**
	 * 更新时间
	 */
	private Date updateDate;
}