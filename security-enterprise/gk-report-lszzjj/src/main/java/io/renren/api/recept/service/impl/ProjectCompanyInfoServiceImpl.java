package io.renren.api.recept.service.impl;

import io.renren.api.recept.dao.ProjectCompanyInfoDao;
import io.renren.api.recept.entity.ProjectCompanyInfoEntity;
import io.renren.api.recept.service.ProjectCompanyInfoService;
import io.renren.api.report.dto.CompanyInfo;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/18 10:12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ProjectCompanyInfoServiceImpl extends BaseServiceImpl<ProjectCompanyInfoDao, ProjectCompanyInfoEntity> implements ProjectCompanyInfoService {
    @Override
    public List<CompanyInfo> selectCompanyInfoByReportStatus() {
        return baseDao.selectCompanyInfoByReportStatus();
    }

    @Override
    public String getCorpType(Long cp0201) {
        return baseDao.getCorpType(cp0201);
    }
}
