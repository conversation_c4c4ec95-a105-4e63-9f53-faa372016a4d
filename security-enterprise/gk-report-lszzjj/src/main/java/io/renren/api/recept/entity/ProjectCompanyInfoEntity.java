package io.renren.api.recept.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/7/18 9:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PROJECT_COMPANY_INFO")
public class ProjectCompanyInfoEntity {

    /**
     * 主键ID
     */
    @TableId
    private Long cp0201;

    /**
     * 项目ID
     */
    private Long pj0101;

    /**
     * 企业名称
     */
    @JSONField(name = "corpname")
    private String name;

    /**
     * 统一社会信用代码
     */
    @JSONField(name = "corpcode")
    private String code;

    /**
     * 参建类型
     */
    @JSONField(name = "corptype")
    private String type;

    /**
     * 经营范围
     */
    @JSONField(name = "entscope")
    private String businessScope;

    /**
     * 注册日期(yyyy-MM-dd)
     */
    @JSONField(name = "establishdate")
    private Date registerDate;

    /**
     * 成立日期(yyyy-MM-dd)
     */
    @JSONField(name = "establishdate")
    private Date establishDate;

    /**
     * 法定代表人
     */
    @JSONField(name = "legalman")
    private String legalMan;

    /**
     * 法定代表人身份证号码
     */
    @JSONField(name = "legalmanidcardnumber")
    private String legalManIdCardNumber;

    /**
     * 法定代表人联系电话
     */
    @JSONField(name = "linkcellphone")
    private String legalManPhone;

    /**
     * 注册资本(万元)
     */
    @JSONField(name = "regcapital")
    private BigDecimal regCapital;

    /**
     * 实收资本(万元)
     */
    private BigDecimal factRegCapital;

    /**
     * 营业地址
     */
    private String address;

    /**
     * 邮政编码
     */
    @JSONField(name = "zipcode")
    private String zipCode;

    /**
     * 企业联系电话
     */
    @JSONField(name = "officephone")
    private String officePhone;

    /**
     * 联系人姓名
     */
    @JSONField(name = "linkman")
    private String linkMan;

    /**
     * 联系人办公电话
     */
    @JSONField(name = "linkphone")
    private String linkPhone;

    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 进场日期
     */
    @JSONField(name = "entrytime")
    private Date enterDate;

    /**
     * 退场日期
     */
    @JSONField(name = "exittime")
    private Date exitDate;

    /**
     * 上报状态(0待上报,1上报成功,2上报失败)
     */
    private String reportStatus;

    /**
     * 第三方项目编码
     */
    private String companyCode;

    /**
     * 错误原因
     */
    private String errorCause;

    /**
     * 上报时间
     */
    private Date reportDate;

    /**
     * 更新时间
     */
    private Date updateDate;

}
