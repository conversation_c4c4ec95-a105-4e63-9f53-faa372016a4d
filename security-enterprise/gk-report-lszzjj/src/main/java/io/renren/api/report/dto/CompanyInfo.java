package io.renren.api.report.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/7/14 13:08
 */
@Data
@ApiModel(value = "五冶上报企业信息")
public class CompanyInfo {

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "企业名称")
    private String name;

    @ApiModelProperty(value = "统一社会信用代码")
    private String code;

    @ApiModelProperty(value = "参建类型")
    @Dict(fieldType = "CORPTYPE")
    private String type;

    @ApiModelProperty(value = "经营范围")
    private String businessScope;

    @ApiModelProperty(value = "注册日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date registerDate;

    @ApiModelProperty(value = "成立日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date establishDate;

    @ApiModelProperty(value = "法定代表人")
    private String legalMan;

    @ApiModelProperty(value = "法定代表人身份证号码")
    private String legalManIdCardNumber;

    @ApiModelProperty(value = "法定代表人联系电话")
    private String legalManPhone;

    @ApiModelProperty(value = "注册资本")
    private BigDecimal regCapital;

    @ApiModelProperty(value = "实收资本")
    private BigDecimal factRegCapital;

    @ApiModelProperty(value = "营业地址")
    private String address;

    @ApiModelProperty(value = "邮政编码")
    private String zipCode;

    @ApiModelProperty(value = "企业联系电话")
    private String officePhone;

    @ApiModelProperty(value = "联系人姓名")
    private String linkMan;

    @ApiModelProperty(value = "联系人办公电话")
    private String linkPhone;

    @ApiModelProperty(value = "企业邮箱")
    private String email;

    @ApiModelProperty(value = "进场日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date enterDate;

    @ApiModelProperty(value = "退场日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date exitDate;

    @ApiModelProperty(value = "五冶返回编码")
    private String companyCode;

    @JSONField(serialize = false)
    private Long cp0201;
}
