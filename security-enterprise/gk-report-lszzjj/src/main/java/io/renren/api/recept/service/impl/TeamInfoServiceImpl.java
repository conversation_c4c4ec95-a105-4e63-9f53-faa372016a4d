package io.renren.api.recept.service.impl;

import io.renren.api.recept.dao.TeamMasterInfoDao;
import io.renren.api.recept.entity.TeamMasterInfoEntity;
import io.renren.api.recept.service.TeamInfoService;
import io.renren.api.report.dto.CompanyInfo;
import io.renren.api.report.dto.TeamMasterInfo;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @date: 2023/7/26 15:54
 * @author: hanchaofei
 */
@Service
public class TeamInfoServiceImpl extends BaseServiceImpl<TeamMasterInfoDao, TeamMasterInfoEntity> implements TeamInfoService {

    @Override
    public TeamMasterInfoEntity selectByPj0101(Long pj0101, Long cp0201) {
        return baseDao.selectByPj0101(pj0101, cp0201);
    }

    @Override
    public List<TeamMasterInfo> selectTeamInfoByReportStatus() {
        return baseDao.selectTeamInfoByReportStatus();
    }
}
