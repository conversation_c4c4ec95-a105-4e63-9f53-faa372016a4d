package io.renren.api.recept.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-6-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("YC_DV01")
public class Dv01Entity {

    /**
     * 主键ID
     */
    @TableId
    private Long dv0101;

    /**
     * 项目ID
     */
    private Long pj0101;

    /**
     * 设备序列号
     */
    private String sn;

    /**
     * 上报状态
     */
    private String state;

    /**
     * 返回结果
     */
    private String msg;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 上报时间
     */
    private Date reportDate;

}
