package io.renren.api.recept.dao;

import io.renren.api.recept.entity.ProjectCompanyInfoEntity;
import io.renren.api.report.dto.CompanyInfo;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/18 9:39
 */
@Mapper
public interface ProjectCompanyInfoDao extends BaseDao<ProjectCompanyInfoEntity> {
    /**
     * 查询待上报的企业信息
     *
     * @return List<CompanyInfo>
     */
    List<CompanyInfo> selectCompanyInfoByReportStatus();

    /**
     * 查询参建单位类型
     * @param cp0201 参建单位
     * @return
     */
    String getCorpType(Long cp0201);
}
