package io.renren.api.recept.policy;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import io.renren.common.constant.Constant;
import io.renren.utils.TokenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 扬尘数据处理
 *
 * <AUTHOR>
 * @date 2023/7/14 11:12
 */
@Component
public class FugitiveDustInfoPolicy implements DataStrategy {

    @Resource
    private TokenUtils tokenUtils;

    @Value("${lszjj.loginId}")
    private String loginId;

    @Value("${lszjj.httpUrl}")
    private String baseUrl;

    private static final String DUST_DATA_PATH = "/api/dust/saveProjectDustData";
    private static final String CONTENT_TYPE_JSON = "application/json";
    private static final Logger logger = LoggerFactory.getLogger(FugitiveDustInfoPolicy.class);

    /**
     * 字段映射关系
     */
    private static final Map<String, String> FIELD_MAP = new HashMap<>();

    static {
        FIELD_MAP.put("sn", "sn");
        FIELD_MAP.put("pm10value", "pm10");
        FIELD_MAP.put("pm25value", "pm2");
        FIELD_MAP.put("voice", "noise");
        FIELD_MAP.put("temperature", "temperature");
        FIELD_MAP.put("humidity", "humidity");
        FIELD_MAP.put("windspeed", "windSpeed");
        FIELD_MAP.put("winddirection", "windDirection");
    }

    @Override
    public String dataType() {
        return Constant.DUST;
    }

    @Override
    public void processData(JSONObject data) {
        if (data == null) {
            logger.warn("接收到的扬尘数据为null,结束上报");
            return;
        }
        try {
            JSONObject bodyParams = new JSONObject();
            bodyParams.put("loginId", loginId);
            JSONObject dataObject = data.getJSONObject("data");
            if (dataObject == null) {
                logger.warn("扬尘数据中data字段为null,结束上报");
                return;
            }
            // 记录缺失字段
            StringBuilder missingFields = new StringBuilder();
            for (Map.Entry<String, String> entry : FIELD_MAP.entrySet()) {
                String sourceKey = entry.getKey();
                String targetKey = entry.getValue();
                if (dataObject.containsKey(sourceKey)) {
                    Object value = dataObject.get(sourceKey);
                    if (value != null) {
                        bodyParams.put(targetKey, value.toString());
                    }
                } else {
                    missingFields.append(sourceKey).append(",");
                }
            }
            if (missingFields.length() > 0) {
                logger.warn("未找到对应的数据字段: {}", missingFields.toString().trim());
            }
            // 上报扬尘数据
            String response;
            try (HttpResponse httpResponse = HttpRequest.post(baseUrl + DUST_DATA_PATH)
                    .header("Content-Type", CONTENT_TYPE_JSON)
                    .header("accessToken", tokenUtils.getToken())
                    .body(bodyParams.toJSONString())
                    .timeout(60000)
                    .execute()) {
                response = httpResponse.body();
            }
            logger.info("上报扬尘数据:{},返回结果:{}", bodyParams.toJSONString(), response);
        } catch (Exception e) {
            logger.error("扬尘数据上报失败,{}", data.toJSONString(), e);
        }
    }
}