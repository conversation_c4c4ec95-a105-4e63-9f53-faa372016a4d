package io.renren.api.recept.service;

import io.renren.api.recept.entity.ProjectWorkerInfoEntity;
import io.renren.api.report.dto.WorkerAttendanceInfo;
import io.renren.api.report.dto.WorkerInfo;
import io.renren.common.service.BaseService;

import java.util.List;

/**
 * 项目人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-26
 */
public interface ProjectWorkerInfoService extends BaseService<ProjectWorkerInfoEntity> {

    /**
     * 查询待上报工人
     * @return List<WorkerInfo>
     */
    List<WorkerInfo> selectWorkerInfoByReportStatus();

}