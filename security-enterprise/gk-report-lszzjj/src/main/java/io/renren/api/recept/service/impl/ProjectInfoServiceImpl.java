package io.renren.api.recept.service.impl;

import io.renren.api.recept.dao.ProjectInfoDao;
import io.renren.api.recept.entity.ProjectInfoEntity;
import io.renren.api.recept.service.ProjectInfoService;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2023/7/18 10:05
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ProjectInfoServiceImpl extends BaseServiceImpl<ProjectInfoDao, ProjectInfoEntity> implements ProjectInfoService {
}
