package io.renren.api.recept.policy;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/14 11:15
 */
@Component
public class ReceptionDataFactory {
    private final Map<String, DataStrategy> dataTypeMap = new HashMap<>();

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        final Map<String, DataStrategy> map = applicationContext.getBeansOfType(DataStrategy.class);
        map.values().forEach(source -> dataTypeMap.put(source.dataType(), source));
    }

    public void calculate(String dataType, JSONObject data) {
        DataStrategy targetDataStrategy = dataTypeMap.get(dataType);
        if (targetDataStrategy != null) {
            targetDataStrategy.processData(data);
        }
    }
}
