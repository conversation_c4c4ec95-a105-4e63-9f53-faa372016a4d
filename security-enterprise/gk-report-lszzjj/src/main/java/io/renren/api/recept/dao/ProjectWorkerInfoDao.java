package io.renren.api.recept.dao;

import io.renren.api.recept.entity.ProjectWorkerInfoEntity;
import io.renren.api.report.dto.WorkerAttendanceInfo;
import io.renren.api.report.dto.WorkerInfo;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-26
 */
@Mapper
public interface ProjectWorkerInfoDao extends BaseDao<ProjectWorkerInfoEntity> {

    /**
     * 待上报工人查询
     * @return List<WorkerInfo>
     */
    List<WorkerInfo> selectWorkerInfoByReportStatus();
}