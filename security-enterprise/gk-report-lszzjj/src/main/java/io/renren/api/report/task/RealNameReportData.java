//package io.renren.api.report.task;
//
//import io.renren.api.recept.service.ProjectCompanyInfoService;
//import io.renren.api.recept.service.ProjectWorkerInfoService;
//import io.renren.api.recept.service.TeamInfoService;
//import io.renren.api.recept.service.WorkerAttendanceInfoService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * 数据上报定时任务
// *
// * <AUTHOR>
// * @date 2023/7/18 15:56
// */
//@Component
//public class RealNameReportData {
//    /**
//     * 日志文件
//     */
//    private static final Logger logger = LoggerFactory.getLogger(RealNameReportData.class);
//
//    @Resource
//    private ProjectCompanyInfoService projectCompanyInfoService;
//
//    @Resource
//    private TeamInfoService teamInfoService;
//
//    @Resource
//    private ProjectWorkerInfoService projectWorkerInfoService;
//
//    @Resource
//    private WorkerAttendanceInfoService workerAttendanceInfoService;
//
//    @Value("${lszjj.httpUrl}")
//    private String url;
//    /**
//     * 接口成功状态
//     */
//    private static final String SUCCEED_STATE = "0";
//    /**
//     * 数据成功状态
//     */
//    private static final String DATA_SUCCEED_STATE = "1";
//    /**
//     * 数据失败状态
//     */
//    private static final String DATA_FAILURE_STATE = "2";
//    /**
//     * 状态码
//     */
//    private static final String CODE = "code";
//    /**
//     * 请求超时时间(单位毫秒)
//     */
//    private static final int HTTP_TIME_OUT = 60000;

//    /**
//     * 上报参建单位信息
//     */
//    @Scheduled(cron = "*/5 * * * * ?")
//    public void reportCompanyInfo() {
//        String httpUrl = url + "/sync/trd/standard/v1/uploadCompany";
//        List<CompanyInfo> list = projectCompanyInfoService.selectCompanyInfoByReportStatus();
//        for (CompanyInfo companyInfo : list) {
//            ProjectCompanyInfoEntity entity = new ProjectCompanyInfoEntity();
//            logger.info("开始上报参建单位{}", companyInfo.getCp0201());
//            entity.setCp0201(companyInfo.getCp0201());
//            entity.setReportDate(new Date());
//            try {
//                //上报数据
//                Map<String, Object> signature = EncryptUtils.signature(companyInfo.getProjectCode(),
//                        JSONObject.toJSONString(companyInfo));
//                String result = HttpUtil.post(httpUrl, new JSONObject(signature).toJSONString(), HTTP_TIME_OUT);
//                JSONObject responseResult = JSONObject.parseObject(result);
//                //成功
//                if (SUCCEED_STATE.equals(responseResult.getString(CODE))) {
//                    //更新上报状态和第三方编码
//                    entity.setReportStatus(DATA_SUCCEED_STATE);
//                    if (responseResult.get("data") != null) {
//                        entity.setCompanyCode(String.valueOf(responseResult.get("data")));
//                    }
//                } else {
//                    //更新上报状态,记录返回的错误信息
//                    entity.setReportStatus(DATA_FAILURE_STATE);
//                    entity.setErrorCause(result);
//                }
//            } catch (Exception e) {
//                entity.setReportStatus(DATA_FAILURE_STATE);
//                entity.setErrorCause("上报数据发生异常,请检查错误日志");
//                logger.error("企业信息上报出现异常,异常信息:" + e.getMessage());
//            } finally {
//                projectCompanyInfoService.updateById(entity);
//                try {
//                    Thread.sleep(2000);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    }
//
//    /**
//     * 上报项目班组
//     */
//    @Scheduled(cron = "*/7 * * * * ?")
//    public void reportTeamInfo() {
//        String httpUrl = url + "/sync/trd/standard/v1/uploadGroup";
//        List<TeamMasterInfo> list = teamInfoService.selectTeamInfoByReportStatus();
//        for (TeamMasterInfo teamMasterInfo : list) {
//            TeamMasterInfoEntity entity = new TeamMasterInfoEntity();
//            logger.info("开始上报班组{}", teamMasterInfo.getTm0101());
//            entity.setTm0101(teamMasterInfo.getTm0101());
//            entity.setReportDate(new Date());
//            try {
//                Map<String, Object> signature = EncryptUtils.signature(teamMasterInfo.getProjectCode(), JSONObject.toJSONString(teamMasterInfo));
//                String result = HttpUtil.post(httpUrl, new JSONObject(signature).toJSONString(), HTTP_TIME_OUT);
//                JSONObject responseResult = JSONObject.parseObject(result);
//                //成功
//                if (SUCCEED_STATE.equals(responseResult.getString(CODE))) {
//                    //更新上报状态和第三方编码
//                    entity.setReportStatus(DATA_SUCCEED_STATE);
//                    if (responseResult.get("data") != null) {
//                        entity.setGroupCode(String.valueOf(responseResult.get("data")));
//                    }
//                } else {
//                    //更新上报状态,记录返回的错误信息
//                    entity.setReportStatus(DATA_FAILURE_STATE);
//                    entity.setErrorCause(result);
//                }
//            } catch (Exception e) {
//                entity.setReportStatus(DATA_FAILURE_STATE);
//                entity.setErrorCause("上报数据发生异常,请检查错误日志");
//                logger.error("班组信息上报出现异常,异常信息:" + e.getMessage());
//            } finally {
//                teamInfoService.updateById(entity);
//                try {
//                    Thread.sleep(2000);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    }
//
//    /**
//     * 上报项目工人
//     */
//    @Scheduled(cron = "*/9 * * * * ?")
//    public void reportWorkerInfo() {
//        String httpUrl = url + "/sync/trd/standard/v1/uploadWorker";
//        //查询上报工人
//        List<WorkerInfo> list = projectWorkerInfoService.selectWorkerInfoByReportStatus();
//        for (WorkerInfo workerInfo : list) {
//            ProjectWorkerInfoEntity entity = new ProjectWorkerInfoEntity();
//            entity.setErrorCause("");
//            logger.info("开始上报班组{}", workerInfo.getUserid());
//            entity.setUserid(workerInfo.getUserid());
//            entity.setReportDate(new Date());
//            try {
//                String string = JSONObject.toJSONString(workerInfo);
//                Map<String, Object> signature = EncryptUtils.signature(workerInfo.getProjectCode(), string);
//                String result = HttpUtil.post(httpUrl, new JSONObject(signature).toJSONString(), HTTP_TIME_OUT);
//                JSONObject responseResult = JSONObject.parseObject(result);
//                //成功
//                if (SUCCEED_STATE.equals(responseResult.getString(CODE))) {
//                    //更新上报状态和第三方编码
//                    entity.setUsercode(Long.valueOf(responseResult.getString("data")));
//                    entity.setReportStatus(DATA_SUCCEED_STATE);
//                } else {
//                    //记录错误信息和更新上报状态
//                    entity.setReportStatus(DATA_FAILURE_STATE);
//                    entity.setErrorCause(result);
//                }
//            } catch (Exception e) {
//                entity.setReportStatus(DATA_FAILURE_STATE);
//                entity.setErrorCause("工人上报出现异常,请检查错误日志");
//                logger.error("工人信息上报出现异常,异常信息:" + e.getMessage());
//            } finally {
//                projectWorkerInfoService.updateById(entity);
//                try {
//                    Thread.sleep(2000);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    }
//
//    /**
//     * 上报项目人员考勤
//     */
//    @Scheduled(cron = "*/11 * * * * ?")
//    public void reportAttendanceInfo() {
//        String httpUrl = url + "/sync/trd/standard/v1/uploadAttendances";
//        //查询50条待上报的考勤
//        List<WorkerAttendanceInfo> list = workerAttendanceInfoService.selectWorkerAttendanceInfoByReportStatus();
//        //数据进行分组
//        Map<String, List<WorkerAttendanceInfo>> collect = list.stream().collect(Collectors.groupingBy(WorkerAttendanceInfo::getProjectCode));
//        for (String projectCode : collect.keySet()) {
//            String reportStatus = DATA_SUCCEED_STATE;
//            String errorCause = "";
//            String asyncRequestCode = "";
//            List<Long> ids = new ArrayList<>();
//            try {
//                List<WorkerAttendanceInfo> reportAttInfo = collect.get(projectCode);
//                logger.info("开始上报考勤数据{}：", reportAttInfo);
//                Map<String, Object> signature = EncryptUtils.signature(projectCode,
//                        JSONObject.toJSONString(reportAttInfo));
//                //上报数据
//                String result = HttpUtil.post(httpUrl, new JSONObject(signature).toJSONString(), HTTP_TIME_OUT);
//                JSONObject responseResult = JSONObject.parseObject(result);
//                ids = reportAttInfo.stream().map(WorkerAttendanceInfo::getKq0201).collect(Collectors.toList());
//                if (SUCCEED_STATE.equals(responseResult.getString(CODE))) {
//                    //更新上报状态和第三方异步查询编码，考勤需要二次查询是否上报成功，1为待处理
//                    reportStatus = DATA_SUCCEED_STATE;
//                    if (responseResult.getJSONObject("data") != null) {
//                        asyncRequestCode = (String.valueOf(responseResult.getJSONObject("data").get("asyncRequestCode")));
//                    }
//                } else {
//                    //记录错误信息和更新项目状态
//                    reportStatus = "3";
//                    errorCause = result;
//                }
//            } catch (Exception e) {
//                reportStatus = DATA_FAILURE_STATE;
//                errorCause = "考勤上报出现异常,请检查错误日志";
//                logger.error("考勤出现异常" + e.getMessage());
//            } finally {
//                workerAttendanceInfoService.updateBatchByIds(ids, reportStatus, errorCause, asyncRequestCode);
//                try {
//                    Thread.sleep(2000);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    }
//
//    /**
//     * 查询项目人员考勤上报状态
//     */
//    @Scheduled(cron = "*/20 * * * * ?")
//    public void queryAttendanceInfoResult() {
//        String httpUrl = url + "/sync/trd/standard/v1/queryAsyncResult";
//        //查询一条待查询记录
//        List<AsyncResultDTO> asyncResultList = workerAttendanceInfoService.getAsyncRequestCode();
//        WorkerAttendanceInfoEntity workerAttendanceInfoEntity = new WorkerAttendanceInfoEntity();
//        try {
//            for (AsyncResultDTO asyncResultDTO : asyncResultList) {
//                logger.info("开始查询考勤处理情况{}：", asyncResultDTO.getAsyncRequestCode());
//                Map<String, Object> signature = EncryptUtils.signature(asyncResultDTO.getProjectId(), JSONObject.toJSONString(asyncResultDTO));
//                String result = HttpUtil.post(httpUrl, new JSONObject(signature).toJSONString(),HTTP_TIME_OUT);
//                JSONObject responseResult = JSONObject.parseObject(result);
//                //返回成功处理数据
//                try {
//                    Thread.sleep(2000);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//                if (SUCCEED_STATE.equals(responseResult.getString(CODE))) {
//                    List<AsyncResultDTO> list = JSONObject.parseArray(responseResult.getJSONArray("data").toJSONString(), AsyncResultDTO.class);
//                    for (AsyncResultDTO entity : list) {
//                        //判断是否存在考勤定义编号，如果没有中断
//                        if (entity.getCurNumber() == null) {
//                            continue;
//                        }
//                        //更新处理结果
//                        workerAttendanceInfoEntity.setKq0201(entity.getCurNumber());
//                        workerAttendanceInfoEntity.setErrorCause(entity.getMessage());
//                        workerAttendanceInfoEntity.setUpdateDate(new Date());
//                        if ("1".equals(entity.getSuccess())) {
//                            //成功
//                            workerAttendanceInfoEntity.setReportStatus("2");
//                        } else {
//                            //失败
//                            workerAttendanceInfoEntity.setReportStatus("3");
//                        }
//                        workerAttendanceInfoService.updateById(workerAttendanceInfoEntity);
//                    }
//                    //因为返回结果会出现不返回上报考勤编号的情况（即上报50条最后返回40条结果，所以需要把未返回的结果失败处理）
//                    //处理该异步查询中没有返回的考勤
//                    workerAttendanceInfoService.updateByAsyncRetrun(
//                            String.valueOf(asyncResultDTO.getAsyncRequestCode()),
//                            String.valueOf(responseResult.get("message")));
//                } else {
//                    //批量处理失败并且处于处理中的考勤记录，记录错误原因
//                    workerAttendanceInfoService.updateByAsyncRetrun(
//                            String.valueOf(responseResult.get("asyncRequestCode")),
//                            String.valueOf(responseResult.get("message")));
//                    logger.error("人员考勤信息查询结果失败,返回结果:" + result);
//                }
//            }
//        } catch (Exception e) {
//            logger.error("人员考勤信息查询出现异常,异常信息:" + e.getMessage());
//            throw e;
//        }
//    }
//}
