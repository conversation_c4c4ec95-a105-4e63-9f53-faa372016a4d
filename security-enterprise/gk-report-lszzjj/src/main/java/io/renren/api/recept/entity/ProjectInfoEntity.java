package io.renren.api.recept.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PROJECT_INFO")
public class ProjectInfoEntity implements Serializable {
    private static final long serialVersionUID = 5853391376262952990L;

    /**
     * 主键id
     */
    @TableId
    private Long pj0101;
    /**
     * 项目名称
     */
    private String name;
    /**
     * 项目简介
     */
    private String description;
    /**
     * 所属行业
     */
    private String industry;
    /**
     * 项目类别
     */
    private String category;
    /**
     * 建设性质
     */
    private String constructtype;
    /**
     * 投资类型
     */
    private String investtype;
    /**
     * 项目所在地
     */
    private String areacode;
    /**
     * 建设地址（项目的详细地址具体到XX街道XX号）
     */
    private String address;
    /**
     * 总面积(平方米)
     */
    private BigDecimal buildingarea;
    /**
     * 总长度(米)
     */
    private BigDecimal buildinglength;
    /**
     * 总投资(万元)
     */
    private BigDecimal invest;
    /**
     * 工程造价(万元)
     */
    private BigDecimal engineering;
    /**
     * 项目规模
     */
    private String scale;
    /**
     * 开工日期
     */
    private Date startdate;
    /**
     * 竣工日期
     */
    private Date completeDate;
    /**
     * 经度
     */
    private BigDecimal lng;
    /**
     * 纬度
     */
    private BigDecimal lat;
    /**
     * 联系人姓名
     */
    private String linkman;
    /**
     * 联系人电话
     */
    private String linkphone;
    /**
     * 是否重点项目
     */
    private String ismajorProject;
    /**
     * 是否缴纳保证金
     */
    private String isdeposit;
    /**
     * 项目状态
     */
    private String prjstatus;
    /**
     * 电子围栏区域
     */
    private String region;
    /**
     * 第三方编码
     */
    private String projectCode;
    /**
     * 创建时间
     */
    private Date createDate;
}