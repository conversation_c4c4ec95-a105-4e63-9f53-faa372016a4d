package io.renren.api.recept.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.api.recept.dao.Dv01Dao;
import io.renren.api.recept.entity.Dv01Entity;
import io.renren.api.recept.service.Dv01Service;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/18 10:12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Dv01ServiceImpl extends BaseServiceImpl<Dv01Dao, Dv01Entity> implements Dv01Service {

    @Override
    public List<Dv01Entity> selectByStateList() {
        return baseDao.selectList(new QueryWrapper<Dv01Entity>().eq("state", "0"));
    }
}
