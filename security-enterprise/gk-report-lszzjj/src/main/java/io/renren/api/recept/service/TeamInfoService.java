package io.renren.api.recept.service;

import io.renren.api.recept.entity.TeamMasterInfoEntity;
import io.renren.api.report.dto.CompanyInfo;
import io.renren.api.report.dto.TeamMasterInfo;
import io.renren.common.service.BaseService;

import java.util.List;

/**
 * @description:
 * @date: 2023/7/26 15:54
 * @author: hanchaofei
 */
public interface TeamInfoService extends BaseService<TeamMasterInfoEntity> {

    /**
     * 根据项目pj0101查询班组信息
     * @param pj0101 项目编号
     * @param cp0201 参建单位
     * @return TeamMasterInfoEntity
     */
    TeamMasterInfoEntity selectByPj0101(Long pj0101, Long cp0201);

    /**
     * 查询待上报班组
     * @return List<TeamMasterInfo>
     */
    List<TeamMasterInfo> selectTeamInfoByReportStatus();
}
