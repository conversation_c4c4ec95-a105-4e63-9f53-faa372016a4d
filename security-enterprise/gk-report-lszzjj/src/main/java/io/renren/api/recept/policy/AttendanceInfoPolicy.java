//package io.renren.api.recept.policy;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import io.renren.api.recept.entity.WorkerAttendanceInfoEntity;
//import io.renren.api.recept.service.WorkerAttendanceInfoService;
//import io.renren.common.constant.Constant;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//
/// **
// * 考勤数据处理
// * <AUTHOR>
// * @date 2023/7/14 11:12
// */
//@Component
//public class AttendanceInfoPolicy implements DataStrategy {
//
//    @Autowired
//    private WorkerAttendanceInfoService workerAttendanceInfoService;
//
//    /**
//     * 处理中
//     */
//    private final static String PROCESSING_STATUS = "1";
//    /**
//     * 成功状态
//     */
//    private final static String SUCCESS_STATE = "2";
//    /**
//     * 失败状态
//     */
//    private final static String FAIL_STATE = "3";
//
//    @Override
//    public String dataType() {
//        return Constant.ATTENDANCE;
//    }
//
//    @Override
//    public void processData(JSONObject data) {
//        //组合JSON字符串
//        JSONObject jsonObject = data.getJSONObject("kq02");
//        //查询数据是否存在
//        WorkerAttendanceInfoEntity workerAttendanceInfoEntity = JSON.toJavaObject(jsonObject, WorkerAttendanceInfoEntity.class);
//        WorkerAttendanceInfoEntity existingEntity = workerAttendanceInfoService.selectById(workerAttendanceInfoEntity.getKq0201());
//        //查询考勤是否存在
//        if(existingEntity != null){
//            // 如果考勤信息存在而且已上报失败,则更新数据重新上报
//            if (!SUCCESS_STATE.equals(existingEntity.getReportStatus()) &&
//                    !PROCESSING_STATUS.equals(existingEntity.getReportStatus())) {
//                workerAttendanceInfoEntity.setReportStatus("0");
//                workerAttendanceInfoEntity.setUpdateDate(new Date());
//                workerAttendanceInfoService.updateById(workerAttendanceInfoEntity);
//            }
//        }else {
//            //如果班组信息不存在，则直接插入数据
//            workerAttendanceInfoService.insert(workerAttendanceInfoEntity);
//        }
//    }
//}
