package io.renren.api.report.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-31
 */
@Data
@ApiModel(value = "五冶上报考勤信息")
public class WorkerAttendanceInfo {

	@ApiModelProperty(value = "项目编码")
	@JSONField(serialize = false)
	private String projectCode;

	@ApiModelProperty(value = "考勤编号")
	@JSONField(serialize = false)
	private Long kq0201;

	@ApiModelProperty(value = "身份证号")
	private String identification;

	@ApiModelProperty(value = "刷卡时间，格式：yyyy-MM-ddHH:mm:ss")
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private Date swipeTime;

	@ApiModelProperty(value = "考勤方向，1：入场，0：出场(dic)")
	@Dict(fieldType = "NOTE")
	private String swipeType;

	@ApiModelProperty(value = "刷卡照片，url")
	private String image;

	@ApiModelProperty(value = "通行方式(dic)")
	@Dict(fieldType = "ATTENDTYPE")
	private String attendType;

	@ApiModelProperty(value = "考勤编号。当前数据在数组中的唯一标识，响应结果会返回")
	private String curnumber;

	@ApiModelProperty(value = "设备编码")
	private String deviceCode;

	@ApiModelProperty(value = "刷卡地点")
	private String workPointName;

	@ApiModelProperty(value = "体温")
	private String temperature;

	@ApiModelProperty(value = "异步查询code")
	@JSONField(serialize = false)
	private String asyncRequestCode;
}