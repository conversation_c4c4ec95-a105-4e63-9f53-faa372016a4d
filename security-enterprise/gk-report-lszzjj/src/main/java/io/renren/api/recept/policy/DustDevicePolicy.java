package io.renren.api.recept.policy;

import com.alibaba.fastjson.JSONObject;
import io.renren.api.recept.dao.Dv01Dao;
import io.renren.api.recept.entity.Dv01Entity;
import io.renren.common.constant.Constant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 扬尘设备数据处理策略
 *
 * <AUTHOR>
 * @date 2025/6/23 8:51
 */
@Component
public class DustDevicePolicy implements DataStrategy {

    @Resource
    private Dv01Dao dv01Dao;

    private static final String DEFAULT_STATE = "0";
    private static final Logger logger = LoggerFactory.getLogger(DustDevicePolicy.class);

    @Override
    public String dataType() {
        return Constant.DUST_DEVICE;
    }

    @Override
    public void processData(JSONObject data) {
        if (data == null) {
            logger.warn("接收到的设备数据为null,结束处理");
            return;
        }
        try {
            JSONObject device = data.getJSONObject("device");
            if (device == null) {
                logger.warn("设备数据中device字段为null,结束处理");
                return;
            }
            String sn = device.getString("sn");
            Long pj0101 = device.getLong("pj0101");
            if (!isValidDeviceData(sn, pj0101)) {
                logger.error("设备数据验证失败: sn={}, pj0101={}", sn, pj0101);
                return;
            }
            Dv01Entity dv01Entity = buildDv01Entity(sn, pj0101);
            dv01Dao.insert(dv01Entity);
            logger.info("扬尘设备数据写入成功: sn={}, pj0101={}", sn, pj0101);
        } catch (Exception e) {
            logger.error("扬尘设备数据写入失败: {}", data.toJSONString(), e);
        }
    }

    /**
     * 验证设备数据是否有效
     */
    private boolean isValidDeviceData(String sn, Long pj0101) {
        return sn != null && !sn.trim().isEmpty() && pj0101 != null && pj0101 >= 0;
    }

    /**
     * 构建Dv01Entity对象
     */
    private Dv01Entity buildDv01Entity(String sn, Long pj0101) {
        Dv01Entity dv01Entity = new Dv01Entity();
        dv01Entity.setSn(sn);
        dv01Entity.setPj0101(pj0101);
        dv01Entity.setState(DEFAULT_STATE);
        dv01Entity.setCreateDate(new Date());
        return dv01Entity;
    }
}