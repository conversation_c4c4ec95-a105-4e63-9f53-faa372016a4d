package io.renren.api.report.task;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import io.renren.api.recept.entity.Dv01Entity;
import io.renren.api.recept.service.Dv01Service;
import io.renren.utils.TokenUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 数据上报定时任务
 *
 * <AUTHOR>
 * @date 2023/7/18 15:56
 */
@Component
public class DustReportData {

    private static final Logger logger = LoggerFactory.getLogger(DustReportData.class);

    @Resource
    private Dv01Service dv01Service;
    @Resource
    private TokenUtils tokenUtils;

    @Value("${lszjj.httpUrl}")
    private String url;

    @Value("${lszjj.loginId}")
    private String loginId;

    private static final String DUST_DATA_PATH = "/api/dust/saveProjectDust";
    private static final String DATA_SUCCEED_STATE = "1";
    private static final String DATA_FAILURE_STATE = "2";
    private static final String CODE = "code";
    private static final int HTTP_TIME_OUT = 60000;
    private static final String CONTENT_TYPE_JSON = "application/json";
    private static final int SUCCESS_CODE = 200;

    /**
     * 扬尘设备上报
     */
    @Scheduled(cron = "*/5 * * * * ?")
    public void queryDustDeviceInfoResult() {
        List<Dv01Entity> list = dv01Service.selectByStateList();
        if (list.isEmpty()) {
            logger.debug("没有需要上报的设备数据");
            return;
        }
        String token = tokenUtils.getToken();
        Date reportDate = new Date();
        List<Dv01Entity> updateList = new ArrayList<>();
        for (Dv01Entity entity : list) {
            processDeviceEntity(entity, token, reportDate, updateList);
        }
        // 批量更新数据库
        if (!updateList.isEmpty()) {
            dv01Service.updateBatchById(updateList);
            logger.info("批量更新设备数据完成，共处理 {} 条记录", updateList.size());
        }
    }

    /**
     * 处理单个设备实体
     */
    private void processDeviceEntity(Dv01Entity entity, String token, Date reportDate, List<Dv01Entity> updateList) {
        String result = null;
        try {
            result = reportDeviceData(entity, token);
            updateEntityStatus(entity, result, reportDate);
        } catch (Exception e) {
            logger.error("处理设备数据失败, SN: {}", entity.getSn(), e);
            setEntityFailureStatus(entity, reportDate, result);
        }
        updateList.add(entity);
    }

    /**
     * 上报设备数据
     */
    private String reportDeviceData(Dv01Entity entity, String token) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("loginId", loginId);
        jsonObject.put("sn", entity.getSn());
        jsonObject.put("description", "");
        try (cn.hutool.http.HttpResponse response = HttpRequest.post(url + DUST_DATA_PATH)
                .header("Content-Type", CONTENT_TYPE_JSON)
                .header("accessToken", token)
                .body(jsonObject.toJSONString())
                .timeout(HTTP_TIME_OUT)
                .execute()) {
            return response.body();
        } catch (Exception e) {
            logger.error("HTTP请求异常，SN: {}", entity.getSn(), e);
            throw e;
        }
    }

    /**
     * 更新实体状态
     */
    private void updateEntityStatus(Dv01Entity entity, String result, Date reportDate) {
        entity.setReportDate(reportDate);
        JSONObject response = JSONObject.parseObject(result);
        if (response != null && Objects.equals(SUCCESS_CODE, response.getInteger(CODE))) {
            entity.setState(DATA_SUCCEED_STATE);
            logger.debug("设备数据上报成功, SN: {}", entity.getSn());
        } else {
            entity.setState(DATA_FAILURE_STATE);
            entity.setMsg(result);
            logger.warn("设备数据上报失败, SN: {}, 响应: {}", entity.getSn(), result);
        }
    }

    /**
     * 设置实体失败状态
     */
    private void setEntityFailureStatus(Dv01Entity entity, Date reportDate, String result) {
        entity.setState(DATA_FAILURE_STATE);
        entity.setReportDate(reportDate);
        entity.setMsg(result);
    }
}