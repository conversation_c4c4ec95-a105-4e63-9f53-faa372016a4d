/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.api.report.service;

import io.renren.api.report.dto.LoginDTO;
import io.renren.api.report.entity.UserEntity;
import io.renren.common.service.BaseService;

import java.util.Map;

/**
 * 用户
 *
 * @<NAME_EMAIL>
 */
public interface UserService extends BaseService<UserEntity> {

	UserEntity getByMobile(String mobile);

	UserEntity getUserByUserId(Long userId);

	/**
	 * 用户登录
	 * @param dto    登录表单
	 * @return        返回登录信息
	 */
	Map<String, Object> login(LoginDTO dto);
}
