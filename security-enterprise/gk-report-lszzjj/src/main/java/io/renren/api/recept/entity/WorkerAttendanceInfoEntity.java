package io.renren.api.recept.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 人员考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-31
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("WORKER_ATTENDANCE_INFO")
public class WorkerAttendanceInfoEntity implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
     * 考勤编号
     */
	@TableId
	private Long kq0201;

    /**
     * 项目编号
     */
	private Long pj0101;

    /**
     * 身份证号
     */
	private Long userId;

    /**
     * 刷卡时间，格式：yyyy-MM-ddHH:mm:ss
     */
	@JSONField(name = "checkdate")
	private Date swipeTime;

    /**
     * 考勤方向，1：入场，0：出场(dic)
     */
	@JSONField(name = "direction")
	private String swipeType;

    /**
     * 刷卡照片，url
     */
	@JSONField(name = "imageUrl")
	private String image;

    /**
     * 通行方式(dic)
     */
	@JSONField(name = "attendtype")
	private String attendType;

    /**
     * 考勤编号。上次前定义，目前为kq0201
     */
	private String curnumber;

	/**
	 * 设备编码
	 */
	private String deviceCode;

	/**
	 * 刷卡地点
	 */
	private String workPointName;

	/**
	 * 体温
	 */
	private String temperature;

    /**
     * 上报状态(0待上报,1上报成功,2上报失败)
     */
	private String reportStatus;

    /**
     * 错误原因
     */
	private String errorCause;

    /**
     * 上报时间
     */
	private Date reportDate;

	/**
     * 创建时间
     */
	private Date createDate;

	/**
	 * 更新时间
	 */
	private Date updateDate;

	/**
	 * 异步查询code
	 */
	private String asyncRequestCode;
}