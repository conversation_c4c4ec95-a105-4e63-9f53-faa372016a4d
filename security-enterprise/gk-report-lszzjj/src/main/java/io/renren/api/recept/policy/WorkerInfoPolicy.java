//package io.renren.api.recept.policy;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import io.renren.api.recept.entity.ProjectWorkerInfoEntity;
//import io.renren.api.recept.entity.TeamMasterInfoEntity;
//import io.renren.api.recept.service.ProjectWorkerInfoService;
//import io.renren.api.recept.service.TeamInfoService;
//import io.renren.common.constant.Constant;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//
/// **
// * 工人数据处理
// *
// * <AUTHOR>
// * @date 2023/7/14 11:10
// */
//@Component
//public class WorkerInfoPolicy implements DataStrategy {
//
//    @Autowired
//    private ProjectWorkerInfoService projectWorkerInfoService;
//
//    @Autowired
//    private TeamInfoService teamInfoService;
//
//    /**
//     * 成功状态
//     */
//    private final static String SUCCESS_STATE = "1";
//    /**
//     * 失败状态
//     */
//    private final static String FAIL_STATE = "2";
//
//    @Override
//    public String dataType() {
//        return Constant.WORKER;
//    }
//
//    @Override
//    public void processData(JSONObject data) {
//        /*
//          <P>
//           人员信息入库逻辑如下:
//           1、当人员信息上报成功之后,生产端推送的消息数据,消费端则丢弃数据.即:上报库的数据不允许修改
//           2、当人员信息上报成功之后且人员退场成功之后,生产端产生数据之后,消费端对接收的数据进行入库处理.即:对上报库的数据进行修改或者新增
//              然后重新上报此条数据.
//           3、上报未成功的数据允许对上报库数据进行修改或者新增处理
//          </P>
//         */
//        //组合JSON字符串
//        JSONObject jsonObject = data.getJSONObject("ps02");
//        jsonObject.putAll(data.getJSONObject("ps01"));
//        //查询数据是否存在
//        ProjectWorkerInfoEntity projectWorkerInfoEntity = JSON.toJavaObject(jsonObject, ProjectWorkerInfoEntity.class);
//        ProjectWorkerInfoEntity existingEntity = projectWorkerInfoService.selectById(projectWorkerInfoEntity.getUserid());
//        projectWorkerInfoEntity.setRecentPhoto(projectWorkerInfoEntity.getPhoto());
//        //工人是否存在
//        if(existingEntity != null){
//            if(FAIL_STATE.equals(existingEntity.getReportStatus())){
//                //如果工人存在并且上报状态为失败，重新上报修改工人信息
//                projectWorkerInfoEntity.setReportStatus("0");
//                projectWorkerInfoEntity.setUpdateDate(new Date());
//                projectWorkerInfoService.updateById(projectWorkerInfoEntity);
//            } else if(SUCCESS_STATE.equals(existingEntity.getReportStatus())&& projectWorkerInfoEntity.getExitDate() != null){
//                //如果工人存在并且上报状态为成功，必须有退场时间才能进行修改，重新上报修改工人信息
//                projectWorkerInfoEntity.setReportStatus("0");
//                projectWorkerInfoEntity.setUpdateDate(new Date());
//                projectWorkerInfoService.updateById(projectWorkerInfoEntity);
//            }
//        }else {
//            //工人类型
//            projectWorkerInfoEntity.setPostType("0");
//            //如果工人不存在，插入班组
//            projectWorkerInfoService.insert(projectWorkerInfoEntity);
//        }
//    }
//}
