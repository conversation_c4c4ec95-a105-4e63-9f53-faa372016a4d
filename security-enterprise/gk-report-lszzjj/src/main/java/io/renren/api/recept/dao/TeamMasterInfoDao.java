package io.renren.api.recept.dao;

import io.renren.api.recept.entity.TeamMasterInfoEntity;
import io.renren.api.report.dto.CompanyInfo;
import io.renren.api.report.dto.TeamMasterInfo;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目班组信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-26
 */
@Mapper
public interface TeamMasterInfoDao extends BaseDao<TeamMasterInfoEntity> {

    /**
     * 根据项目id查询管理班组
     * @param pj0101 项目查询编号
     * @param cp0201 参建单位
     * @return TeamMasterInfoEntity
     */
    TeamMasterInfoEntity selectByPj0101(Long pj0101, Long cp0201);

    /**
     * 查询待上报班组
     * @return List<TeamMasterInfo>
     */
    List<TeamMasterInfo> selectTeamInfoByReportStatus();
}