package io.renren.api.recept.dao;

import io.renren.api.recept.entity.WorkerAttendanceInfoEntity;
import io.renren.api.report.dto.AsyncResultDTO;
import io.renren.api.report.dto.WorkerAttendanceInfo;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 人员考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-26
 */
@Mapper
public interface WorkerAttendanceInfoDao extends BaseDao<WorkerAttendanceInfoEntity> {

    /**
     * 查询待上报考勤记录
     *
     * @return List<WorkerAttendanceInfo>
     */
    List<WorkerAttendanceInfo> selectWorkerAttendanceInfoByReportStatus();

    /**
     * 查询待核验考勤核验编号
     *
     * @return List<AsyncResultDTO>
     */
    List<AsyncResultDTO> getAsyncRequestCode();

    /**
     * 批量修改处理失败的考勤记录
     *
     * @param asyncRequestCode 异步查询code
     */
    void updateByAsyncRetrun(String asyncRequestCode, String message);

    /**
     * 更新上报考勤数据
     *
     * @param ids              ids
     * @param reportStatus     状态
     * @param errorCause       错误信息
     * @param asyncRequestCode 第三方编码
     */
    void updateBatchByIds(@Param("ids") List<Long> ids, @Param("reportStatus") String reportStatus, @Param("errorCause") String errorCause, @Param("asyncRequestCode") String asyncRequestCode);
}