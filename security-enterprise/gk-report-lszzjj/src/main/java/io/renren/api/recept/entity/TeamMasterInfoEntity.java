package io.renren.api.recept.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目班组信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-31
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("TEAM_MASTER_INFO")
@KeySequence(value = "SEQ_TEAM_MASTER_INFO")
public class TeamMasterInfoEntity implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
     * 班组编号
     */
    @TableId
	private Long tm0101;

    /**
     * 项目编号
     */
	private Long pj0101;

    /**
     * 企业编号
     */
	private Long cp0201;

    /**
     * 班组名称
     */
	@JSONField(name = "teamname")
	private String name;

    /**
     * 班组长姓名
     */
	@JSONField(name = "responsiblepersonname")
	private String leaderName;

    /**
     * 班组长身份证号
     */
	@JSONField(name = "responsiblepersonidnumber")
	private String leaderIdentification;

    /**
     * 班组长联系电话
     */
	@JSONField(name = "responsiblepersonphone")
	private String leaderPhone;

    /**
     * 班组类型（0：建筑工人班组，1：管理人员班组）
     */
	private String type;

    /**
     * 五冶返回班组编码，班组上传成功返回的编码，后期支持修改班组信息时，可以用来修改班组信息
     */
	private String groupCode;

	/**
	 * 工种编码
	 */
	private String workTypeCode;

    /**
     * 上报状态(0待上报,1上报成功,2上报失败)
     */
	private String reportStatus;

    /**
     * 错误原因
     */
	private String errorCause;
    /**
     * 上报时间
     */
	private Date reportDate;
	/**
     * 创建时间
     */
	private Date createDate;
	/**
	 * 更新时间
	 */
	private Date updateDate;
}