package io.renren.api.report.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-31
 */
@Data
@ApiModel(value = "五冶上报工人信息")
public class WorkerInfo implements Serializable{

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "无冶项目编号")
	@JSONField(serialize = false)
	private String projectCode;

	@ApiModelProperty(value = "工人编号")
	@JSONField(serialize = false)
	private Long userid;

	@ApiModelProperty(value = "企业编码")
	private String companyCode;

	@ApiModelProperty(value = "五冶上报返回班组编号")
	private String groupCode;

	@ApiModelProperty(value = "人员姓名")
	private String name;

	@ApiModelProperty(value = "身份证号")
	private String identification;

	@ApiModelProperty(value = "身份证有效期的开始日期，格式 yyyy-MM-dd")
	@JSONField(format = "yyyy-MM-dd")
	private Date startExpiration;

	@ApiModelProperty(value = "身份证有效期的结束日期，格式 yyyy-MM-dd，长期有效取值：2099-12-31")
	@JSONField(format = "yyyy-MM-dd")
	private Date endExpiration;

	@ApiModelProperty(value = "签发机关")
	private String issuingUnit;

	@ApiModelProperty(value = "住址")
	private String address;

	@ApiModelProperty(value = "民族（dic）")
	@Dict(fieldType = "NATION")
	private String ethnic;

	@ApiModelProperty(value = "联系电话")
	private String phone;

	@ApiModelProperty(value = "身份证图片")
	private String photo;

	@ApiModelProperty(value = "近照，url")
	private String recentPhoto;

	@ApiModelProperty(value = "身份证复印件正面照，url")
	private String identificationCopyFront;

	@ApiModelProperty(value = "身份证复印件反面照，url")
	private String identificationCopyBack;

	@ApiModelProperty(value = "政治面貌（dic）")
	@Dict(fieldType = "POLITICSTYPE")
	private String politicalStatus;

	@ApiModelProperty(value = "文化程度(dic)")
	@Dict(fieldType = "CULTURELEVELTYPE")
	private String degreeName;

	@ApiModelProperty(value = "紧急联系人")
	private String urgentContact;

	@ApiModelProperty(value = "紧急联系人电话")
	private String urgentTel;

	@ApiModelProperty(value = "人员角色(0:工人，1:管理人员)")
	private String postType;

	@ApiModelProperty(value = "是否班组长(0:否，1:是)")
	private String isLeader;

	@ApiModelProperty(value = "工种/岗位编码(dic)")
	@Dict(fieldType = "WORKTYPECODE")
	private String workTypeCode;

	@ApiModelProperty(value = "进场（也称入职）日期，格式：yyyy-MM-dd")
	@JSONField(format = "yyyy-MM-dd")
	private Date enterDate;

	@ApiModelProperty(value = "退场（也称离职）日期，进场时这个值为空；退场时，必填，格式：yyyy-MM-d")
	@JSONField(format = "yyyy-MM-dd")
	private Date exitDate;

	@ApiModelProperty(value = "出生日期")
	@JSONField(format = "yyyy-MM-dd")
	private Date birthDate;

	@ApiModelProperty(value = "血型")
	private String bloodType;

	@ApiModelProperty(value = "银行卡号")
	private String bankCode;

	@ApiModelProperty(value = "银行名称")
	private String bankName;

	@ApiModelProperty(value = "五冶返回工人code")
	private Long usercode;
}