//package io.renren.api.recept.policy;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import io.renren.api.recept.entity.ProjectInfoEntity;
//import io.renren.api.recept.service.ProjectInfoService;
//import io.renren.common.constant.Constant;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
/// **
// * 项目数据处理
// *
// * <AUTHOR>
// * @date 2023/7/14 11:04
// */
//@Component
//public class ProjectInfoPolicy implements DataStrategy {
//    @Resource
//    private ProjectInfoService projectInfoService;
//
//    @Override
//    public String dataType() {
//        return Constant.PROJECT;
//    }
//
//    @Override
//    public void processData(JSONObject data) {
//        /*
//          <p>
//          生产端推送消息之后,消费端只进行一次新增不做任何修改操作
//          </p>
//         */
//        ProjectInfoEntity projectInfoEntity = JSON.toJavaObject(data.getJSONObject("pj01"), ProjectInfoEntity.class);
//        //查询项目是否存在
//        ProjectInfoEntity entity = projectInfoService.selectById(projectInfoEntity.getPj0101());
//        if (entity == null) {
//            projectInfoService.insert(projectInfoEntity);
//        }
//    }
//}
