package io.renren.api.recept.service.impl;

import io.renren.api.recept.dao.ProjectWorkerInfoDao;
import io.renren.api.recept.entity.ProjectWorkerInfoEntity;
import io.renren.api.recept.service.ProjectWorkerInfoService;
import io.renren.api.report.dto.WorkerAttendanceInfo;
import io.renren.api.report.dto.WorkerInfo;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-26
 */
@Service
public class ProjectWorkerInfoServiceImpl extends BaseServiceImpl<ProjectWorkerInfoDao, ProjectWorkerInfoEntity> implements ProjectWorkerInfoService {

    @Override
    public List<WorkerInfo> selectWorkerInfoByReportStatus() {
        return baseDao.selectWorkerInfoByReportStatus();
    }
}