package io.renren.api.recept.service;

import io.renren.api.recept.entity.ProjectCompanyInfoEntity;
import io.renren.api.report.dto.CompanyInfo;
import io.renren.common.service.BaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/18 10:11
 */
public interface ProjectCompanyInfoService extends BaseService<ProjectCompanyInfoEntity> {
    /**
     * 查询待上报的企业信息
     *
     * @return List<CompanyInfo>
     */
    List<CompanyInfo> selectCompanyInfoByReportStatus();

    /**
     * 查询当前参建单位类型
     * @param cp0201 参建单位
     * @return
     */
    String getCorpType(Long cp0201);
}
