package io.renren.api.recept;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import io.renren.api.recept.policy.ReceptionDataFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/7/14 10:47
 */
@Component
public class FanoutMsgConsumer {
    @Resource
    private ReceptionDataFactory receptionDataFactory;

    /**
     * 消费端接收数据
     *
     * @param data    数据内容
     * @param channel Channel
     * @param message Message
     */
    @RabbitListener(queues = "QUEU_LSZ_ZJJ")
    @RabbitHandler
    public void messageService(JSONObject data, Channel channel, Message message) {
        try {
            String datatype = data.getString("dataType");
            receptionDataFactory.calculate(datatype, data);
        } catch (Exception e) {
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
            e.printStackTrace();
        }
    }

}
