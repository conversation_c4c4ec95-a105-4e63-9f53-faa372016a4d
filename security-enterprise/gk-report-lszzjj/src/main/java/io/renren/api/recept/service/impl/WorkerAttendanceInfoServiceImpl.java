package io.renren.api.recept.service.impl;

import io.renren.api.recept.dao.WorkerAttendanceInfoDao;
import io.renren.api.recept.entity.WorkerAttendanceInfoEntity;
import io.renren.api.recept.service.WorkerAttendanceInfoService;
import io.renren.api.report.dto.AsyncResultDTO;
import io.renren.api.report.dto.WorkerAttendanceInfo;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 人员考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-26
 */
@Service
public class WorkerAttendanceInfoServiceImpl extends BaseServiceImpl<WorkerAttendanceInfoDao, WorkerAttendanceInfoEntity> implements WorkerAttendanceInfoService {

    @Override
    public List<WorkerAttendanceInfo> selectWorkerAttendanceInfoByReportStatus() {
        return baseDao.selectWorkerAttendanceInfoByReportStatus();
    }

    @Override
    public  List<AsyncResultDTO> getAsyncRequestCode() {
        return baseDao.getAsyncRequestCode();
    }

    @Override
    public void updateByAsyncRetrun(String asyncRequestCode, String message) {
        baseDao.updateByAsyncRetrun(asyncRequestCode, message);
    }

    @Override
    public void updateBatchByIds(List<Long> ids, String reportStatus, String errorCause, String asyncRequestCode) {
        baseDao.updateBatchByIds(ids, reportStatus, errorCause,asyncRequestCode);
    }

}