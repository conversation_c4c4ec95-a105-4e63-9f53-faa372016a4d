//package io.renren.api.recept.policy;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import io.renren.api.recept.entity.TeamMasterInfoEntity;
//import io.renren.api.recept.service.TeamInfoService;
//import io.renren.common.constant.Constant;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//
/// **
// * 班组数据处理
// *
// * <AUTHOR>
// * @date 2023/7/14 11:08
// */
//@Component
//public class TeamInfoPolicy implements DataStrategy {
//
//    @Autowired
//    private TeamInfoService teamInfoService;
//
//    /**
//     * 成功状态
//     */
//    private final static String SUCCESS_STATE = "1";
//
//    @Override
//    public String dataType() {
//        return Constant.TEAM;
//    }
//
//    @Override
//    public void processData(JSONObject data) {
//        //获取班组json
//        JSONObject jsonObject = data.getJSONObject("tm01");
//        //查询数据是否存在
//        TeamMasterInfoEntity teamMasterInfoEntity = JSON.toJavaObject(jsonObject, TeamMasterInfoEntity.class);
//        TeamMasterInfoEntity existingEntity = teamInfoService.selectById(teamMasterInfoEntity.getTm0101());
//        //查询班组是否存在
//        if(existingEntity != null){
//            // 如果企业信息存在而且已上报成功,则更新数据重新上报
//            teamMasterInfoEntity.setReportStatus("0");
//            teamMasterInfoEntity.setUpdateDate(new Date());
//            teamInfoService.updateById(teamMasterInfoEntity);
//        }else {
//            //如果班组信息不存在，则直接插入数据,默认工人班组
//            teamMasterInfoEntity.setType("0");
//            teamInfoService.insert(teamMasterInfoEntity);
//        }
//    }
//}
