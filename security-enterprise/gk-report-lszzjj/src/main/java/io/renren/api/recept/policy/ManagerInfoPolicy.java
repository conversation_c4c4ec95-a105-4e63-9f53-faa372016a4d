//package io.renren.api.recept.policy;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import io.renren.api.recept.entity.ProjectMangerInfoEntity;
//import io.renren.api.recept.entity.TeamMasterInfoEntity;
//import io.renren.api.recept.service.ProjectCompanyInfoService;
//import io.renren.api.recept.service.ProjectManagerInfoService;
//import io.renren.api.recept.service.TeamInfoService;
//import io.renren.common.constant.Constant;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//
/// **
// * 管理人员数据处理
// *
// * <AUTHOR>
// * @date 2023/7/14 11:11
// */
//@Component
//public class ManagerInfoPolicy implements DataStrategy {
//
//    @Autowired
//    private ProjectManagerInfoService projectManagerInfoService;
//
//    @Autowired
//    private TeamInfoService teamInfoService;
//
//    @Autowired
//    private ProjectCompanyInfoService projectCompanyInfoService;
//
//    /**
//     * 成功状态
//     */
//    private final static String SUCCESS_STATE = "1";
//    /**
//     * 失败状态
//     */
//    private final static String FAIL_STATE = "2";
//
//    @Override
//    public String dataType() {
//        return Constant.MANAGER;
//    }
//
//    @Override
//    public void processData(JSONObject data) {
//        //组合JSON字符串
//        JSONObject jsonObject = data.getJSONObject("ps04");
//        jsonObject.putAll(data.getJSONObject("ps01"));
//        //查询数据是否存在
//        ProjectMangerInfoEntity projectMangerInfoEntity = JSON.toJavaObject(jsonObject, ProjectMangerInfoEntity.class);
//        ProjectMangerInfoEntity existingEntity = projectManagerInfoService.selectById(projectMangerInfoEntity.getUserid());
//        //管理人员是否存在
//        if(existingEntity != null){
//            //如果管理人员存在修改上报状态，重新上报修改管理人员信息
//            projectMangerInfoEntity.setReportStatus("0");
//            projectMangerInfoEntity.setUpdateDate(new Date());
//            projectManagerInfoService.updateById(projectMangerInfoEntity);
//        }else {
//            //如果管理人员不存在，先查询班组是否存在
//            TeamMasterInfoEntity teamMasterInfoEntity = teamInfoService.selectByPj0101(
//                    projectMangerInfoEntity.getPj0101(), (Long) data.getJSONObject("ps04").get("cp0201"));
//            if(teamMasterInfoEntity == null){
//                //不存在则新增一个班组
//                teamMasterInfoEntity = new TeamMasterInfoEntity();
//                teamMasterInfoEntity.setType("1");
//                String type = projectCompanyInfoService.getCorpType((Long) data.getJSONObject("ps04").get("cp0201"));
//                teamMasterInfoEntity.setName(type + "管理班组");
//                teamMasterInfoEntity.setPj0101(projectMangerInfoEntity.getPj0101());
//                teamMasterInfoEntity.setCp0201((Long) data.getJSONObject("ps04").get("cp0201"));
//                teamInfoService.insert(teamMasterInfoEntity);
//            }
//            //设置班组和管理人员类型
//            projectMangerInfoEntity.setTm0101(teamMasterInfoEntity.getTm0101());
//            projectMangerInfoEntity.setPostType("1");
//            //设置五冶班组code，插入数据
//            projectManagerInfoService.insert(projectMangerInfoEntity);
//        }
//    }
//}
