<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.BPj01Dao">

    <resultMap type="io.renren.api.recept.entity.BPj01Entity" id="bPj01Map">
        <result property="pj0101" column="PJ0101"/>
        <result property="safetyno" column="SAFETYNO"/>
        <result property="code" column="CODE"/>
        <result property="name" column="NAME"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="industry" column="INDUSTRY"/>
        <result property="category" column="CATEGORY"/>
        <result property="constructtype" column="CONSTRUCTTYPE"/>
        <result property="investtype" column="INVESTTYPE"/>
        <result property="areacode" column="AREACODE"/>
        <result property="address" column="ADDRESS"/>
        <result property="buildingarea" column="BUILDINGAREA"/>
        <result property="buildinglength" column="BUILDINGLENGTH"/>
        <result property="invest" column="INVEST"/>
        <result property="engineering" column="ENGINEERING"/>
        <result property="scale" column="SCALE"/>
        <result property="startdate" column="STARTDATE"/>
        <result property="completeDate" column="COMPLETE_DATE"/>
        <result property="lng" column="LNG"/>
        <result property="lat" column="LAT"/>
        <result property="linkman" column="LINKMAN"/>
        <result property="linkphone" column="LINKPHONE"/>
        <result property="ismajorProject" column="ISMAJOR_PROJECT"/>
        <result property="isdeposit" column="ISDEPOSIT"/>
        <result property="prjstatus" column="PRJSTATUS"/>
        <result property="deptId" column="DEPT_ID"/>
        <result property="depcode" column="DEPCODE"/>
        <result property="sectioncode" column="SECTIONCODE"/>
        <result property="region" column="REGION"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>


</mapper>