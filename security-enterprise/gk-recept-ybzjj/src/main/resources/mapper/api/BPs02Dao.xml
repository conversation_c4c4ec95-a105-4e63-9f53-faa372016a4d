<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.BPs02Dao">

    <resultMap type="io.renren.api.recept.entity.BPs02Entity" id="bPs02Map">
        <result property="ps0201" column="PS0201"/>
        <result property="ps0101" column="PS0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="tm0101" column="TM0101"/>
        <result property="isteamleader" column="ISTEAMLEADER"/>
        <result property="worktypecode" column="WORKTYPECODE"/>
        <result property="payrollbankcardnumber" column="PAYROLLBANKCARDNUMBER"/>
        <result property="payrollbankname" column="PAYROLLBANKNAME"/>
        <result property="payrolltopbankcode" column="PAYROLLTOPBANKCODE"/>
        <result property="issuecardpicurl" column="ISSUECARDPICURL"/>
        <result property="hasbuyinsurance" column="HASBUYINSURANCE"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="memo" column="MEMO"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>


    <select id="findListByProjectCode" resultType="io.renren.api.report.dto.WorkerInfo">
        SELECT
            a.PS0201,
            a.PS0101,
            a.PJ0101,
            a.TM0101,
            b.IDCARDNUMBER,
            b.USERID
        FROM B_PS02 a left join YB_STAFF b on a.PS0201 = b.WORKNUMBER
                      left join B_PS01 b on a.PS0101 = b.PS0101
        WHERE a.PJ0101 = #{pj0101} and b.TYPE = '1'
    </select>
    <select id="findLastTime" resultType="java.lang.String">
        SELECT nvl(MAX(a.UPDATE_DATE), sysdate - 900) as last_date
        FROM B_PS02 a left join B_TM01 b on a.TM0101 = b.TM0101
        WHERE a.PJ0101 = #{pj0101}
            and b.TM0101 = #{tm0101}
    </select>

</mapper>