<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.BPs01Dao">

    <resultMap type="io.renren.api.recept.entity.BPs01Entity" id="bPs01Map">
        <result property="ps0101" column="PS0101"/>
        <result property="name" column="NAME"/>
        <result property="idcardtype" column="IDCARDTYPE"/>
        <result property="idcardnumber" column="IDCARDNUMBER"/>
        <result property="gender" column="GENDER"/>
        <result property="nation" column="NATION"/>
        <result property="birthday" column="BIRTHDAY"/>
        <result property="address" column="ADDRESS"/>
        <result property="edulevel" column="EDULEVEL"/>
        <result property="degree" column="DEGREE"/>
        <result property="areacode" column="AREACODE"/>
        <result property="headimageurl" column="HEADIMAGEURL"/>
        <result property="politicstype" column="POLITICSTYPE"/>
        <result property="isjoined" column="ISJOINED"/>
        <result property="joinedtime" column="JOINEDTIME"/>
        <result property="cellphone" column="CELLPHONE"/>
        <result property="cultureleveltype" column="CULTURELEVELTYPE"/>
        <result property="specialty" column="SPECIALTY"/>
        <result property="hasbadmedicalhistory" column="HASBADMEDICALHISTORY"/>
        <result property="urgentlinkman" column="URGENTLINKMAN"/>
        <result property="urgentlinkmanphone" column="URGENTLINKMANPHONE"/>
        <result property="workdate" column="WORKDATE"/>
        <result property="maritalstatus" column="MARITALSTATUS"/>
        <result property="grantorg" column="GRANTORG"/>
        <result property="positiveidcardimageurl" column="POSITIVEIDCARDIMAGEURL"/>
        <result property="negativeidcardimageurl" column="NEGATIVEIDCARDIMAGEURL"/>
        <result property="startdate" column="STARTDATE"/>
        <result property="expirydate" column="EXPIRYDATE"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>


</mapper>