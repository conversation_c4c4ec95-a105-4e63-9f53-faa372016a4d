<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.YbLogRequestDao">

    <resultMap type="io.renren.api.recept.entity.YbLogRequestEntity" id="ybLogRequestMap">
        <result property="id" column="ID"/>
        <result property="requestUrl" column="REQUEST_URL"/>
        <result property="requestMethod" column="REQUEST_METHOD"/>
        <result property="requestBody" column="REQUEST_BODY"/>
        <result property="responseBody" column="RESPONSE_BODY"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="businesstype" column="BUSINESSTYPE"/>
        <result property="status" column="STATUS"/>
        <result property="enteredstatus" column="ENTEREDSTATUS"/>
        <result property="exceptionInfo" column="EXCEPTION_INFO"/>
        <result property="creationTime" column="CREATION_TIME"/>
    </resultMap>


</mapper>