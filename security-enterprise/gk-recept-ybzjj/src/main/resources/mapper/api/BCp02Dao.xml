<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.BCp02Dao">

    <resultMap type="io.renren.api.recept.entity.BCp02Entity" id="bCp02Map">
        <result property="cp0201" column="CP0201"/>
        <result property="cp0101" column="CP0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="corptype" column="CORPTYPE"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="pmName" column="PM_NAME"/>
        <result property="pmIdCardType" column="PM_ID_CARD_TYPE"/>
        <result property="pmIdCardNumber" column="PM_ID_CARD_NUMBER"/>
    </resultMap>
    <select id="findListByProjectCode" resultType="io.renren.api.report.dto.CompanyInfo">
        SELECT
            a.CP0101,
            a.CP0201,
            a.PJ0101,
            b.CORPCODE
        FROM B_CP02 a left join b_cp01 b on a.CP0101 = b.CP0101
        WHERE PJ0101 = #{PJ0101}
    </select>


</mapper>