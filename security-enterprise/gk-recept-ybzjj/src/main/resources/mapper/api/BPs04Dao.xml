<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.BPs04Dao">

    <resultMap type="io.renren.api.recept.entity.BPs04Entity" id="bPs04Map">
        <result property="ps0401" column="PS0401"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="cp0201" column="CP0201"/>
        <result property="ps0101" column="PS0101"/>
        <result property="jobtype" column="JOBTYPE"/>
        <result property="managestatus" column="MANAGESTATUS"/>
        <result property="managetype" column="MANAGETYPE"/>
        <result property="photo" column="PHOTO"/>
        <result property="hasbuyinsurance" column="HASBUYINSURANCE"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="findLastTime" resultType="java.lang.String">
        SELECT nvl(MAX(a.UPDATE_DATE), sysdate - 900) as last_date
        FROM B_PS04 a
        WHERE PJ0101 = #{pj0101}
            and a.CP0201 = #{cp0201}
    </select>

    <select id="findListByProjectCode" resultType="io.renren.api.report.dto.ManagerInfo">
        SELECT a.PS0401,
               a.PJ0101,
               a.CP0201,
               a.PS0101,
               b.idcardnumber,
               b.userid
        FROM B_PS04 a left join YB_STAFF b on a.PS0401 = b.WORKNUMBER
            left join B_PS01 b on a.PS0101 = b.PS0101
        WHERE a.PJ0101 = #{pj0101} and b.TYPE = '2'
    </select>

</mapper>