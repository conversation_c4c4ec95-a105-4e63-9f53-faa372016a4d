<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.BKq02Dao">

    <resultMap type="io.renren.api.recept.entity.BKq02Entity" id="bKq02Map">
        <result property="kq0201" column="KQ0201"/>
        <result property="userId" column="USER_ID"/>
        <result property="personName" column="PERSON_NAME"/>
        <result property="personType" column="PERSON_TYPE"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="deviceserialno" column="DEVICESERIALNO"/>
        <result property="checkdate" column="CHECKDATE"/>
        <result property="direction" column="DIRECTION"/>
        <result property="attendtype" column="ATTENDTYPE"/>
        <result property="lng" column="LNG"/>
        <result property="lat" column="LAT"/>
        <result property="imageUrl" column="IMAGE_URL"/>
        <result property="temperature" column="TEMPERATURE"/>
        <result property="standard" column="STANDARD"/>
        <result property="temperatureState" column="TEMPERATURE_STATE"/>
        <result property="tempUnit" column="TEMP_UNIT"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="downloadCount" column="DOWNLOAD_COUNT"/>
    </resultMap>

    <select id="findLastTime" resultType="java.util.Date">
        SELECT nvl(MAX(a.CHECKDATE), sysdate - 90) as last_date
        FROM b_kq02 a
        WHERE PJ0101 = #{pj0101}
    </select>

    <select id="getListUserIdByPj0101" resultType="io.renren.api.report.dto.WorkerAttendanceInfo">
        select
            a.USERID,
            a.PJ0101,
            a.TYPE as person_type,
            a.worknumber
        from YB_STAFF a
        where a.pj0101 = #{pj0101}
    </select>

</mapper>