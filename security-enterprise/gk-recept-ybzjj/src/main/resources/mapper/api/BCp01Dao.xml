<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.BCp01Dao">

    <resultMap type="io.renren.api.recept.entity.BCp01Entity" id="bCp01Map">
        <result property="cp0101" column="CP0101"/>
        <result property="corpcode" column="CORPCODE"/>
        <result property="corpname" column="CORPNAME"/>
        <result property="organizationtype" column="ORGANIZATIONTYPE"/>
        <result property="areacode" column="AREACODE"/>
        <result property="address" column="ADDRESS"/>
        <result property="zipcode" column="ZIPCODE"/>
        <result property="legalman" column="LEGALMAN"/>
        <result property="idcardtype" column="IDCARDTYPE"/>
        <result property="legalmanidcardnumber" column="LEGALMANIDCARDNUMBER"/>
        <result property="regcapital" column="REGCAPITAL"/>
        <result property="capitalcurrencytype" column="CAPITALCURRENCYTYPE"/>
        <result property="establishdate" column="ESTABLISHDATE"/>
        <result property="officephone" column="OFFICEPHONE"/>
        <result property="faxnumber" column="FAXNUMBER"/>
        <result property="linkman" column="LINKMAN"/>
        <result property="linkphone" column="LINKPHONE"/>
        <result property="linkcellphone" column="LINKCELLPHONE"/>
        <result property="email" column="EMAIL"/>
        <result property="website" column="WEBSITE"/>
        <result property="businessstatus" column="BUSINESSSTATUS"/>
        <result property="entscope" column="ENTSCOPE"/>
        <result property="regdept" column="REGDEPT"/>
        <result property="memo" column="MEMO"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>


</mapper>