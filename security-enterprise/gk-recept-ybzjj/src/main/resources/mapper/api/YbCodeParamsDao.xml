<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.YbCodeParamsDao">

    <resultMap type="io.renren.api.recept.entity.YbCodeParamsEntity" id="ybCodeParamsMap">
        <result property="pj0101" column="PJ0101"/>
        <result property="code" column="CODE"/>
    </resultMap>

    <select id="selectProjectParams" resultType="io.renren.api.recept.dto.RequestBodyDto">
        select
            a.PJ0101,
            a.CODE projectcode
        from YB_CODE_PARAMS a
            left join B_PJ01 b on a.PJ0101 = b.PJ0101
        where b.PRJSTATUS = '3'
    </select>

    <select id="selectProjectParamsByCorp" resultType="io.renren.api.recept.dto.RequestBodyDto">
        select
            a.PJ0101,
            a.CODE projectcode,
            d.CORPCODE,
            c.CP0201
        from YB_CODE_PARAMS a
                 left join B_PJ01 b on a.PJ0101 = b.PJ0101
                 left join B_CP02 c on b.PJ0101 = c.PJ0101
                 left join B_CP01 d on d.CP0101 = c.CP0101
        where b.PRJSTATUS = '3'
    </select>

    <select id="selectProjectParamsByTeam" resultType="io.renren.api.recept.dto.RequestBodyDto">
        select
            a.PJ0101,
            a.CODE projectcode,
            d.CORPCODE,
            c.CP0201,
            e.TM0101,
            e.TEAMSYSNO
        from YB_CODE_PARAMS a
                 left join B_PJ01 b on a.PJ0101 = b.PJ0101
                 left join B_TM01 e on e.PJ0101 = a.PJ0101
                 left join B_CP02 c on c.cp0201 = e.cp0201
                 left join B_CP01 d on d.CP0101 = c.CP0101
        where b.PRJSTATUS = '3'
    </select>

</mapper>