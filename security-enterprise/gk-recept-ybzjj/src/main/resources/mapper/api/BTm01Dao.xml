<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.recept.dao.BTm01Dao">

    <resultMap type="io.renren.api.recept.entity.BTm01Entity" id="bTm01Map">
        <result property="tm0101" column="TM0101"/>
        <result property="cp0201" column="CP0201"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="teamsysno" column="TEAMSYSNO"/>
        <result property="teamname" column="TEAMNAME"/>
        <result property="responsiblepersonname" column="RESPONSIBLEPERSONNAME"/>
        <result property="responsiblepersonphone" column="RESPONSIBLEPERSONPHONE"/>
        <result property="idcardtype" column="IDCARDTYPE"/>
        <result property="responsiblepersonidnumber" column="RESPONSIBLEPERSONIDNUMBER"/>
        <result property="entrytime" column="ENTRYTIME"/>
        <result property="exittime" column="EXITTIME"/>
        <result property="inOrOut" column="IN_OR_OUT"/>
        <result property="memo" column="MEMO"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="findListByProjectCode" resultType="io.renren.api.report.dto.TeamMasterInfo">
        select
            a.tm0101,
            a.TEAMSYSNO,
            a.TEAMNAME,
            a.CP0201,
            a.PJ0101
        FROM B_TM01 a
        where a.pj0101 = #{PJ0101}
    </select>


</mapper>