package io.renren.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import io.renren.annotation.DictConvert;
import io.renren.api.recept.dto.DictInfo;
import io.renren.utils.DictUtils;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.List;

/**
 * 字典翻译
 *
 * <AUTHOR> <PERSON>@gmail.com
 */
@Slf4j
public class DictConvertHandler {

    /**
     * 数据翻译
     *
     * @param obj
     */
    public static <T> void translateDict(T obj) {
        if (ObjectUtil.isEmpty(obj)) {
            log.info("DictConvertHandler translateDict obj is null");
            return;
        }
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            boolean annotationPresent = field.isAnnotationPresent(DictConvert.class);
            if (annotationPresent) {
                // 检查字段是否有 @Dict 注解
                DictConvert dictConvert = field.getAnnotation(DictConvert.class);
                Object fieldValue = BeanUtil.getFieldValue(obj, field.getName());
                // 获取字典标签
                DictInfo dict = new DictInfo();
                DictUtils.dictList.stream().filter(dictInfo -> dictInfo.getFieldType().equals(dictConvert.fieldType()) && dictInfo.getOriginalValue().equals(fieldValue))
                        .findFirst().ifPresent(p -> dict.setTranscodeValue(p.getTranscodeValue()));
                // 反射设置 *Label 字段
                field.setAccessible(true);
                BeanUtil.setFieldValue(obj, field.getName(), dict.getTranscodeValue());
            }
        }
    }

    /**
     * 批量数据翻译
     *
     * @param obj
     */
    public static <T> void batchTranslateDict(List<T> obj) {
        if (CollUtil.isEmpty(obj)) {
            log.info("DictConvertHandler batchTranslateDict obj is null");
            return;
        }
        obj.forEach(DictConvertHandler::translateDict);
    }
}
