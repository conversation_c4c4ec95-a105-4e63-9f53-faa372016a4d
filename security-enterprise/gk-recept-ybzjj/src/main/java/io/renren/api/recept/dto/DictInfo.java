package io.renren.api.recept.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/17 15:24
 */
@Data
@ApiModel(value = "字典信息")
public class DictInfo implements Serializable {

    private static final long serialVersionUID = 7652268476538446386L;

    @ApiModelProperty(value = "字典类型")
    private String fieldType;

    @ApiModelProperty(value = "字典原值")
    private String originalValue;

    @ApiModelProperty(value = "字典转码值")
    private String transcodeValue;

}
