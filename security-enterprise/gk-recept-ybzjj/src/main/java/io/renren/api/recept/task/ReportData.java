package io.renren.api.recept.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import io.renren.api.recept.dto.*;
import io.renren.api.recept.entity.*;
import io.renren.api.recept.service.*;
import io.renren.api.report.dto.*;
import io.renren.handler.DictConvertHandler;
import io.renren.utils.ApiSignUtils;
import io.renren.utils.HttpUtils;
import io.renren.utils.SendDataUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据上报定时任务
 *
 * <AUTHOR>
 * @date 2023/7/18 15:56
 */
@Component
public class ReportData {
    /**
     * 日志文件
     */
    private static final Logger logger = LoggerFactory.getLogger(ReportData.class);

    @Resource
    private YbCodeParamsService ybCodeParamsService;

    @Resource
    private BCp01Service bCp01Service;

    @Resource
    private BCp02Service bCp02Service;

    @Resource
    private BTm01Service tm01Service;

    @Resource
    private BPs01Service bPs01Service;

    @Resource
    private BPs02Service bPs02Service;

    @Resource
    private BPs04Service bPs04Service;

    @Resource
    private BKq02Service bKq02Service;

    @Resource
    private YbStaffService ybStaffService;

    @Resource
    private YbKq02FailService ybKq02FailService;

    @Resource
    private YbLogRequestService ybLogRequestService;

    @Value("${mcc5.httpUrl}")
    private String url;
    /**
     * 接口成功状态
     */
    private static final String SUCCEED_STATE = "0";
    /**
     * 数据成功状态
     */
    private static final String DATA_SUCCEED_STATE = "1";
    /**
     * 数据失败状态
     */
    private static final String DATA_FAILURE_STATE = "2";
    /**
     * 状态码
     */
    private static final String CODE = "code";
    /**
     * 数据
     */
    private static final String DATA = "data";
    /**
     * 数组
     */
    private static final String RECORDS = "records";
    /**
     * 请求超时时间(单位毫秒)
     */
    private static final int HTTP_TIME_OUT = 60000;

    @Autowired
    private SendDataUtil sendDataUtil;

    /**
     * 获取参建单位信息
     */
    @Scheduled(initialDelay = 1000 * 5, fixedRate = 1000 * 60 * 60 * 3)
    public void receptCompanyInfo() {
        String httpUrl = url + "/api/v1/getCompanys";
        //查询需要查询参建单位的项目
        List<RequestBodyDto> list = ybCodeParamsService.selectProjectParams();
        for (RequestBodyDto requestBodyDto : list) {
            logger.info("开始获取参建单位数据{}", requestBodyDto.getProjectcode());
            YbLogRequestEntity entity = new YbLogRequestEntity();
            try {
                //签名请求数据
                Map<String, Object> signature = ApiSignUtils.checkSignature();
                Map<String, Object> date = new HashMap<>();
                date.put("projectcode", requestBodyDto.getProjectcode());
                entity.setRequestBody(date.toString());
                String result = HttpUtil.createPost(httpUrl)
                        .body(JSONObject.toJSONString(date))
                        .header("appkey",signature.get("appkey").toString())
                        .header("nonce",signature.get("nonce").toString())
                        .header("timestamp",signature.get("timestamp").toString())
                        .header("sign",signature.get("sign").toString())
                        .timeout(HTTP_TIME_OUT)
                        .execute().body();
                JSONObject responseResult = JSONObject.parseObject(result);
                entity.setRequestBody(responseResult.toString());
                //成功
                if (SUCCEED_STATE.equals(responseResult.getString(CODE))) {
                    //记录结果，用于推送数据
                    List<BCp02DTO> ids = new ArrayList<>();
                    //json转对象
                    String data = responseResult.getString(DATA);
                    List<CompanyInfo> companyInfoList = JSONObject.parseArray(data, CompanyInfo.class);
                    //查询当前数据库的该项目的参建单位信息
                    List<CompanyInfo> companyInfos = bCp02Service.findListByProjectCode(requestBodyDto.getPj0101());
                    Map<String, CompanyInfo> companyInfoMap = companyInfos.stream()
                            .collect(Collectors.toMap(CompanyInfo::getCorpcode, info -> info, (existing, replacement) -> existing));
                    //对比数据
                    for(CompanyInfo companyInfo:companyInfoList){
                        BCp01Entity cp01Entities = BeanUtil.copyProperties(companyInfo, BCp01Entity.class);
                        BCp02Entity cp02Entities = BeanUtil.copyProperties(companyInfo, BCp02Entity.class);
                        //对比后存在则修改不存在则新增
                        if(companyInfoMap.containsKey(companyInfo.getCorpcode())){
                            cp01Entities.setCp0101(companyInfoMap.get(companyInfo.getCorpcode()).getCp0101());
                            bCp01Service.updateById(cp01Entities);
                            cp02Entities.setCp0201(companyInfoMap.get(companyInfo.getCorpcode()).getCp0201());
                            bCp02Service.updateById(cp02Entities);
                        }else {
                            //插入企业基础信息
                            BCp01Entity bCp01 = bCp01Service.selectByCorpCode(cp01Entities.getCorpcode());
                            if(bCp01 == null){
                                bCp01Service.insert(cp01Entities);
                            }else {
                                cp01Entities = bCp01;
                            }
                            //插入企业关联信息
                            cp02Entities.setCp0101(cp01Entities.getCp0101());
                            cp02Entities.setPj0101(requestBodyDto.getPj0101());
                            bCp02Service.insert(cp02Entities);
                        }
                        BCp02Entity bCp02Entity = bCp02Service.selectById(cp02Entities.getCp0201());
                        BCp02DTO cp02DTO = BeanUtil.copyProperties(bCp02Entity, BCp02DTO.class);
                        BCp01Entity bCp01Entity = bCp01Service.selectById(cp01Entities.getCp0101());
                        BCp01DTO cp01DTO = BeanUtil.copyProperties(bCp01Entity, BCp01DTO.class);
                        cp02DTO.setCp01DTO(cp01DTO);
                        ids.add(cp02DTO);
                    }
                    entity.setStatus(DATA_SUCCEED_STATE);
                    //上报数据推送
                    for(BCp02DTO bCp02DTO: ids){
                        sendDataUtil.sendData(bCp02DTO);
                    }
                } else {
                    entity.setStatus(DATA_FAILURE_STATE);
                    entity.setExceptionInfo("参建单位查询失败");
                }
            } catch (Exception e) {
                entity.setStatus(DATA_FAILURE_STATE);
                entity.setExceptionInfo(e.getMessage());
                logger.error("企业信息查询出现异常,异常信息:" + e.getMessage());
            } finally {
                try {
                    Thread.sleep(2000);
                    entity.setRequestUrl(httpUrl);
                    entity.setRequestMethod("POST");
                    entity.setPj0101(requestBodyDto.getPj0101());
                    entity.setCreationTime(DateUtil.date());
                    ybLogRequestService.insert(entity);
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 获取班组信息
     */
    @Scheduled(initialDelay = 1000 * 40, fixedRate = 1000 * 60 * 60 * 3)
    public void receptTeamInfo() {
        String httpUrl = url + "/api/v1/getTeams";
        //查询需要查询班组的项目
        List<RequestBodyDto> list = ybCodeParamsService.selectProjectParamsByCorp();
        for (RequestBodyDto requestBodyDto : list) {
            logger.info("开始获取班组数据{}", requestBodyDto.getProjectcode());
            YbLogRequestEntity entity = new YbLogRequestEntity();
            try {
                //签名请求数据
                Map<String, Object> signature = ApiSignUtils.checkSignature();
                Map<String, Object> date = new HashMap<>();
                date.put("projectcode", requestBodyDto.getProjectcode());
                date.put("corpcode", requestBodyDto.getCorpcode());
                String result = HttpUtil.createPost(httpUrl)
                        .body(JSONObject.toJSONString(date))
                        .header("appkey", signature.get("appkey").toString())
                        .header("nonce", signature.get("nonce").toString())
                        .header("timestamp", signature.get("timestamp").toString())
                        .header("sign", signature.get("sign").toString())
                        .timeout(HTTP_TIME_OUT)
                        .execute().body();
                JSONObject responseResult = JSONObject.parseObject(result);
                //成功
                if (SUCCEED_STATE.equals(responseResult.getString(CODE))) {
                    //记录结果，用于推送数据
                    List<BTm01Entity> ids = new ArrayList<>();
                    //json转对象
                    String data = responseResult.getString(DATA);
                    List<TeamMasterInfo> teamMasterInfoList = JSONObject.parseArray(data, TeamMasterInfo.class);
                    //查询当前数据库的该项目的参建单位信息
                    List<TeamMasterInfo> teamMasterInfos = tm01Service.findListByProjectCode(requestBodyDto.getPj0101());
                    Map<String, TeamMasterInfo> teamMasterInfoMap = teamMasterInfos.stream()
                            .collect(Collectors.toMap(TeamMasterInfo::getTeamsysno, info -> info, (existing, replacement) -> existing));
                    //对比数据
                    for(TeamMasterInfo teamMasterInfo: teamMasterInfoList){
                        BTm01Entity bTm01Entity = BeanUtil.copyProperties(teamMasterInfo, BTm01Entity.class);
                        //对比后存在则修改不存在则新增
                        if(teamMasterInfoMap.containsKey(bTm01Entity.getTeamsysno())){
                            bTm01Entity.setTm0101(teamMasterInfoMap.get(bTm01Entity.getTeamsysno()).getTm0101());
                            bTm01Entity.setPj0101(requestBodyDto.getPj0101());
                            bTm01Entity.setCp0201(teamMasterInfoMap.get(bTm01Entity.getTeamsysno()).getCp0201());
                            tm01Service.updateById(bTm01Entity);
                        }else {
                            bTm01Entity.setPj0101(requestBodyDto.getPj0101());
                            bTm01Entity.setCp0201(requestBodyDto.getCp0201());
                            tm01Service.insert(bTm01Entity);
                        }
                        BTm01Entity bTm01 = tm01Service.selectById(bTm01Entity.getTm0101());
                        ids.add(bTm01);
                    }
                    entity.setStatus(DATA_SUCCEED_STATE);
                    //上报数据推送
                    for(BTm01Entity tm01Entity: ids){
                        sendDataUtil.sendData(tm01Entity);
                    }
                } else {
                    entity.setStatus(DATA_FAILURE_STATE);
                    entity.setExceptionInfo("班组查询失败");
                }
            } catch (Exception e) {
                entity.setStatus(DATA_FAILURE_STATE);
                entity.setExceptionInfo(e.getMessage());
                logger.error("企业信息上报出现异常,异常信息:" + e.getMessage());
            } finally {
                try {
                    Thread.sleep(2000);
                    entity.setRequestUrl(httpUrl);
                    entity.setRequestMethod("POST");
                    entity.setCreationTime(DateUtil.date());
                    entity.setPj0101(requestBodyDto.getPj0101());
                    ybLogRequestService.insert(entity);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 查询管理人员信息
     */
    @Scheduled(initialDelay = 1000 * 10, fixedRate = 1000 * 60 * 60 * 3)
    public void receptManagerInfo() {
        String httpUrl = url + "/api/v1/getManagers";
        //查询需要查询班组的项目
        List<RequestBodyDto> list = ybCodeParamsService.selectProjectParamsByCorp();
        for (RequestBodyDto requestBodyDto : list) {
            logger.info("开始获取管理人员数据{}", requestBodyDto.getProjectcode());
            YbLogRequestEntity entity = new YbLogRequestEntity();
            try {
                //获取最新的工人数据时间
                String lastTime = bPs04Service.findLastTime(requestBodyDto.getPj0101(), requestBodyDto.getCp0201());
                //签名请求数据
                Map<String, Object> signature = ApiSignUtils.checkSignature();
                Map<String, Object> date = new HashMap<>();
                date.put("projectcode", requestBodyDto.getProjectcode());
                date.put("corpcode", requestBodyDto.getCorpcode());
                if(StringUtils.isNotBlank(lastTime)){
                    date.put("updateDate", lastTime);
                }
                entity.setRequestBody(date.toString());
                String result = HttpUtil.createPost(httpUrl)
                        .body(JSONObject.toJSONString(date))
                        .header("appkey", signature.get("appkey").toString())
                        .header("nonce", signature.get("nonce").toString())
                        .header("timestamp", signature.get("timestamp").toString())
                        .header("sign", signature.get("sign").toString())
                        .timeout(HTTP_TIME_OUT)
                        .execute().body();
                JSONObject responseResult = JSONObject.parseObject(result);
                entity.setResponseBody(responseResult.toJSONString());
                //成功
                if (SUCCEED_STATE.equals(responseResult.getString(CODE))) {
                    //记录结果，用于推送数据
                    List<BPs04DTO> ids = new ArrayList<>();
                    //json转对象
                    String data = responseResult.getString(DATA);
                    List<ManagerInfo> managerInfoList = JSONObject.parseArray(data, ManagerInfo.class);
                    //字典转换
                    DictConvertHandler.batchTranslateDict(managerInfoList);
                    //查询当前数据库的该项目的参建单位信息
                    List<ManagerInfo> managerInfos = bPs04Service.findListByProjectCode(requestBodyDto.getPj0101());
                    Map<Long, ManagerInfo> managerInfoMap = managerInfos.stream()
                            .collect(Collectors.toMap(ManagerInfo::getUserId, info -> info, (existing, replacement) -> existing));
                    //对比数据
                    for(ManagerInfo managerInfo: managerInfoList){
                        BPs01Entity bPs01Entity = BeanUtil.copyProperties(managerInfo, BPs01Entity.class);
                        BPs04Entity bPs04Entity = BeanUtil.copyProperties(managerInfo, BPs04Entity.class);
                        //对比后存在则修改不存在则新增
                        if(managerInfoMap.containsKey(managerInfo.getUserId())){
                            bPs01Entity.setPs0101(managerInfoMap.get(managerInfo.getUserId()).getPs0101());
                            //修改管理人员信息
                            bPs04Entity.setPs0401(managerInfoMap.get(managerInfo.getUserId()).getPs0401());
                            bPs04Service.updateById(bPs04Entity);
                        }else {
                            //保存基础人员信息
                            BPs01Entity bPs01 = bPs01Service.selectByIdnumer(bPs01Entity.getIdcardnumber());
                            if(bPs01 == null){
                                bPs01Service.insert(bPs01Entity);
                            }else {
                                bPs01Entity = bPs01;
                            }
                            //保存管理人员信息
                            bPs04Entity.setPs0101(bPs01Entity.getPs0101());
                            bPs04Entity.setPj0101(requestBodyDto.getPj0101());
                            bPs04Entity.setCp0201(requestBodyDto.getCp0201());
                            bPs04Service.insert(bPs04Entity);
                            //保存关联表
                            YbStaffEntity ybStaffEntity = new YbStaffEntity();
                            ybStaffEntity.setUserid(managerInfo.getUserId());
                            ybStaffEntity.setType("2");
                            ybStaffEntity.setPj0101(requestBodyDto.getPj0101());
                            ybStaffEntity.setWorknumber(bPs04Entity.getPs0401());
                            ybStaffService.insert(ybStaffEntity);
                        }
                        //记录结果
                        BPs01Entity bPs01 = bPs01Service.selectById(bPs01Entity.getPs0101());
                        BPs01DTO bPs01DTO = BeanUtil.copyProperties(bPs01, BPs01DTO.class);
                        BPs04Entity bPs04 = bPs04Service.selectById(bPs04Entity.getPs0401());
                        BPs04DTO bPs04DTO = BeanUtil.copyProperties(bPs04, BPs04DTO.class);
                        bPs04DTO.setPs01DTO(bPs01DTO);
                        ids.add(bPs04DTO);
                    }
                    entity.setStatus(DATA_SUCCEED_STATE);
                    //上报数据推送
                    for(BPs04DTO bPs04DTO: ids){
                        sendDataUtil.sendData(bPs04DTO);
                    }
                } else {
                    entity.setStatus(DATA_FAILURE_STATE);
                    entity.setExceptionInfo("管理人员查询失败");
                }
            } catch (Exception e) {
                entity.setStatus(DATA_FAILURE_STATE);
                entity.setExceptionInfo(e.getMessage());
                logger.error("管理人员查询出现异常,异常信息:" + e.getMessage());
            } finally {
                try {
                    Thread.sleep(2000);
                    entity.setCreationTime(DateUtil.date());
                    entity.setRequestUrl(httpUrl);
                    entity.setRequestMethod("POST");
                    entity.setPj0101(requestBodyDto.getPj0101());
                    ybLogRequestService.insert(entity);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 查询工人信息
     */
    @Scheduled(initialDelay = 1000 * 10, fixedRate = 1000 * 60 * 60 * 3)
    public void receptWorkerInfo() {
        String httpUrl = url + "/api/v1/getWorkers";
        //查询需要查询班组的项目
        List<RequestBodyDto> list = ybCodeParamsService.selectProjectParamsByTeam();
        for (RequestBodyDto requestBodyDto : list) {
            logger.info("开始获取工人信息数据{}", requestBodyDto.getProjectcode());
            YbLogRequestEntity entity = new YbLogRequestEntity();
            try {
                //获取最新的工人数据时间
                String lastTime = bPs02Service.findLastTime(requestBodyDto.getPj0101(), requestBodyDto.getTm0101());
                //签名请求数据
                Map<String, Object> date = new HashMap<>();
                date.put("projectcode", requestBodyDto.getProjectcode());
                date.put("corpcode", requestBodyDto.getCorpcode());
                date.put("teamsysno", requestBodyDto.getTeamsysno());
                date.put("page", 1);
                if (StringUtils.isNotBlank(lastTime)){
                    date.put("updateDate", lastTime);
                }
                entity.setRequestBody(date.toString());
                //查询工人信息
                JSONObject responseResult = ps02doPost(httpUrl, date);
                JSONObject jsonObject = responseResult.getJSONObject(DATA);
                entity.setResponseBody(jsonObject.toJSONString());
                Integer pages = jsonObject.getInteger("pages");
                //分页查询
                for (int i = 1; i <= pages; i++) {
                    date.put("page", i);
                    JSONObject result = ps02doPost(httpUrl, date);
                    if (SUCCEED_STATE.equals(result.getString(CODE))) {
                        //记录结果，用于推送数据
                        List<BPs02DTO> ids = workerInfoHandle(result, requestBodyDto);
                        entity.setStatus(DATA_SUCCEED_STATE);
                        //上报数据推送
                        for(BPs02DTO bPs02DTO: ids){
                            sendDataUtil.sendData(bPs02DTO);
                        }
                    }
                }
                entity.setStatus(DATA_SUCCEED_STATE);
                entity.setExceptionInfo("人员信息查询成功");
            } catch (Exception e) {
                entity.setStatus(DATA_FAILURE_STATE);
                entity.setExceptionInfo(e.getMessage());
                logger.error("人员信息查询出现异常,异常信息:" + e.getMessage());
            } finally {
                try {
                    Thread.sleep(2000);
                    entity.setRequestUrl(httpUrl);
                    entity.setRequestMethod("POST");
                    entity.setCreationTime(DateUtil.date());
                    entity.setPj0101(requestBodyDto.getPj0101());
                    ybLogRequestService.insert(entity);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public JSONObject ps02doPost(String httpUrl, Map<String, Object> date) {
        Map<String, Object> signature = ApiSignUtils.checkSignature();
        String result = HttpUtil.createPost(httpUrl)
                .body(JSONObject.toJSONString(date))
                .header("appkey", signature.get("appkey").toString())
                .header("nonce", signature.get("nonce").toString())
                .header("timestamp", signature.get("timestamp").toString())
                .header("sign", signature.get("sign").toString())
                .timeout(HTTP_TIME_OUT)
                .execute().body();
        return JSONObject.parseObject(result);
    }

    private List<BPs02DTO> workerInfoHandle(JSONObject responseResult, RequestBodyDto requestBodyDto){
        //记录结果，用于推送数据
        List<BPs02DTO> ids = new ArrayList<>();
        //json转对象
        JSONObject jsonObject = responseResult.getJSONObject(DATA);
        String records = jsonObject.getString(RECORDS);
        List<WorkerInfo> workerInfoList = JSONObject.parseArray(records, WorkerInfo.class);
        //字典转换
        DictConvertHandler.batchTranslateDict(workerInfoList);
        //查询当前数据库的该项目的参建单位信息
        List<WorkerInfo> workerInfos = bPs02Service.findListByProjectCode(requestBodyDto.getPj0101());
        Map<Long, WorkerInfo> managerInfoMap = workerInfos.stream()
                .collect(Collectors.toMap(WorkerInfo::getUserId, info -> info, (existing, replacement) -> existing));
        //对比数据
        for(WorkerInfo workerInfo: workerInfoList){
            BPs01Entity bPs01Entity = BeanUtil.copyProperties(workerInfo, BPs01Entity.class);
            BPs02Entity bPs02Entity = BeanUtil.copyProperties(workerInfo, BPs02Entity.class);
            //对比后存在则修改不存在则新增
            if(managerInfoMap.containsKey(workerInfo.getUserId())){
                bPs01Entity.setPs0101(managerInfoMap.get(workerInfo.getUserId()).getPs0101());
                bPs02Entity.setPs0201(managerInfoMap.get(workerInfo.getUserId()).getPs0201());
                bPs02Service.updateById(bPs02Entity);
            }else {
                //保存工人信息
                //保存基础人员信息
                BPs01Entity bPs01 = bPs01Service.selectByIdnumer(bPs01Entity.getIdcardnumber());
                if(bPs01 == null){
                    bPs01Service.insert(bPs01Entity);
                }else {
                    bPs01Entity = bPs01;
                }
                bPs02Entity.setPs0101(bPs01Entity.getPs0101());
                bPs02Entity.setPj0101(requestBodyDto.getPj0101());
                bPs02Entity.setTm0101(requestBodyDto.getTm0101());
                bPs02Service.insert(bPs02Entity);
                //保存关联表
                YbStaffEntity ybStaffEntity = new YbStaffEntity();
                ybStaffEntity.setUserid(workerInfo.getUserId());
                ybStaffEntity.setType("1");
                ybStaffEntity.setPj0101(requestBodyDto.getPj0101());
                ybStaffEntity.setWorknumber(bPs02Entity.getPs0201());
                ybStaffService.insert(ybStaffEntity);
            }
            BPs01Entity bPs01 = bPs01Service.selectById(bPs01Entity.getPs0101());
            BPs01DTO bPs01DTO = BeanUtil.copyProperties(bPs01, BPs01DTO.class);
            BPs02Entity bPs02 = bPs02Service.selectById(bPs02Entity.getPs0201());
            BPs02DTO bPs02DTO = BeanUtil.copyProperties(bPs02, BPs02DTO.class);
            bPs02DTO.setPs01DTO(bPs01DTO);
            ids.add(bPs02DTO);
        }
        return ids;
    }

    /**
     * 查询考勤信息
     */
    @Scheduled(initialDelay = 1000 * 60, fixedRate = 1000 * 60 * 10)
    public void receptAttendanceInfo() {
        String httpUrl = url + "/api/v1/getAttendances";
        //查询需要查询班组的项目
        List<RequestBodyDto> list = ybCodeParamsService.selectProjectParams();
        for (RequestBodyDto requestBodyDto : list) {
            logger.info("开始获取考勤数据{}", requestBodyDto.getProjectcode());
            try {
                //获取最新的工人数据时间
                Date lastTime = bKq02Service.findLastTime(requestBodyDto.getPj0101());
                Date lastEndTime = DateUtil.offsetDay(lastTime, 1);
                //设置查询参数
                Map<String, Object> date = new HashMap<>();
                date.put("projectcode", requestBodyDto.getProjectcode());
                date.put("startdate", DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"));
                date.put("enddate", DateUtil.format(lastEndTime, "yyyy-MM-dd HH:mm:ss"));
                //提取数据库内存在的人员
                List<WorkerAttendanceInfo> attendanceInfos = bKq02Service.getListUserIdByPj0101(requestBodyDto.getPj0101());
                Map<Long, WorkerAttendanceInfo> userIdMap = attendanceInfos.stream()
                        .collect(Collectors.toMap(WorkerAttendanceInfo::getUserId, info -> info, (existing, replacement) -> existing));
                //计算距今天数，开始遍历查询
                long l = DateUtil.betweenDay(lastTime, DateUtil.date(), true);
                for(int i = 0; i <= l; i++) {
                    logger.info("开始获取考勤数据{}", lastTime);
                    YbLogRequestEntity entity = new YbLogRequestEntity();
                    JSONObject result = HttpUtils.doPost(httpUrl, date);
                    entity.setRequestBody(date.toString());
                    //成功
                    if (SUCCEED_STATE.equals(result.getString(CODE))) {
                        //记录结果，用于推送数据
                        List<BKq02DTO> ids = new ArrayList<>();
                        //json转对象
                        String data = result.getString(DATA);
                        List<WorkerAttendanceInfo> workerAttendanceInfoList = JSONObject.parseArray(data, WorkerAttendanceInfo.class);
                        //考勤数据不需要比对，直接插入
                        for (WorkerAttendanceInfo workerAttendanceInfo : workerAttendanceInfoList) {
                            //比对是否存在系统内
                            if (userIdMap.containsKey(workerAttendanceInfo.getUserId())) {
                                BKq02Entity bKq02Entity = BeanUtil.copyProperties(workerAttendanceInfo, BKq02Entity.class);
                                bKq02Entity.setPj0101(requestBodyDto.getPj0101());
                                bKq02Entity.setUserId(userIdMap.get(workerAttendanceInfo.getUserId()).getWorknumber());
                                bKq02Service.insert(bKq02Entity);
                                //记录结果
                                BKq02DTO bKq02DTO = BeanUtil.copyProperties(bKq02Entity, BKq02DTO.class);
                                ids.add(bKq02DTO);
                            } else {
                                //不存在系统内的话存入失败表中
                                YbKq02FailEntity ybKq02FailEntity = new YbKq02FailEntity();
                                BeanUtil.copyProperties(workerAttendanceInfo, ybKq02FailEntity);
                                ybKq02FailEntity.setPj0101(requestBodyDto.getPj0101());
                                ybKq02FailService.insert(ybKq02FailEntity);
                            }
                        }
                        entity.setStatus(DATA_SUCCEED_STATE);
                        //设置下一次查询参数，查询+1天数据
                        lastTime = DateUtil.offsetDay(lastTime, 1);
                        lastEndTime = DateUtil.offsetDay(lastTime, 1);
                        date.put("startdate", DateUtil.format(lastTime, "yyyy-MM-dd HH:mm:ss"));
                        date.put("enddate", DateUtil.format(lastEndTime, "yyyy-MM-dd HH:mm:ss"));
                        //上报数据推送
                        for (BKq02DTO bKq02DTO : ids) {
                            sendDataUtil.sendData(bKq02DTO);
                        }
                    }
                    try {
                        entity.setRequestUrl(httpUrl);
                        entity.setRequestMethod("POST");
                        entity.setCreationTime(DateUtil.date());
                        entity.setStatus("1");
                        entity.setPj0101(requestBodyDto.getPj0101());
                        ybLogRequestService.insert(entity);
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            } catch (Exception e) {
                logger.error("考勤信息查询出现异常,异常信息:" + e.getMessage());
            } finally {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 数据补偿
     */
//    @Scheduled(initialDelay = 1000 * 60, fixedRate = 1000 * 60 * 60)
    public void receptRetryInfo() {
        List<RequestBodyDto> list = ybCodeParamsService.selectProjectParams();
        for (RequestBodyDto requestBodyDto : list) {
            logger.info("开始处理失败数据{}", requestBodyDto.getProjectcode());
            try {
                List<YbKq02FailEntity> ybKq02FailEntityList = ybKq02FailService.list(requestBodyDto.getPj0101());
                List<WorkerAttendanceInfo> attendanceInfos = bKq02Service.getListUserIdByPj0101(requestBodyDto.getPj0101());
                Map<Long, WorkerAttendanceInfo> userIdMap = attendanceInfos.stream()
                        .collect(Collectors.toMap(WorkerAttendanceInfo::getUserId, info -> info, (existing, replacement) -> existing));
                //记录结果，用于推送数据
                List<BKq02DTO> ids = new ArrayList<>();
                //json转对象
                //考勤数据不需要比对，直接插入
                for(YbKq02FailEntity ybKq02FailEntity: ybKq02FailEntityList){
                    //判断人员是否存在系统内
                    BKq02Entity bKq02Entity = BeanUtil.copyProperties(ybKq02FailEntity, BKq02Entity.class);
                    if(userIdMap.containsKey(ybKq02FailEntity.getUserId())){
                        bKq02Entity.setPj0101(requestBodyDto.getPj0101());
                        bKq02Entity.setUserId(userIdMap.get(ybKq02FailEntity.getUserId()).getUserId());
                        bKq02Service.insert(bKq02Entity);
                        //标记已经处理
                        ybKq02FailEntity.setStatus("1");
                        ybKq02FailService.updateById(ybKq02FailEntity);
                        //记录结果
                        BKq02DTO bKq02DTO = BeanUtil.copyProperties(bKq02Entity, BKq02DTO.class);
                        ids.add(bKq02DTO);
                    } else {
                        //记录重试次数
                        ybKq02FailEntity.setRetrycount(ybKq02FailEntity.getRetrycount() + 1);
                        ybKq02FailService.updateById(ybKq02FailEntity);
                    }
                }
                //上报数据推送
                for(BKq02DTO bKq02DTO: ids){
                    sendDataUtil.sendData(bKq02DTO);
                }
            } catch (Exception e) {
                logger.error("处理失败数据出现异常,异常信息:" + e.getMessage());
            }
        }
    }

}
