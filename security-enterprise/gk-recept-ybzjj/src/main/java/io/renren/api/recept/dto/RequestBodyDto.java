package io.renren.api.recept.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "请求参数")
public class RequestBodyDto {

    @ApiModelProperty(value = "项目编码")
    private String projectcode;

    @ApiModelProperty(value = "项目编码")
    private Long pj0101;

    @ApiModelProperty(value = "社会统一信用代码")
    private String corpcode;

    @ApiModelProperty(value = "参建单位编号")
    private Long cp0201;

    @ApiModelProperty(value = "班组id")
    private Long tm0101;

    @ApiModelProperty(value = "班组号")
    private Long teamsysno;

}
