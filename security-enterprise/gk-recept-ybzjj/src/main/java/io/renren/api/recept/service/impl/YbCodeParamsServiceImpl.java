package io.renren.api.recept.service.impl;

import io.renren.api.recept.dao.YbCodeParamsDao;
import io.renren.api.recept.dto.RequestBodyDto;
import io.renren.api.recept.entity.YbCodeParamsEntity;
import io.renren.api.recept.service.YbCodeParamsService;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * ${comments}
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Service
public class YbCodeParamsServiceImpl extends BaseServiceImpl<YbCodeParamsDao, YbCodeParamsEntity> implements YbCodeParamsService {

    @Override
    public List<RequestBodyDto> selectProjectParams() {
        return baseDao.selectProjectParams();
    }

    @Override
    public List<RequestBodyDto> selectProjectParamsByCorp() {
        return baseDao.selectProjectParamsByCorp();
    }

    @Override
    public List<RequestBodyDto> selectProjectParamsByTeam() {
        return baseDao.selectProjectParamsByTeam();
    }

}