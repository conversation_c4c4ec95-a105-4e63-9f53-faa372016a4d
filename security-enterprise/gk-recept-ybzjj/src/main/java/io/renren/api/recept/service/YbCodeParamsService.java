package io.renren.api.recept.service;

import io.renren.api.recept.dto.RequestBodyDto;
import io.renren.api.recept.entity.YbCodeParamsEntity;
import io.renren.common.service.BaseService;

import java.util.List;

/**
 * ${comments}
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
public interface YbCodeParamsService extends BaseService<YbCodeParamsEntity> {

    /**
     * 按照项目查询请求参数
     * @return
     */
    List<RequestBodyDto> selectProjectParams();

    /**
     * 按照企业查询请求参数
     * @return
     */
    List<RequestBodyDto> selectProjectParamsByCorp();

    /**
     * 按照班组查询请求参数
     * @return
     */
    List<RequestBodyDto> selectProjectParamsByTeam();
}