package io.renren.modules.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.api.recept.dao.BTm01Dao;
import io.renren.api.recept.entity.BTm01Entity;
import io.renren.api.recept.service.BTm01Service;
import io.renren.api.report.dto.TeamMasterInfo;
import io.renren.common.service.impl.BaseServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Service
public class BTm01ServiceImpl extends BaseServiceImpl<BTm01Dao, BTm01Entity> implements BTm01Service {

    @Override
    public List<TeamMasterInfo> findListByProjectCode(Long pj0101) {
        return baseDao.findListByProjectCode(pj0101);
    }

}