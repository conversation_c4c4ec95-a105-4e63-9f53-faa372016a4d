package io.renren.api.recept.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.iscitizenidvalidator.IsCitizenIdValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 项目参建单位信息数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-08
 */
@Data
@ApiModel(value = "项目参建单位信息数据表")
public class BCp02DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long cp0201;

    @ApiModelProperty(value = "企业ID")
    private Long cp0101;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "参建类型_Select选择器", required = true)
    @NotBlank(message = "参建类型不能为空")
    private String corptype;

    @ApiModelProperty(value = "项目经理")
    private String pmName;

    @ApiModelProperty(value = "证件类型")
    private String pmIdCardType;

    @ApiModelProperty(value = "证件号码")
    @IsCitizenIdValidator(message = "身份证号码格式不正确")
    private String pmIdCardNumber;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;

    @ApiModelProperty(value = "退场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exittime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

    @ApiModelProperty(value = "参加单位基础信息")
    @Valid
    private BCp01DTO cp01DTO;

}