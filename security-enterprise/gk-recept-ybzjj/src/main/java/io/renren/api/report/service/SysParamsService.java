/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.api.report.service;


import io.renren.api.report.entity.SysParamsEntity;
import io.renren.common.service.BaseService;


/**
 * 参数管理
 *
 * <AUTHOR> <PERSON>@gmail.com
 * @since 1.0.0
 */
public interface SysParamsService extends BaseService<SysParamsEntity> {


    /**
     * 根据参数编码，获取参数的value值
     *
     * @param paramCode  参数编码
     */
    String getValue(String paramCode);

    /**
     * 根据参数编码，获取value的Object对象
     * @param paramCode  参数编码
     * @param clazz  Object对象
     */
    <T> T getValueObject(String paramCode, Class<T> clazz);


}
