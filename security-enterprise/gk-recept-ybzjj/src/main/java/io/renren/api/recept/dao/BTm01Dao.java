package io.renren.api.recept.dao;

import io.renren.api.recept.entity.BTm01Entity;
import io.renren.api.report.dto.TeamMasterInfo;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 班组基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Mapper
public interface BTm01Dao extends BaseDao<BTm01Entity> {

    /**
     * 根据项目code查询班组信息
     * @param pj0101
     * @return
     */
    List<TeamMasterInfo> findListByProjectCode(Long pj0101);
}