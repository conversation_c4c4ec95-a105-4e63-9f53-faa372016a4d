package io.renren.api.recept.service;

import io.renren.api.recept.entity.BPs02Entity;
import io.renren.api.report.dto.WorkerInfo;
import io.renren.common.service.BaseService;

import java.util.List;

/**
 * 建筑工人信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
public interface BPs02Service extends BaseService<BPs02Entity> {

    /**
     * 查询最后一次时间
     * @param pj0101
     * @return
     */
    String findLastTime(Long pj0101, Long tm0101);

    /**
     * 根据项目编码查询建筑工人信息
     * @param pj0101
     * @return
     */
    List<WorkerInfo> findListByProjectCode(Long pj0101);
}