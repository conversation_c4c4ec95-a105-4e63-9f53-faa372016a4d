package io.renren.api.recept.service;

import io.renren.api.recept.entity.BKq02Entity;
import io.renren.api.report.dto.WorkerAttendanceInfo;
import io.renren.common.service.BaseService;

import java.util.Date;
import java.util.List;

/**
 * 考勤记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
public interface BKq02Service extends BaseService<BKq02Entity> {

    Date findLastTime(Long pj0101);

    /**
     * 获取项目人员id(管理人员和工人)
     * @param pj0101
     * @return
     */
    List<WorkerAttendanceInfo> getListUserIdByPj0101(Long pj0101);

}