package io.renren.api.recept.service;

import io.renren.api.recept.entity.BCp02Entity;
import io.renren.api.report.dto.CompanyInfo;
import io.renren.common.service.BaseService;

import java.util.List;

/**
 * 参建单位信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
public interface BCp02Service extends BaseService<BCp02Entity> {

    /**
     * 根据项目编码查询企业信息
     * @param pj0101
     * @return
     */
    List<CompanyInfo> findListByProjectCode(Long pj0101);

}