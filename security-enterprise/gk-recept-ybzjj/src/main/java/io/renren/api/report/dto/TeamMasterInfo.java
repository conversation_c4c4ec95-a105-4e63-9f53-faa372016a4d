package io.renren.api.report.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目班组信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-31
 */
@Data
@ApiModel(value = "五冶上报班组信息")
public class TeamMasterInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目code")
    @JSONField(serialize = false)
    private String projectCode;

    @JSONField(serialize = false)
    private Long tm0101;

	@JSONField(serialize = false)
	private Long pj0101;

    @JSONField(serialize = false)
    private Long cp0201;

    @ApiModelProperty(value = "统一社会信用代码", required = true)
    private String corpcode;

    @ApiModelProperty(value = "班组名称", required = true)
    private String teamname;

    @ApiModelProperty(value = "班组编号", required = true)
    private String teamsysno;

    @ApiModelProperty(value = "责任人", required = true)
    private String responsiblepersonname;

    @ApiModelProperty(value = "责任人联系电话", required = true)
    private String responsiblepersonphone;

    @ApiModelProperty(value = "证件类型", required = true)
    private String idcardtype;

    @ApiModelProperty(value = "证件号码", required = true)
    private String responsiblepersonidnumber;

    @ApiModelProperty(value = "进场时间", required = true)
    private Date entrytime;

    @ApiModelProperty(value = "退场时间", required = true)
    private Date exittime;

    @ApiModelProperty(value = "进退场状态", required = true)
    private String inOrOut;
}