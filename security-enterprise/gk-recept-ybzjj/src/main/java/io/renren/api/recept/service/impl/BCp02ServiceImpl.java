package io.renren.api.recept.service.impl;

import io.renren.api.recept.dao.BCp02Dao;
import io.renren.api.recept.entity.BCp02Entity;
import io.renren.api.recept.service.BCp02Service;
import io.renren.api.report.dto.CompanyInfo;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 参建单位信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Service
public class BCp02ServiceImpl extends BaseServiceImpl<BCp02Dao, BCp02Entity> implements BCp02Service {


    @Override
    public List<CompanyInfo> findListByProjectCode(Long pj0101) {
        return baseDao.findListByProjectCode(pj0101);
    }
}