package io.renren.api.report.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.annotation.DictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-31
 */
@Data
@ApiModel(value = "工人信息")
public class WorkerInfo implements Serializable{

	private static final long serialVersionUID = 1L;

	@JSONField(serialize = false)
    private Long ps0201;

    @JSONField(serialize = false)
    private Long ps0101;

	@JSONField(serialize = false)
	private Long pj0101;

    @JSONField(serialize = false)
    private Long tm0101;

	@ApiModelProperty(value = "项目编号")
    private String projectcode;

    @ApiModelProperty(value = "统一社会信用代码", required = true)
    private String corpcode;

    @ApiModelProperty(value = "班组编号", required = true)
    private String teamsysno;

    @ApiModelProperty(value = "人员姓名", required = true)
    private String name;

    @ApiModelProperty(value = "证件类型", required = true)
    private String idcardtype;

    @ApiModelProperty(value = "证件号码", required = true)
    private String idcardnumber;

    @ApiModelProperty(value = "性别", required = true)
    private String gender;

    @ApiModelProperty(value = "民族", required = true)
    @DictConvert(fieldType = "NATION")
    private String nation;

    @ApiModelProperty(value = "出生日期", required = true)
    private String birthday;

    @ApiModelProperty(value = "住址", required = true)
    private String address;

    @ApiModelProperty(value = "学历", required = true)
    private String edulevel;

    @ApiModelProperty(value = "籍贯", required = true)
    private Long areacode;

    @ApiModelProperty(value = "头像", required = true)
    private String headimageurl;

    @ApiModelProperty(value = "政治面貌", required = true)
    private String politicstype;

    @ApiModelProperty(value = "手机号码", required = true)
    private String cellphone;

    @ApiModelProperty(value = "文化程度", required = true)
    private String cultureleveltype;

    @ApiModelProperty(value = "发证机关", required = true)
    private String grantorg;

    @ApiModelProperty(value = "正面照 URL", required = true)
    private String positiveidcardimageurl;

    @ApiModelProperty(value = "反面照 URL", required = true)
    private String negativeidcardimageurl;

    @ApiModelProperty(value = "有效期开始日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startdate;

    @ApiModelProperty(value = "有效期结束日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expirydate;

    @ApiModelProperty(value = "是否班组长", required = true)
    private String isteamleader;

    @ApiModelProperty(value = "工种", required = true)
    @DictConvert(fieldType = "WORKTYPECODE")
    private String worktypecode;

    @ApiModelProperty(value = "采集照片", required = true)
    private String issuecardpicurl;

    @ApiModelProperty(value = "进场时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entrytime;

    @ApiModelProperty(value = "退场时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exittime;

    @ApiModelProperty(value = "进退场状态", required = true)
    private String inOrOut;

    @ApiModelProperty(value = "人员工号", required = true)
    private Long userId;

    @ApiModelProperty(value = "更新时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

}
