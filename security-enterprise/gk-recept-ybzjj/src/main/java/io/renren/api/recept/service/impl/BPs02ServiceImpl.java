package io.renren.modules.api.service.impl;

import io.renren.api.recept.dao.BPs02Dao;
import io.renren.api.recept.entity.BPs02Entity;
import io.renren.api.recept.service.BPs02Service;
import io.renren.api.report.dto.WorkerInfo;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 建筑工人信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Service
public class BPs02ServiceImpl extends BaseServiceImpl<BPs02Dao, BPs02Entity> implements BPs02Service {

    @Override
    public String findLastTime(Long pj0101, Long tm0101) {
        return baseDao.findLastTime(pj0101, tm0101);
    }

    @Override
    public List<WorkerInfo> findListByProjectCode(Long pj0101) {
        return baseDao.findListByProjectCode(pj0101);
    }
}