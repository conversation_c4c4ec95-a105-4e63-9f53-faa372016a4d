package io.renren.api.recept.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 班组基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_TM01")
public class BTm01Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long tm0101;
    /**
     * 参建单位ID
     */
	private Long cp0201;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 班组编号
     */
	private String teamsysno;
    /**
     * 班组名称
     */
	private String teamname;
    /**
     * 责任人姓名
     */
	private String responsiblepersonname;
    /**
     * 责任人联系电话
     */
	private String responsiblepersonphone;
    /**
     * 责任人证件类型
     */
	private String idcardtype;
    /**
     * 责任人证件号码
     */
	private String responsiblepersonidnumber;
    /**
     * 进场时间
     */
	private Date entrytime;
    /**
     * 退场时间
     */
	private Date exittime;
    /**
     * 进退场状态
     */
	private String inOrOut;
    /**
     * 备注
     */
	private String memo;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}