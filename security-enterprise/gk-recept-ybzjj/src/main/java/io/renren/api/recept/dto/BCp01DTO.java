package io.renren.api.recept.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 单位基本信息数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-08
 */
@Data
@ApiModel(value = "单位基本信息数据表")
public class BCp01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long cp0101;

    @ApiModelProperty(value = "社会统一信用代码")
    private String corpcode;

    @ApiModelProperty(value = "企业性质")
    private String organizationtype;

    @ApiModelProperty(value = "企业名称")
    private String corpname;

    @ApiModelProperty(value = "注册地区")
    private String areacode;

    @ApiModelProperty(value = "企业营业地址")
    private String address;

    @ApiModelProperty(value = "法定代表人姓名")
    private String legalman;

    @ApiModelProperty(value = "注册资本(万元)")
    private BigDecimal regcapital;

    @ApiModelProperty(value = "注册资本币种")
    private String capitalcurrencytype;

    @ApiModelProperty(value = "成立日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date establishdate;

    @ApiModelProperty(value = "联系人姓名")
    private String linkman;

    @ApiModelProperty(value = "联系人手机号码")
    private String linkcellphone;


    @ApiModelProperty(value = "企业经营状态")
    private String businessstatus;

    @ApiModelProperty(value = "企业经营范围")
    private String entscope;

    @ApiModelProperty(value = "参建类型")
    private String corptype;

}