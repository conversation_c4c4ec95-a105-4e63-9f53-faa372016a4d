package io.renren.api.recept.service.impl;

import io.renren.api.recept.dao.BKq02Dao;
import io.renren.api.recept.entity.BKq02Entity;
import io.renren.api.recept.service.BKq02Service;
import io.renren.api.report.dto.WorkerAttendanceInfo;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 考勤记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Service
public class BKq02ServiceImpl extends BaseServiceImpl<BKq02Dao, BKq02Entity> implements BKq02Service {

    @Override
    public Date findLastTime(Long pj0101) {
        return baseDao.findLastTime(pj0101);
    }

    @Override
    public List<WorkerAttendanceInfo> getListUserIdByPj0101(Long pj0101) {
        return baseDao.getListUserIdByPj0101(pj0101);
    }
}