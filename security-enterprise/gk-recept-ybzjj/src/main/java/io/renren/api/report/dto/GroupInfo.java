package io.renren.api.report.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/14 14:32
 */
@Data
@ApiModel(value = "五冶上报班组信息")
public class GroupInfo {

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "企业统一社会信用代码")
    private String companyCode;

    @ApiModelProperty(value = "班组名称")
    private String name;

    @ApiModelProperty(value = "班组长姓名")
    private String leaderName;

    @ApiModelProperty(value = "班组长身份证号")
    private String leaderIdentification;

    @ApiModelProperty(value = "班组长联系电话")
    private String leaderPhone;

    @ApiModelProperty(value = "班组类型:0:建筑工人班组,1:管理人员班组")
    private Integer type;

    @ApiModelProperty(value = "班组编码")
    private String groupCode;
}
