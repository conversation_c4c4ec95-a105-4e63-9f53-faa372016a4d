package io.renren.api.recept.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * ${comments}
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("YB_LOG_REQUEST")
public class YbLogRequestEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * id
     */
	@TableId
	private Long id;
    /**
     * 请求方法
     */
	private String requestUrl;
    /**
     * 请求类型
     */
	private String requestMethod;
    /**
     * 请求数据
     */
	private String requestBody;
    /**
     * 返回数据
     */
	private String responseBody;
    /**
     * 业务id
     */
	private Long pj0101;
    /**
     * 业务类型
     */
	private String businesstype;
    /**
     * 请求状态
     */
	private String status;
    /**
     * 保存状态
     */
	private String enteredstatus;
    /**
     * 错误信息
     */
	private String exceptionInfo;
    /**
     * 创建时间
     */
	private Date creationTime;

}