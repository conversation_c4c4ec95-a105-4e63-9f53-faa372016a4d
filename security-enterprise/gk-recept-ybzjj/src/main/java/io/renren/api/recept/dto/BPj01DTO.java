package io.renren.api.recept.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.ismobilevalidator.IsMobileValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 项目基本信息数据表   APP项目只做查看 不做新增修改
 *
 * <AUTHOR>
 * @since 1.0.0 2021-06-02
 */
@Data
@ApiModel(value = "项目基本信息数据表")
public class BPj01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pj0101;

    @ApiModelProperty(value = "安监备案号")
    private String safetyno;

    @ApiModelProperty(value = "项目编码")
    private String code;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "项目简介")
    private String description;

    @ApiModelProperty(value = "所属行业_Select选择器", required = true)
    private String industry;

    @ApiModelProperty(value = "项目类别_Select选择器", required = true)
    private String category;

    @ApiModelProperty(value = "建设性质_Select选择器", required = true)
    private String constructtype;

    @ApiModelProperty(value = "投资类型_Select选择器", required = true)
    private String investtype;

    @ApiModelProperty(value = "项目所在地_Cascader级联选择器", required = true)
    private String areacode;

    @ApiModelProperty(value = "建设地址", required = true, example = "成都市武侯区人民南路三段17号")
    private String address;

    @ApiModelProperty(value = "总面积(平方米)_InputNumber计数器", required = true)
    private BigDecimal buildingarea;

    @ApiModelProperty(value = "总长度(米)_InputNumber计数器")
    private BigDecimal buildinglength;

    @ApiModelProperty(value = "总投资(万元)_InputNumber计数器", required = true)
    private BigDecimal invest;

    @ApiModelProperty(value = "工程造价(万元)_InputNumber计数器")
    private BigDecimal engineering;

    @ApiModelProperty(value = "项目规模_Select选择器", required = true)
    private String scale;

    @ApiModelProperty(value = "开工日期", required = true, example = "yyyy-MM-dd")
    @NotNull(message = "开工日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startdate;

    @ApiModelProperty(value = "竣工日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completeDate;

    @ApiModelProperty(value = "经度")
    private BigDecimal lng;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lat;

    @ApiModelProperty(value = "联系人姓名", required = true)
    private String linkman;

    @ApiModelProperty(value = "联系人电话", required = true)
    @NotBlank(message = "联系人电话不能为空")
    @IsMobileValidator(message = "联系人电话请填写正确的手机号码")
    private String linkphone;

    @ApiModelProperty(value = "是否重点项目_Select选择器")
    private String ismajorProject;

    @ApiModelProperty(value = "是否缴纳保证金_Select选择器")
    private String isdeposit;

    @ApiModelProperty(value = "项目状态_Select选择器", required = true)
    @NotBlank(message = "项目状态不能为空")
    private String prjstatus;

    @ApiModelProperty(value = "机构ID", hidden = true, required = true)
    private Long deptId;

    @ApiModelProperty(value = "电子围栏区域")
    private String region;


}