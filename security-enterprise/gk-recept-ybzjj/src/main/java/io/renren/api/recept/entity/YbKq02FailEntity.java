package io.renren.api.recept.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 失败插入的考勤记录，补偿处理
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("YB_KQ02_FAIL")
public class YbKq02FailEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 人员ID
     */
	private Long userId;
    /**
     * 人员类型
     */
	private String personType;
    /**
     * 姓名
     */
	private String personName;
    /**
     * 考勤时间
     */
	private Date checkdate;
    /**
     * 进出方向
     */
	private String direction;
    /**
     * 通行方式
     */
	private String attendtype;
    /**
     * 刷卡近照
     */
	private String imageUrl;
    /**
     * 重试次数，达到三次就不在去补偿
     */
	private int retrycount;
    /**
     * ID
     */
	@TableId
	private Long id;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 状态
	 */
	private String status;

}