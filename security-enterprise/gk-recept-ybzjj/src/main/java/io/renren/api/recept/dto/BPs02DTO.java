package io.renren.api.recept.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 建筑工人数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-05
 */
@Data
@ApiModel(value = "建筑工人数据表")
public class BPs02DTO implements Serializable {
    @ApiModelProperty(value = "主键ID")
    private Long ps0201;

    @ApiModelProperty(value = "人员ID")
    private Long ps0101;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "人员基础信息")
    private BPs01DTO ps01DTO;

    @ApiModelProperty(value = "班组ID", required = true)
    @NotNull(message = "班组长不能为空")
    private Long tm0101;

    @ApiModelProperty(value = "是否班组长", required = true)
    @NotBlank(message = "是否班组长不能为空")
    private String isteamleader;

    @ApiModelProperty(value = "工种", required = true)
    @NotBlank(message = "工种信息不能为空")
    private String worktypecode;

    @ApiModelProperty(value = "工资卡帐号", required = true)
//    @NotBlank(message = "工资卡帐号不能为空")
    private String payrollbankcardnumber;

    @ApiModelProperty(value = "工资卡开户行名称", required = true)
//    @NotBlank(message = "工资卡开户行名称不能为空")
    private String payrollbankname;

    @ApiModelProperty(value = "工资卡银行代码", required = true)
//    @NotBlank(message = "工资卡银行代码不能为空")
    private String payrolltopbankcode;

    @ApiModelProperty(value = "工人头像")
    @NotBlank(message = "人员头像不能为空")
    private String issuecardpicurl;

    @ApiModelProperty(value = "是否购买工伤或意外伤害保险 ")
    private String hasbuyinsurance;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;

    @ApiModelProperty(value = "退场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exittime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "人员基础信息", required = true)
    @Valid
    private BPs01DTO bps01DTO;

    @ApiModelProperty(value = "创建时间")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Date createDate;
}