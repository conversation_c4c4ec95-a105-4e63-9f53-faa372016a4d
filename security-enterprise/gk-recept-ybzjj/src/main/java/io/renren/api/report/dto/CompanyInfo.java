package io.renren.api.report.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/7/14 13:08
 */
@Data
@ApiModel(value = "企业信息")
public class CompanyInfo {

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "单位名称", required = true)
    private String corpname;

    @ApiModelProperty(value = "统一社会信用代码", required = true)
    private String corpcode;

    @ApiModelProperty(value = "企业性质", required = true)
    private String organizationtype;

    @ApiModelProperty(value = "注册地区", required = true)
    private String areacode;

    @ApiModelProperty(value = "企业营业地址", required = true)
    private String address;

    @ApiModelProperty(value = "邮政编码", required = true)
    private String zipcode;

    @ApiModelProperty(value = "法定代表人姓名", required = true)
    private String legalman;

    @ApiModelProperty(value = "法定代表人证件类型", required = true)
    private String idcardtype;

    @ApiModelProperty(value = "法定代表人证件号码", required = true)
    private String legalmanidcardnumber;

    @ApiModelProperty(value = "注册资本(万元)", required = true)
    private BigDecimal regcapital;

    @ApiModelProperty(value = "注册资本币种", required = true)
    private String capitalcurrencytype;

    @ApiModelProperty(value = "成立日期", required = true)
    private String establishdate;

    @ApiModelProperty(value = "参建类型", required = true)
    private String corptype;

    @ApiModelProperty(value = "进退场状态", required = true)
    private String inOrOut;

    @ApiModelProperty(value = "进场时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entrytime;

    @ApiModelProperty(value = "退场时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exittime;

    @ApiModelProperty(value = "联系人姓名", required = true)
    private String linkman;

    @ApiModelProperty(value = "联系人办公电话", required = true)
    private String linkphone;

    @ApiModelProperty(value = "联系人手机号码", required = true)
    private String linkcellphone;

    @JSONField(serialize = false)
    private Long cp0201;

    @JSONField(serialize = false)
    private Long cp0101;

    @JSONField(serialize = false)
    private Long pj0101;
}
