package io.renren.api.recept.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ${comments}
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("YB_CODE_PARAMS")
public class YbCodeParamsEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * $column.comments
     */
	@TableId
	private Long pj0101;
    /**
     * $column.comments
     */
	private Long code;

}