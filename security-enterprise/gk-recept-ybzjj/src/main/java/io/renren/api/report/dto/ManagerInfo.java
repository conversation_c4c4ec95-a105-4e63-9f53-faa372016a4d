package io.renren.api.report.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.renren.annotation.DictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "管理人员信息")
public class ManagerInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @JSONField(serialize = false)
    private Long ps0401;

    @JSONField(serialize = false)
    private Long ps0101;

    @JSONField(serialize = false)
    private Long pj0101;

    @JSONField(serialize = false)
    private Long cp0201;

    @ApiModelProperty(value = "证件类型", required = true)
    private String idcardtype;

    @ApiModelProperty(value = "证件号码", required = true)
    private String idcardnumber;

    @ApiModelProperty(value = "性别", required = true)
    private String gender;

    @ApiModelProperty(value = "民族", required = true)
    @DictConvert(fieldType = "NATION")
    private String nation;

    @ApiModelProperty(value = "出生日期", required = true)
    private String birthday;

    @ApiModelProperty(value = "住址", required = true)
    private String address;

    @ApiModelProperty(value = "学历", required = true)
    private String edulevel;

    @ApiModelProperty(value = "籍贯", required = true)
    private BigDecimal areacode;

    @ApiModelProperty(value = "头像", required = true)
    private String headimageurl;

    @ApiModelProperty(value = "政治面貌", required = true)
    private String politicstype;

    @ApiModelProperty(value = "手机号码", required = true)
    private String cellphone;

    @ApiModelProperty(value = "文化程度", required = true)
    private String cultureleveltype;

    @ApiModelProperty(value = "发证机关", required = true)
    private String grantorg;

    @ApiModelProperty(value = "正面照 URL", required = true)
    private String positiveidcardimageurl;

    @ApiModelProperty(value = "反面照 URL", required = true)
    private String negativeidcardimageurl;

    @ApiModelProperty(value = "有效期开始日期", required = true)
    private Date startdate;

    @ApiModelProperty(value = "有效期结束日期", required = true)
    private Date expirydate;

    @ApiModelProperty(value = "岗位类型", required = true)
    @DictConvert(fieldType = "WORKTYPECODE")
    private String jobtype;

    @ApiModelProperty(value = "采集照片", required = true)
    private String photo;

    @ApiModelProperty(value = "进场时间", required = true)
    private Date entrytime;

    @ApiModelProperty(value = "退场时间", required = true)
    private Date exittime;

    @ApiModelProperty(value = "进退场状态", required = true)
    private String inOrOut;

    @ApiModelProperty(value = "人员工号", required = true)
    private Long userId;

    @ApiModelProperty(value = "更新时间", required = true)
    private Date updateDate;

    @ApiModelProperty(value = "人员姓名", required = true)
    private String name;

    @ApiModelProperty(value = "统一社会信用代码", required = true)
    private String corpcode;


}
