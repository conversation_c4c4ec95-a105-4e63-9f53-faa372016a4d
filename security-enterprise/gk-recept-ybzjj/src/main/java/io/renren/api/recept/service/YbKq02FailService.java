package io.renren.api.recept.service;


import io.renren.api.recept.entity.YbKq02FailEntity;
import io.renren.common.service.BaseService;

import java.util.List;

/**
 * 失败插入的考勤记录，补偿处理
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-29
 */
public interface YbKq02FailService extends BaseService<YbKq02FailEntity> {

    /**
     * 根据项目id查询失败的考勤记录
     * @param pj0101
     * @return
     */
    List<YbKq02FailEntity> list(Long pj0101);

}