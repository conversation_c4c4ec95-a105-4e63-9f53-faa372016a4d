package io.renren.api.recept.dao;

import io.renren.api.recept.entity.BKq02Entity;
import io.renren.api.report.dto.WorkerAttendanceInfo;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

/**
 * 考勤记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Mapper
public interface BKq02Dao extends BaseDao<BKq02Entity> {

    /**
     * 查询最后一次时间
     * @param pj0101
     * @return
     */
    Date findLastTime(Long pj0101);

    /**
     * 查询项目人员id（包括工人和管理人员）
     * @param pj0101
     * @return
     */
    List<WorkerAttendanceInfo> getListUserIdByPj0101(Long pj0101);
}