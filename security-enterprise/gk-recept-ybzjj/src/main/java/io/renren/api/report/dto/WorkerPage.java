package io.renren.api.report.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "工人信息分页")
public class WorkerPage {

    @ApiModelProperty(value = "总条数", required = true)
    private Integer total;

    @ApiModelProperty(value = "本页条数", required = true)
    private Integer size;

    @ApiModelProperty(value = "当前页", required = true)
    private Integer current;

    @ApiModelProperty(value = "总页数", required = true)
    private Integer pages;

    @ApiModelProperty(value = "工人列表")
    private List<WorkerInfo> records;

}
