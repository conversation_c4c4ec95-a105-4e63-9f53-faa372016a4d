package io.renren.api.recept.dao;

import io.renren.api.recept.dto.RequestBodyDto;
import io.renren.api.recept.entity.YbCodeParamsEntity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ${comments}
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Mapper
public interface YbCodeParamsDao extends BaseDao<YbCodeParamsEntity> {

    /**
     * 查询项目参数
     * @return
     */
    List<RequestBodyDto> selectProjectParams();

    /**
     * 查询企业参数
     * @return
     */
    List<RequestBodyDto> selectProjectParamsByCorp();

    /**
     * 查询班组参数
     * @return
     */
    List<RequestBodyDto> selectProjectParamsByTeam();
}