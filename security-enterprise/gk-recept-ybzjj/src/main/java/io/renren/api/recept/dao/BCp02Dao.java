package io.renren.api.recept.dao;

import io.renren.api.recept.entity.BCp02Entity;
import io.renren.api.report.dto.CompanyInfo;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 参建单位信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Mapper
public interface BCp02Dao extends BaseDao<BCp02Entity> {

    /**
     * 根据项目编码查询企业信息
     * @param pj0101
     * @return
     */
    List<CompanyInfo> findListByProjectCode(Long pj0101);

}