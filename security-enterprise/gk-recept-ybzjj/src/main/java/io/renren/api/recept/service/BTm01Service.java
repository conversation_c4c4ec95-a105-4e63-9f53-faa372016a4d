package io.renren.api.recept.service;

import io.renren.api.recept.entity.BTm01Entity;
import io.renren.api.report.dto.TeamMasterInfo;
import io.renren.common.service.BaseService;

import java.util.List;

/**
 * 班组基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
public interface BTm01Service extends BaseService<BTm01Entity> {

    /**
     * 根据项目code查询班组信息
     * @param projectcode
     * @return
     */
    List<TeamMasterInfo> findListByProjectCode(Long projectcode);

}