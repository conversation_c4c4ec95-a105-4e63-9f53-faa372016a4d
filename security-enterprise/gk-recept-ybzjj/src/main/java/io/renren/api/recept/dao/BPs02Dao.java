package io.renren.api.recept.dao;

import io.renren.api.recept.entity.BPs02Entity;
import io.renren.api.report.dto.WorkerInfo;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

/**
 * 建筑工人信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Mapper
public interface BPs02Dao extends BaseDao<BPs02Entity> {

    /**
     * 查询最后一次时间
     * @param pj0101
     * @return
     */
    String findLastTime(Long pj0101, Long tm0101);

    /**
     * 根据项目编码查询建筑工人信息
     * @param pj0101
     * @return
     */
    List<WorkerInfo> findListByProjectCode(Long pj0101);
}