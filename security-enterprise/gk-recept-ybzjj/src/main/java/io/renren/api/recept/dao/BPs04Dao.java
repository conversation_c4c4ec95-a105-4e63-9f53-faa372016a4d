package io.renren.api.recept.dao;

import io.renren.api.recept.entity.BPs04Entity;
import io.renren.api.report.dto.ManagerInfo;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目管理人员信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-28
 */
@Mapper
public interface BPs04Dao extends BaseDao<BPs04Entity> {

    /**
     * 根据项目编码查询项目管理人员信息
     * @param pj0101
     * @return
     */
    List<ManagerInfo> findListByProjectCode(Long pj0101);

    /**
     * 根据项目编码查询最后一次更新时间
     * @param pj0101
     * @return
     */
    String findLastTime(Long pj0101, Long cp0201);
}