package io.renren.api.report.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @date: 2023/8/3 16:58
 * @author: hanchao<PERSON>i
 */
@Data
@ApiModel(value = "五冶考勤核验信结果")
public class AsyncResultDTO {

    @ApiModelProperty(value = "项目编码")
    @JSONField(serialize = false)
    private String projectId;

    @ApiModelProperty(value = "异步查询编码")
    private String asyncRequestCode;

    @ApiModelProperty(value = "结果状态")
    @JSONField(serialize = false)
    private String success;

    @ApiModelProperty(value = "kq0201, 考勤编号")
    @JSONField(serialize = false)
    private Long curNumber;

    @ApiModelProperty(value = "返回消息")
    @JSONField(serialize = false)
    private String message;

}
