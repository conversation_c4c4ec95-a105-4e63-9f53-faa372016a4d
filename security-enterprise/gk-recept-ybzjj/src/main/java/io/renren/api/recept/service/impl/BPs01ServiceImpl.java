package io.renren.api.recept.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.api.recept.dao.BPs01Dao;
import io.renren.api.recept.entity.BPs01Entity;
import io.renren.api.recept.service.BPs01Service;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 人员实名基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Service
public class BPs01ServiceImpl extends BaseServiceImpl<BPs01Dao, BPs01Entity> implements BPs01Service {

    @Override
    public BPs01Entity selectByIdnumer(String idcardnumber) {
        return baseDao.selectOne(new QueryWrapper<BPs01Entity>().eq("idcardnumber", idcardnumber));
    }

}