package io.renren.api.recept.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.api.recept.dao.BCp01Dao;
import io.renren.api.recept.entity.BCp01Entity;
import io.renren.api.recept.service.BCp01Service;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 企业基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Service
public class BCp01ServiceImpl extends BaseServiceImpl<BCp01Dao, BCp01Entity> implements BCp01Service {


    @Override
    public BCp01Entity selectByCorpCode(String corpcode) {
        return baseDao.selectOne(new QueryWrapper<BCp01Entity>().eq("corpcode", corpcode));
    }
}