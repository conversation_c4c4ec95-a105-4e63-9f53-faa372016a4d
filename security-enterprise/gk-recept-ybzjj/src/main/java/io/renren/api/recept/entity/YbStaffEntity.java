package io.renren.api.recept.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 宜宾住建工作人员信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("YB_STAFF")
public class YbStaffEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 人员id
     */
	private Long userid;
    /**
     * 人员类型（1：工人，2：管理人员）
     */
	private String type;
    /**
     * 项目id
     */
	private Long pj0101;
	/**
     * 项目名称
     */
	private Long worknumber;

}