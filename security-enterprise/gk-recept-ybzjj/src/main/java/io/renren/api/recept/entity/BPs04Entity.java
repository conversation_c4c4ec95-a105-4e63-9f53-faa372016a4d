package io.renren.api.recept.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.annotation.Dict;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目管理人员信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-28
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PS04")
public class BPs04Entity  {

	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long ps0401;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 参建单位ID
     */
	private Long cp0201;
    /**
     * 人员ID
     */
	private Long ps0101;
    /**
     * 岗位类型
     */
	@Dict(fieldType = "WORKTYPECODE")
	private String jobtype;
    /**
     * 职员状态
     */
	private String managestatus;
    /**
     * 管理类型
     */
	private String managetype;
    /**
     * 头像采集照片
     */
	private String photo;
    /**
     * 是否购买工伤或意外伤害保险
     */
	private String hasbuyinsurance;
    /**
     * 进场时间
     */
	private Date entrytime;
    /**
     * 退场时间
     */
	private Date exittime;
    /**
     * 进退场状态
     */
	private String inOrOut;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}