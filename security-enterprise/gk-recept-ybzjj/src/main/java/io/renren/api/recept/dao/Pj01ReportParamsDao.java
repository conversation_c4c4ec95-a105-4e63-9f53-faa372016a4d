package io.renren.api.recept.dao;


import io.renren.api.recept.entity.BPj01ReportParamsEntity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 数据上报配置表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-14
 */
@Mapper
public interface Pj01ReportParamsDao extends BaseDao<BPj01ReportParamsEntity> {
    /**
     * 查询项目上报的配置信息
     * @param pj0101 项目ID
     * @return
     */
    List<BPj01ReportParamsEntity> selectParamList(Long pj0101);
}