package io.renren.api.recept.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.annotation.Dict;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 人员实名基础信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PS01")
public class BPs01Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long ps0101;
    /**
     * 姓名
     */
	private String name;
    /**
     * 证件类型
     */
	private String idcardtype;
    /**
     * 证件号码
     */
	private String idcardnumber;
    /**
     * 性别
     */
	private String gender;
    /**
     * 民族
     */
	@Dict(fieldType = "NATION")
	private String nation;
    /**
     * 出生日期
     */
	private Date birthday;
    /**
     * 住址
     */
	private String address;
    /**
     * 学历
     */
	private String edulevel;
    /**
     * 学位
     */
	private String degree;
    /**
     * 籍贯（身份证号前6位）
     */
	private String areacode;
    /**
     * 身份证头像
     */
	private String headimageurl;
    /**
     * 政治面貌
     */
	private String politicstype;
    /**
     * 是否加入工会
     */
	private String isjoined;
    /**
     * 加入工会时间
     */
	private Date joinedtime;
    /**
     * 手机号码
     */
	private String cellphone;
    /**
     * 文化程度
     */
	private String cultureleveltype;
    /**
     * 特长
     */
	private String specialty;
    /**
     * 是否有重大病史
     */
	private String hasbadmedicalhistory;
    /**
     * 紧急联系人姓名
     */
	private String urgentlinkman;
    /**
     * 紧急联系电话
     */
	private String urgentlinkmanphone;
    /**
     * 开始工作日期
     */
	private Date workdate;
    /**
     * 婚姻状况
     */
	private String maritalstatus;
    /**
     * 发证机关
     */
	private String grantorg;
    /**
     * 正面照 URL
     */
	private String positiveidcardimageurl;
    /**
     * 反面照 URL
     */
	private String negativeidcardimageurl;
    /**
     * 有效期开始日期
     */
	private Date startdate;
    /**
     * 有效期结束日期
     */
	private Date expirydate;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}