package io.renren.api.recept.service.impl;

import io.renren.api.recept.dao.BPs04Dao;
import io.renren.api.recept.entity.BPs04Entity;
import io.renren.api.recept.service.BPs04Service;
import io.renren.api.report.dto.ManagerInfo;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 项目管理人员信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-28
 */
@Service
public class BPs04ServiceImpl extends BaseServiceImpl<BPs04Dao, BPs04Entity> implements BPs04Service {

    @Override
    public List<ManagerInfo> findListByProjectCode(Long pj0101) {
        return baseDao.findListByProjectCode(pj0101);
    }

    @Override
    public String findLastTime(Long pj0101, Long cp0201) {
        return baseDao.findLastTime(pj0101, cp0201);
    }
}