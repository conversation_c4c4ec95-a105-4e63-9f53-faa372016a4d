package io.renren.api.recept.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.annotation.Dict;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 建筑工人信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PS02")
public class BPs02Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long ps0201;
    /**
     * 人员ID
     */
	private Long ps0101;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 班组ID
     */
	private Long tm0101;
    /**
     * 是否班组长
     */
	private String isteamleader;
    /**
     * 工种
     */
	@Dict(fieldType = "WORKTYPECODE")
	private String worktypecode;
    /**
     * 工资卡帐号
     */
	private String payrollbankcardnumber;
    /**
     * 工资卡开户行名称
     */
	private String payrollbankname;
    /**
     * 工资卡银行代码
     */
	private String payrolltopbankcode;
    /**
     * 工人头像
     */
	private String issuecardpicurl;
    /**
     * 是否购买工伤或意外伤害保险 
     */
	private String hasbuyinsurance;
    /**
     * 进场时间
     */
	private Date entrytime;
    /**
     * 退场时间
     */
	private Date exittime;
    /**
     * 进退场状态
     */
	private String inOrOut;
    /**
     * 备注
     */
	private String memo;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}