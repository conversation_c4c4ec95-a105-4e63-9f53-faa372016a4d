package io.renren.api.recept.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.renren.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 考勤记录表
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-27
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_KQ02")
public class BKq02Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@TableId
	private Long kq0201;
    /**
     * 人员ID
     */
	private Long userId;
    /**
     * 姓名
     */
	private String personName;
    /**
     * 人员类型
     */
	private String personType;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 设备序列号
     */
	private String deviceserialno;
    /**
     * 考勤时间
     */
	private Date checkdate;
    /**
     * 进出方向
     */
	private String direction;
    /**
     * 通行方式
     */
	private String attendtype;
    /**
     * 经度
     */
	private Long lng;
    /**
     * 纬度
     */
	private Long lat;
    /**
     * 刷卡近照
     */
	private String imageUrl;
    /**
     * 人员测量温度值
     */
	private String temperature;
    /**
     * 设置的体温异常标准
     */
	private String standard;
    /**
     * 体温状态(1:正常 2:异常 3:未开启测温 4:测温打开，但口罩检测未通过)
     */
	private String temperatureState;
    /**
     * 温度单位(1.摄氏度2.华氏度)
     */
	private String tempUnit;
    /**
     * 图片下载次数
     */
	private Short downloadCount;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}