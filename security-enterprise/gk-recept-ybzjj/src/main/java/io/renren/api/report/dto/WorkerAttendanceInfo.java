package io.renren.api.report.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2023-07-31
 */
@Data
@ApiModel(value = "人员考勤记录信息")
public class WorkerAttendanceInfo implements Serializable {

	private static final long serialVersionUID = 1L;

	@JSONField(serialize = false)
	private Long pj0101;

	@ApiModelProperty(value = "人员id", required = true)
	private Long userId;

	@ApiModelProperty(value = "人员工号,宜宾", required = true)
	private Long worknumber;

	@ApiModelProperty(value = "人员类型（1-工人，2-管理人员）", required = true)
	private String personType;

	@ApiModelProperty(value = "人员姓名", required = true)
	private String personName;

	@ApiModelProperty(value = "考勤时间", required = true)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date checkdate;

	@ApiModelProperty(value = "进出方向（1-进2-出）", required = true)
	private String direction;

	@ApiModelProperty(value = "通行方式（1-刷身份证，2-人脸识别，3-指纹，4-红膜，5-移动设备）", required = true)
	private String attendtype;

	@ApiModelProperty(value = "刷卡近照（网络url）", required = true)
	private String imageUrl;
}