package io.renren.api.recept.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.api.recept.dao.YbKq02FailDao;
import io.renren.api.recept.entity.YbKq02FailEntity;
import io.renren.api.recept.service.YbKq02FailService;
import io.renren.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 失败插入的考勤记录，补偿处理
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-29
 */
@Service
public class YbKq02FailServiceImpl extends BaseServiceImpl<YbKq02FailDao, YbKq02FailEntity> implements YbKq02FailService {

    @Override
    public List<YbKq02FailEntity> list(Long pj0101) {
        return baseDao.selectList(new QueryWrapper<YbKq02FailEntity>()
                .eq("pj0101", pj0101)
                .eq("status", "0")
                .le("RETRYCOUNT", 2)
                .le("ROWNUM", 1000));
    }
}