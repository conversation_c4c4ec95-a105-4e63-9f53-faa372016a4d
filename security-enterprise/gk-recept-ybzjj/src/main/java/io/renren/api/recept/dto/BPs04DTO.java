package io.renren.api.recept.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-26
 */
@Data
@ApiModel(value = "项目管理人员信息")
public class BPs04DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0401;

    @ApiModelProperty(value = "项目ID")
    @NotNull(message = "项目id不能为空")
    private Long pj0101;

    @ApiModelProperty(value = "参建单位ID")
    @NotNull(message = "所属企业不能为空")
    private Long cp0201;

    @ApiModelProperty(value = "人员ID")
    private Long ps0101;

    @ApiModelProperty(value = "人员基础信息")
    private BPs01DTO ps01DTO;

    @ApiModelProperty(value = "人员姓名")
    private String personName;

    @ApiModelProperty(value = "人员证件号")
    private String idcardnumber;

    @ApiModelProperty(value = "人员电话")
    private String cellPhone;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "所属企业名称")
    private String corname;

    @ApiModelProperty(value = "岗位类型")
    private String jobtype;

    @ApiModelProperty(value = "职员状态")
    private String managestatus;

    @ApiModelProperty(value = "管理类型")
    private String managetype;

    @ApiModelProperty(value = "头像采集照片")
    @NotBlank(message = "人员头像不能为空")
    private String photo;

    @ApiModelProperty(value = "是否购买工伤或意外伤害保险")
    @NotBlank(message = "是否购买工伤或意外伤害保险不能为空")
    private String hasbuyinsurance;

    @ApiModelProperty(value = "进场时间")
    private Date entrytime;

    @ApiModelProperty(value = "退场时间")
    private Date exittime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "人员基础信息", required = true)
    @Valid
    private BPs01DTO bps01DTO;

}