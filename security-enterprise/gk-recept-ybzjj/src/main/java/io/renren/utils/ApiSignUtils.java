package io.renren.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.renren.common.exception.ErrorCode;
import io.renren.common.exception.RenException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 签名工具类
 *
 * <AUTHOR>
 * @date 2022/11/14 11:38
 */
@Component
public class ApiSignUtils {
    /**
     * 签名字段
     */
    private final static String SIGN_FIELD = "sign";
    /**
     * 时间戳
     */
    private final static String TIMES_TAMP = "timestamp";

    private static String APP_KEY;

    private static String SECRET;

    @Value("${mcc5.secret}")
    private String secret;

    @Value("${mcc5.appkey}")
    private String appkey;

    @PostConstruct
    private void setVariable() {
        APP_KEY = this.appkey;
        SECRET = this.secret;
    }

    /**
     * 获取Body参数转换成Map<String, Object>
     *
     * @param request 输入流
     * @return Map<String, Object>
     */
    public static Map<String, String> getBodyParams(HttpServletRequest request) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
        String str;
        StringBuilder wholeStr = new StringBuilder();
        //一行一行的读取body体里面的内容；
        while ((str = reader.readLine()) != null) {
            wholeStr.append(str);
        }
        return HttpUtil.decodeParamMap(wholeStr.toString(), StandardCharsets.UTF_8);
    }

    /**
     * 签名校验 HMAC MD5
     */
    public static Map<String, Object> checkSignature() {
        String timestamp = String.valueOf(DateUtil.currentSeconds());
        String nonce = RandomUtil.randomString(16);
        String Sign = "";
        try {
            String body = nonce + "&" + timestamp + "&" + APP_KEY;
            SecretKey key = new SecretKeySpec(SECRET.getBytes(), "HmacMD5");
            Mac mac = Mac.getInstance(key.getAlgorithm());
            mac.init(key);
            byte[] code = mac.doFinal(body.getBytes(StandardCharsets.UTF_8));
            Sign = Hex.encodeHexString(code).toUpperCase(Locale.ROOT);
        } catch (Exception e) {
            throw new RenException("签名校验失败");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("nonce", nonce);
        map.put("timestamp", timestamp);
        map.put("appkey", APP_KEY);
        map.put("sign", Sign);
        return map;
    }

    /**
     * 移除body的sign和timestamp,重复数据的校验
     *
     * @param params Map<String, String>
     * @return 移除参数之后的字符串
     */
    public static String removeParams(Map<String, String> params) {
        String removeParams;
        //移除签名字段
        params.keySet().removeIf(keySet -> keySet != null && keySet.contains(SIGN_FIELD));
        //移除时间戳字段
        params.keySet().removeIf(keySet -> keySet != null && keySet.contains(TIMES_TAMP));
        removeParams = params.toString();
        return removeParams;
    }

    /**
     * 字符串处理，undertow作为容器，x-www-form-urlencoded post请求中文乱码问题处理
     *
     * @param str 待处理字符串
     * @return 新的字符串
     */
    public static String revert(String str) {
        String newStr = "";
        if (StringUtils.isNotBlank(str)) {
            char[] chars = str.toCharArray();
            byte[] bytes = new byte[chars.length];
            for (int i = 0; i < chars.length; i++) {
                bytes[i] = (byte) chars[i];
            }
            newStr = new String(bytes, StandardCharsets.UTF_8);
        }
        return newStr;
    }

    /**
     * 校验字符串是否JSON格式
     */
    public static boolean isJson(String str) {
        boolean result;
        if (StringUtils.isBlank(str)) {
            return false;
        }
        try {
            JSONArray jsonArray = JSONObject.parseArray(str);
            result = true;
        } catch (Exception e) {
            result = false;
        }
        return result;
    }
}
