package io.renren.utils;

import com.alibaba.fastjson.JSONObject;
import io.renren.api.recept.dto.*;
import io.renren.api.recept.dao.*;
import io.renren.api.recept.entity.*;
import io.renren.common.constant.Constant;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 发送rabbitmq数据的工具
 *
 * <AUTHOR>
 */
@Component
public class SendDataUtil {

    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private BPj01Dao pj01Dao;
    @Autowired
    private BCp01Dao cp01Dao;
    @Autowired
    private BCp02Dao cp02Dao;
    @Autowired
    private BPs02Dao ps02Dao;
    @Autowired
    private BPs01Dao ps01Dao;
    @Autowired
    private BTm01Dao tm01Dao;
    @Autowired
    private Pj01ReportParamsDao paramsDao;


    private static final String EXCHANGE_NAME = "attendance_data";
    private static final String PJ0101 = "pj0101";
    private static final String DATATYPE = "dataType";
    private static final String PJ01 = "pj01";
    private static final String CP01 = "cp01";
    private static final String CP02 = "cp02";
    private static final String TM01 = "tm01";
    private static final String PS01 = "ps01";
    private static final String PS02 = "ps02";
    private static final String PS04 = "ps04";
    private static final String PA01 = "pa01";
    private static final String KQ02 = "kq02";


    /**
     * 发送项目数据到上报服务
     *
     * @param pj01DTO 项目主键
     */
    public void sendData(BPj01DTO pj01DTO) {
        Long pj0101 = pj01DTO.getPj0101();
        JSONObject jsonObject = new JSONObject(8);
        jsonObject.put(PJ0101, pj0101);
        jsonObject.put(DATATYPE, Constant.PROJECT);
        jsonObject.put(PJ01, JSONObject.toJSONString(pj01DTO));
        send(jsonObject);
    }

    /**
     * 发送参建单位和企业数据到上报服务
     * @param cp02DTO 参建单位信息
     */
    public void sendData(BCp02DTO cp02DTO) {
        Long pj0101 = cp02DTO.getPj0101();
        BPj01Entity pj01Entity = pj01Dao.selectById(pj0101);
        BCp01Entity cp01Entity = getCp01(cp02DTO.getCp0201());
        JSONObject jsonObject = new JSONObject(8);

        jsonObject.put(PJ0101, pj0101);
        jsonObject.put(DATATYPE, Constant.COMPANY);
        jsonObject.put(PJ01, JSONObject.toJSONString(pj01Entity));
        jsonObject.put(CP01, JSONObject.toJSONString(cp01Entity));
        jsonObject.put(CP02, JSONObject.toJSONString(cp02DTO));
        send(jsonObject);

    }

    /**
     * 发送工人数据到上报服务
     *
     * @param ps02DTO 工人参建信息
     */
    public void sendData(BPs02DTO ps02DTO) {
        JSONObject jsonObject = new JSONObject(8);
        BTm01Entity tm01Entity = tm01Dao.selectById(ps02DTO.getTm0101());
        BCp01Entity bCp01Entity = getCp01(tm01Entity.getCp0201());
        BPs01Entity ps01Entity = getPs01(ps02DTO.getPs0201());
        jsonObject.put(PJ0101, ps02DTO.getPj0101());
        jsonObject.put(DATATYPE, Constant.WORKER);
        jsonObject.put(CP01, JSONObject.toJSONString(bCp01Entity));
        jsonObject.put(PS01, JSONObject.toJSONString(ps01Entity));
        jsonObject.put(PS02, JSONObject.toJSONString(ps02DTO));
        send(jsonObject);

    }

    /**
     * 发送班组数据到上报服务
     *
     * @param tm01Entity 班组信息
     */
    public void sendData(BTm01Entity tm01Entity) {
        BCp01Entity cp01Entity = getCp01(tm01Entity.getCp0201());
        JSONObject jsonObject = new JSONObject(8);

        jsonObject.put(PJ0101, tm01Entity.getPj0101());
        jsonObject.put(DATATYPE, Constant.TEAM);
        jsonObject.put(CP01, JSONObject.toJSONString(cp01Entity));
        jsonObject.put(TM01, JSONObject.toJSONString(tm01Entity));
        send(jsonObject);

    }

    /**
     * 发送管理人员数据到上报服务
     *
     * @param ps04DTO 管理人员信息
     */
    public void sendData(BPs04DTO ps04DTO) {
        Long pj0101 = ps04DTO.getPj0101();
        BCp01Entity cp01Entity = getCp01(ps04DTO.getCp0201());
        BPs01Entity ps01Entity = ps01Dao.selectById(ps04DTO.getPs0101());
        JSONObject jsonObject = new JSONObject(16);
        jsonObject.put(PJ0101, pj0101);
        jsonObject.put(DATATYPE, Constant.MANAGER);
        jsonObject.put(CP01, JSONObject.toJSONString(cp01Entity));
        jsonObject.put(PS01, JSONObject.toJSONString(ps01Entity));
        jsonObject.put(PS04, JSONObject.toJSONString(ps04DTO));
        send(jsonObject);
    }


    /**
     * 考勤服务
     *
     * @param bKq02DTO
     */
    public void sendData(BKq02DTO bKq02DTO) {
        Long pj0101 = bKq02DTO.getPj0101();
        JSONObject jsonObject = new JSONObject(16);
        jsonObject.put(PJ0101, pj0101);
        jsonObject.put(DATATYPE, Constant.ATTENDANCE);
        jsonObject.put(KQ02, JSONObject.toJSONString(bKq02DTO));
        send(jsonObject);

    }


    private BCp01Entity getCp01(Long cp0201) {
        BCp02Entity appCp02Entity = cp02Dao.selectById(cp0201);
        BCp01Entity appCp01Entity = cp01Dao.selectById(appCp02Entity.getCp0101());
        return appCp01Entity;
    }

    private BPs01Entity getPs01(Long ps0201) {
        BPs02Entity ps02Entity = ps02Dao.selectById(ps0201);
        BPs01Entity ps01Entity = ps01Dao.selectById(ps02Entity.getPs0101());
        return ps01Entity;
    }


    private void send(JSONObject payload) {
        //查询数据需要写入的队列
        List<BPj01ReportParamsEntity> paramsEntityList = paramsDao.selectParamList(payload.getLong("pj0101"));
        paramsEntityList.forEach(bPj01ReportParamsEntity -> {
            rabbitTemplate.convertAndSend(EXCHANGE_NAME, bPj01ReportParamsEntity.getRoutingKey(), payload);
        });
    }
}
