package io.renren.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import io.renren.common.utils.DateUtils;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.spec.AlgorithmParameterSpec;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 五冶集团数据上报加密工具类
 *
 * <AUTHOR>
 * @date 2023/7/14 9:09
 */
@Component
public class EncryptUtils {
    private static final Logger log = LoggerFactory.getLogger(EncryptUtils.class);
    private static final String KEY_ALGORITHM = "AES";
    /**
     * 加密算法 算法名称/加密模式/数据填充方式
     */
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";

    private static String APP_KEY;

    private static String SECRET;

    @Value("${mcc5.secret}")
    private String secret;

    @Value("${mcc5.appkey}")
    private String appkey;

    @PostConstruct
    private void setVariable() {
        APP_KEY = this.appkey;
        SECRET = this.secret;

    }

    /**
     * AES加密
     *
     * @param content 待加密内容
     * @return 返回Base64转码后的加密数据
     */
    public static String encryptAes(String content) {
        byte[] result = new byte[0];
        try {
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(SECRET.getBytes(StandardCharsets.UTF_8), KEY_ALGORITHM);
            AlgorithmParameterSpec paramSpec = new IvParameterSpec(SECRET.substring(0, 16).getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, paramSpec);
            result = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("AES加密错误:" + e);
            e.printStackTrace();
        }
        return Base64.encodeBase64String(result);
    }

    /**
     * 数据签名
     *
     * @param projectCode 项目编码
     * @param data        待签名的数据(JSON字符串)
     * @return 签名字符串
     */
    public static Map<String, Object> signature(String projectCode, String data) {
        Map<String, Object> map = new HashMap<>(6);
        try {
            map.put("appId", APP_KEY);
            map.put("projectCode", projectCode);
            map.put("nonce", IdUtil.simpleUUID());
            map.put("timestamp", DateUtils.format(new Date(), "yyyyMMddHHmmss"));
            map.put("data", encryptAes(data));
            //map参数字母升序排列,使用“ &”符号将“参数名=参数值”连接起来,组成字符串
            String sortJoin = MapUtil.sortJoin(map, "&", "=", true);
            //组成的字符串的首尾加上secret值,同时转成小写
            String secretSpStr = (SECRET + sortJoin + SECRET).toLowerCase(Locale.ROOT);
            //对拼接的字符串进行MD5加密
            String md5Str = SecureUtil.md5(secretSpStr);
            map.put("sign", md5Str);
        } catch (Exception e) {
            log.error("签名错误:" + e);
            e.printStackTrace();
        }
        return map;
    }

    /**
     * 数据签名
     *
     * @return 签名字符串
     */
    public static Map<String, Object> signature() {
        Map<String, Object> map = new HashMap<>(6);
        try {
            map.put("appkey", APP_KEY);
            map.put("nonce", RandomUtil.randomString(16));
            map.put("timestamp", DateUtil.currentSeconds());
            //map参数字母升序排列,使用“ &”符号将“参数名=参数值”连接起来,组成字符串
            String sortJoin = MapUtil.sortJoin(map, "&", "=", true);
            //组成的字符串的首尾加上secret值,同时转成大写
            String secretSpStr = (SECRET + sortJoin + SECRET).toUpperCase(Locale.ROOT);
            //对拼接的字符串进行MD5加密
            String md5Str = SecureUtil.md5(secretSpStr);
            map.put("sign", md5Str);
        } catch (Exception e) {
            log.error("签名错误:" + e);
            e.printStackTrace();
        }
        return map;
    }
}
