package io.renren.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class HttpUtils {

    /**
     * 请求超时时间(单位毫秒)
     */
    private static final int HTTP_TIME_OUT = 60000;

    public static JSONObject doPost(String httpUrl, Map<String, Object> date) {
        Map<String, Object> signature = ApiSignUtils.checkSignature();
        String result = HttpUtil.createPost(httpUrl)
                .body(JSONObject.toJSONString(date))
                .header("appkey", signature.get("appkey").toString())
                .header("nonce", signature.get("nonce").toString())
                .header("timestamp", signature.get("timestamp").toString())
                .header("sign", signature.get("sign").toString())
                .timeout(HTTP_TIME_OUT)
                .execute().body();
        return JSONObject.parseObject(result);
    }

}
