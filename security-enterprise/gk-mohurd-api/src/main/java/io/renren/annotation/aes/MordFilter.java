package io.renren.annotation.aes;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.serializer.ValueFilter;
import io.renren.common.AesUtil;
import io.renren.common.ImageUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;

/**
 * 自定义字段过滤器，处理json数据格式
 *
 * <AUTHOR>
 * @Date 2020-11-27 13:27
 */

public class MordFilter implements ValueFilter {
    @Override
    public Object process(Object object, String name, Object value) {
        if (ObjectUtil.isNull(object) || StringUtils.isEmpty(name) || ObjectUtil.isNull(value)) {
            return value;
        }
        try {
            Field field = object.getClass().getDeclaredField(name);
            boolean hasSecureField = field.isAnnotationPresent(AesEncField.class);
            boolean annotationPresent = field.isAnnotationPresent(UrlToBase64Field.class);
            //此处目前就2个注解,如果多了可以进行优化，不要用if判断
            if (hasSecureField) {
                String plaintextValue = (String) value;
                value = AesUtil.encrypt(plaintextValue);
            }
            if (annotationPresent) {
                String plaintextValue = (String) value;
                value = ImageUtils.ImageToBase64ByLocal(plaintextValue);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }
}
