package io.renren.common;

import io.renren.config.MohurdProperties;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.security.spec.AlgorithmParameterSpec;

/**
 * AES加密工具类
 *
 * <AUTHOR>
 * @Date 2020-11-24 14:27
 */
@Component
public class AesUtil {
    private static final String KEY_ALGORITHM = "AES";
    /**
     * 秘钥
     */
    private static String ENCRYPT_KEY;
    /**
     * 加密算法 算法名称/加密模式/数据填充方式
     */
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/CBC/PKCS7Padding";

    static {
        //如果是PKCS7Padding填充方式，则必须加上下面这行
        Security.addProvider(new BouncyCastleProvider());
    }

    @Autowired
    public void init(MohurdProperties mohurdProperties) {
        AesUtil.ENCRYPT_KEY = mohurdProperties.getAppSecret();
    }

    /**
     * AES 加密操作
     *
     * @param content 待加密内容
     * @return 返回Base64转码后的加密数据
     */
    public static String encrypt(String content) throws Exception {
        Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
        SecretKeySpec keySpec = new SecretKeySpec(ENCRYPT_KEY.getBytes(StandardCharsets.UTF_8), KEY_ALGORITHM);
        AlgorithmParameterSpec paramSpec = new IvParameterSpec(ENCRYPT_KEY.substring(0, 16).getBytes(StandardCharsets.UTF_8));
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, paramSpec);
        byte[] result = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64String(result);
    }

    /**
     * AES解密
     *
     * @param encryptStr 解密内容
     * @return 返回Base64转码后的解密数据
     */
    public static String decrypt(String encryptStr) throws Exception {
        Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
        SecretKeySpec keySpec = new SecretKeySpec(ENCRYPT_KEY.getBytes(StandardCharsets.UTF_8), KEY_ALGORITHM);
        AlgorithmParameterSpec paramSpec = new IvParameterSpec(ENCRYPT_KEY.substring(0, 16).getBytes(StandardCharsets.UTF_8));
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, paramSpec);
        byte[] result = cipher.doFinal(encryptStr.getBytes(StandardCharsets.UTF_8));
        return new String(result, StandardCharsets.UTF_8);
    }
}
