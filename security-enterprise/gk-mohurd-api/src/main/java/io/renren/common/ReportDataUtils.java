package io.renren.common;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import io.renren.config.MohurdProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 住建部数据上报
 *
 * <AUTHOR>
 * @Date 2020-11-19 15:30
 */
@Component
public class ReportDataUtils {

    private static MohurdProperties mohurdProperties;
    /**
     * 接口版本号
     */
    private static final String VERSION = "1.0";
    /**
     * 数据响应格式
     */
    private static final String FORMAT = "json";
    /**
     * 时间格式
     */
    public final static String DATE_TIME_PATTERN = "yyyyMMddHHmmss";

    @Autowired
    public void init(MohurdProperties mohurdProperties) {
        ReportDataUtils.mohurdProperties = mohurdProperties;
    }

    /**
     * 数据上报
     *
     * @param data   待上报的数据json字符串
     * @param method 数据上报方法名称,必填
     * @return 响应的结果json字符串
     */
    public static String reportData(String data, String method) {
        String url = mohurdProperties.getUrl();
        Map<String, Object> map = new HashMap<>(1);
        map.put("method", method);
        map.put("version", VERSION);
        map.put("appid", mohurdProperties.getAppId());
        map.put("format", FORMAT);
        map.put("timestamp", DateUtil.format(DateUtil.date(), DATE_TIME_PATTERN));
        map.put("nonce", RandomUtil.randomString(16));
        map.put("data", data);
        //先按照格式拼接字符串
        String signData = MapUtil.sortJoin(map, "&", "=", false).toLowerCase();
        //组装字符串进行SHA256加密
        map.put("sign", SecureUtil.sha256(signData + "&" + "appsecret=" + mohurdProperties.getAppSecret()));
        //发送请求
        return HttpUtil.post(url, map);
    }
}
