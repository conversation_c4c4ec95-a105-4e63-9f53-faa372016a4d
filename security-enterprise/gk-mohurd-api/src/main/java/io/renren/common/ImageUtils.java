package io.renren.common;


import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

/**
 * 图片工具类
 *
 * <AUTHOR>
 * @Date 2020-11-30 8:43
 */

public class ImageUtils {
    /**
     * base64图片header信息
     */
    private final static String IMAGE_HEADER = "data:image/jpeg;base64,";

    /**
     * 此处的base64是添加了header的,根据实际情况使用
     * 将图片文件转化为字节数组字符串，并对其进行Base64编码处理
     *
     * @param imgFile 图片路径
     * @return
     */
    public static String ImageToBase64ByLocal(String imgFile) {
        InputStream in = null;
        byte[] data = null;
        // 读取图片字节数组
        try {
            in = new FileInputStream(imgFile);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        //对字节数组Base64编码
        Base64.Encoder encoder = Base64.getEncoder();
        return IMAGE_HEADER + encoder.encodeToString(data);
    }

}
