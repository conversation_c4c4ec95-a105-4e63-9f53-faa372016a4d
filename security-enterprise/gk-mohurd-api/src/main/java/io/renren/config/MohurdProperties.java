package io.renren.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2020-11-19 9:57
 */
@Component
@Data
@ConfigurationProperties(prefix = "mohurd")
public class MohurdProperties {
    /**
     * 访问地址
     */
    private String url;
    /**
     * AppSecret计算签名以及对敏感字段的加密
     */
    private String appSecret;
    /**
     * 接口调用方的身份标识符
     */
    private String appId;
    /**
     * 企业秘钥
     */
    private String key;

}
