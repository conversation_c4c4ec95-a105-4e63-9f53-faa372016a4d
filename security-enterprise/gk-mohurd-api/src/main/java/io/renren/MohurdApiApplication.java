/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

/**
 * renren-api
 *
 * <AUTHOR>
 */
@SpringBootApplication
public class MohurdApiApplication extends SpringBootServletInitializer {

	public static void main(String[] args) {
		SpringApplication.run(MohurdApiApplication.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(MohurdApiApplication.class);
	}

}
