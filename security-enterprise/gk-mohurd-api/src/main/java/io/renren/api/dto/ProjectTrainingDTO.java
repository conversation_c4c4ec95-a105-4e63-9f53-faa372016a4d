package io.renren.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目培训表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-07-13
 */
@Data
@ApiModel(value = "项目培训表")
public class ProjectTrainingDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
	@JSONField(serialize = false)
	private Long id;

	@ApiModelProperty(value = "住建部项目CODE")
	private String projectCode;

    @ApiModelProperty(value = "培训日期(yyyy-MM-dd)")
    private String trainingDate;

    @ApiModelProperty(value = "培训时长(小时)")
    private String trainingDuration;

    @ApiModelProperty(value = "培训名称")
    private String trainingName;

    @ApiModelProperty(value = "培训类型代码")
    private String trainingTypeCode;

    @ApiModelProperty(value = "培训人")
    private String trainer;

    @ApiModelProperty(value = "培训机构")
    private String trainingOrg;

    @ApiModelProperty(value = "培训地址")
    private String trainingAddress;

    @ApiModelProperty(value = "培训简述")
    private String description;

    @ApiModelProperty(value = "0待上报，1上报失败,2待核验，3核验通过，4核验失败")
    private String status;

    @ApiModelProperty(value = "响应的结果")
    private String message;

    @ApiModelProperty(value = "响应接口异步查询使用")
	@JSONField(serialize = false)
	private String requestSerialCode;


}