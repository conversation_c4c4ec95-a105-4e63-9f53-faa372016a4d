package io.renren.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.renren.annotation.aes.AesEncField;
import io.renren.annotation.aes.UrlToBase64Field;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-24
 */
@Data
@ApiModel(value = "项目工人信息")
public class WorkerInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
	@JSONField(serialize = false)
	private Long ps0201;

    @ApiModelProperty(value = "所属项目ID")
	@JSONField(serialize = false)
	private Long pj0101;

    @ApiModelProperty(value = "所属班组ID")
	@JSONField(serialize = false)
	private Long tm0101;

    @ApiModelProperty(value = "工人姓名")
    private String workerName;

    @ApiModelProperty(value = "是否班组长")
    private String isTeamLeader;

    @ApiModelProperty(value = "证件类型")
    private String idCardType;

    @ApiModelProperty(value = "证件号码")
    @AesEncField
    private String idCardNumber;

    @ApiModelProperty(value = "当前工种")
    private String workType;

    @ApiModelProperty(value = "工人类型")
    private String workRole;

    @ApiModelProperty(value = "管理岗位")
    private String manageType;

    @ApiModelProperty(value = "发卡时间(yyyy-MM-dd)")
    private String issueCardDate;

    @ApiModelProperty(value = "办卡采集相片")
    private String issueCardPic;

    @ApiModelProperty(value = "考勤卡号")
    private String cardNumber;

    @ApiModelProperty(value = "发放工资银行卡号")
    @AesEncField
    private String payRollBankCardNumber;

    @ApiModelProperty(value = "发放工资银行名称")
    private String payRollBankName;

    @ApiModelProperty(value = "发放工资卡银行联号")
    private String bankLinkNumber;

    @ApiModelProperty(value = "发放工资卡银行")
    private String payRollTopBankCode;

    @ApiModelProperty(value = "是否购买工伤或意外伤害保险")
    private String hasBuyInsurance;

    @ApiModelProperty(value = "民族(如：汉，回，藏等)")
    private String nation;

    @ApiModelProperty(value = "住址")
    private String address;

    @ApiModelProperty(value = "头像")
    @UrlToBase64Field
    private String headImage;

    @ApiModelProperty(value = "政治面貌")
    private String politicsType;

    @ApiModelProperty(value = "加入工会时间(yyyy-MM-dd)")
    private String joinedTime;

    @ApiModelProperty(value = "手机号码")
    private String cellPhone;

    @ApiModelProperty(value = "文化程度")
    private String cultureLevelType;

    @ApiModelProperty(value = "特长")
    private String specialty;

    @ApiModelProperty(value = "是否有重大病史")
    private String hasBadMedicalHistory;

    @ApiModelProperty(value = "紧急联系人姓名")
    private String urgentLinkMan;

    @ApiModelProperty(value = "紧急联系方式")
    private String urgentLinkManPhone;

    @ApiModelProperty(value = "开始工作日期(yyyy-MM-dd)")
    private String workDate;

    @ApiModelProperty(value = "婚姻状况")
    private String maritalStatus;

    @ApiModelProperty(value = "发证机关")
    private String grantOrg;

    @ApiModelProperty(value = "正面照(不超过500KB的BASE64字符串)")
    @UrlToBase64Field
    private String positiveIDCardImage;

    @ApiModelProperty(value = "反面照(不超过500KB的BASE64字符串)")
    @UrlToBase64Field
    private String negativeIDCardImage;

    @ApiModelProperty(value = "证件有效期开始日期(yyyy-MM-dd)")
    private String startDate;

    @ApiModelProperty(value = "证件有效期结束日期(yyyy-MM-dd)")
    private String expiryDate;

    @ApiModelProperty(value = "响应接口异步查询使用")
	@JSONField(serialize = false)
	private String requestSerialCode;


}