package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目培训表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-07-13
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("MHD_PROJECT_TRAINING")
public class ProjectTrainingEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
	private Long id;

	/**
	 * 项目ID
	 */
	private Long pj0101;
    /**
     * 培训日期(yyyy-MM-dd)
     */
	private String trainingDate;
    /**
     * 培训时长(小时)
     */
	private String trainingDuration;
    /**
     * 培训名称
     */
	private String trainingName;
    /**
     * 培训类型代码
     */
	private String trainingTypeCode;
    /**
     * 培训人
     */
	private String trainer;
    /**
     * 培训机构
     */
	private String trainingOrg;
    /**
     * 培训地址
     */
	private String trainingAddress;
    /**
     * 培训简述
     */
	private String description;
    /**
     * 0待上报，1上报失败,2待核验，3核验通过，4核验失败
     */
	private String status;
    /**
     * 响应的结果
     */
	private String message;
    /**
     * 响应接口异步查询使用
     */
	private String requestSerialCode;
    /**
     * 异步校验结果
     */
	private String arynMessage;
    /**
     * 异步校验时间
     */
	private Date arynCheckDate;
	/**
     * 创建时间
     */
	private Date createDate;
	/**
	 * 更新时间
	 */
	private Date updateDate;
}