package io.renren.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 工人考勤记录表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-01-06
 */
@Data
@ApiModel(value = "工人考勤记录表")
public class WorkerAttInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "班组编号")
    private String teamSysNo;

    @ApiModelProperty(value = "考勤数据")
    private List<WorkerAttDTO> dataList;

}