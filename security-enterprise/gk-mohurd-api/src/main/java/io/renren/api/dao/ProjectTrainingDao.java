package io.renren.api.dao;


import io.renren.api.dto.ProjectTrainingDTO;
import io.renren.api.entity.ProjectTrainingEntity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目培训表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-07-13
 */
@Mapper
public interface ProjectTrainingDao extends BaseDao<ProjectTrainingEntity> {
    /**
     * 查询项目培训数据
     * @param pendMode
     * @return
     */
    List<ProjectTrainingDTO> selectProjectTrainingInfo(String pendMode);
}