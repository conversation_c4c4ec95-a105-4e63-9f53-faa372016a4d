package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目施工许可证
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("MHD_PROJECT_BUILDER_LICENSE")
public class ProjectBuilderLicenseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 工程名称
     */
    private String prjName;
    /**
     * 施工许可证编号
     */
    private String builderLicenseNum;

    /**
     * 创建时间
     */
    private Date createDate;
}