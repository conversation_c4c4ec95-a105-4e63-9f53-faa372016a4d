package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("MHD_WORKER_INFO")
public class WorkerInfoEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long ps0201;
    /**
     * 所属项目ID
     */
    private Long pj0101;
    /**
     * 所属班组ID
     */
    private Long tm0101;
    /**
     * 工人姓名
     */
    private String workerName;
    /**
     * 是否班组长
     */
    private String isTeamLeader;
    /**
     * 证件类型
     */
    private String idCardType;
    /**
     * 证件号码
     */
    private String idCardNumber;
    /**
     * 当前工种
     */
    private String workType;
    /**
     * 工人类型
     */
    private String workRole;
    /**
     * 管理岗位，工人类型为管理
     * 人员时，管理岗位为必填
     */
    private String manageType;
    /**
     * 发卡时间(yyyy-MM-dd)
     */
    private String issueCardDate;
    /**
     * 办卡采集相片(不超过 50KB
     * 的 Base64 字符串)
     */
    private String issueCardPic;
    /**
     * 考勤卡号
     */
    private String cardNumber;
    /**
     * 发放工资银行卡号
     */
    private String payRollBankCardNumber;
    /**
     * 发放工资银行名称
     */
    private String payRollBankName;
    /**
     * 发放工资卡银行联号
     */
    private String bankLinkNumber;
    /**
     * 发放工资卡银行
     */
    private String payRollTopBankCode;
    /**
     * 是否购买工伤或意外伤害保险
     */
    private String hasBuyInsurance;
    /**
     * 民族(如：汉，回，藏等)
     */
    private String nation;
    /**
     * 住址
     */
    private String address;
    /**
     * 头像(不超过 50KB 的 Base64
     * 字符串)
     */
    private String headImage;
    /**
     * 政治面貌
     */
    private String politicsType;
    /**
     * 加入工会时间(yyyy-MM-dd)
     */
    private String joinedTime;
    /**
     * 手机号码
     */
    private String cellPhone;
    /**
     * 文化程度
     */
    private String cultureLevelType;
    /**
     * 特长
     */
    private String specialty;
    /**
     * 是否有重大病史
     */
    private String hasBadMedicalHistory;
    /**
     * 紧急联系人姓名
     */
    private String urgentLinkMan;
    /**
     * 紧急联系方式
     */
    private String urgentLinkManPhone;
    /**
     * 开始工作日期(yyyy-MM-dd)
     */
    private String workDate;
    /**
     * 婚姻状况
     */
    private String maritalStatus;
    /**
     * 发证机关
     */
    private String grantOrg;
    /**
     * 正面照(不超过500KB的BASE64字符串)
     */
    private String positiveIDCardImage;
    /**
     * 反面照(不超过500KB的BASE64字符串)
     */
    private String negativeIDCardImage;
    /**
     * 证件有效期开始日期(yyyy-MM-dd)
     */
    private String startDate;
    /**
     * 证件有效期结束日期(yyyy-MM-dd)
     */
    private String expiryDate;
    /**
     * 0待上报，1上报失败,2待核验，3核验通过，4核验失败
     */
    private String status;
    /**
     * 响应的结果
     */
    private String message;
    /**
     * 响应接口异步查询使用
     */
    private String requestSerialCode;
    /**
     * 异步校验结果
     */
    private String arynMessage;
    /**
     * 异步校验时间
     */
    private Date arynCheckDate;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
}