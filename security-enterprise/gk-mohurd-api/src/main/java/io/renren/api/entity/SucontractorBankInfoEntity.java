package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 参建单位发放工资银行信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("MHD_SUB_CONTRACTOR_BANK_INFO")
public class SucontractorBankInfoEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 参建单位ID
     */
    private Long cp0201;
    /**
     * 银行代码
     */
    private String bankCode;
    /**
     * 银行支行名称
     */
    private String bankName;
    /**
     * 银行卡号
     */
    private String bankNumber;
    /**
     * 银行联号
     */
    private String bankLinkNumber;
    /**
     * 创建时间
     */
    private Date createDate;

}