package io.renren.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.renren.annotation.aes.AesEncField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 项目工资单明细表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-07-13
 */
@Data
@ApiModel(value = "项目工资单明细表")
public class ProjectPayrollWorkerDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @JSONField(serialize = false)
    private Long payrollWorkerId;

    @ApiModelProperty(value = "证件类型")
    private String idCardType;

    @ApiModelProperty(value = "证件号码")
    @AesEncField
    private String idCardNumber;

    @ApiModelProperty(value = "出勤天数")
    private Short days;

    @ApiModelProperty(value = "总工时(小时)")
    private String workHours;

    @ApiModelProperty(value = "工人工资卡号")
    @AesEncField
    private String payRollBankCardNumber;

    @ApiModelProperty(value = "工人工资卡银行代码")
    private String payRollBankCode;

    @ApiModelProperty(value = "工人工资卡开户行名称")
    private String payRollBankName;

    @ApiModelProperty(value = "工资代发银行卡号")
    @AesEncField
    private String payBankCardNumber;

    @ApiModelProperty(value = "工资代发银行代码")
    private String payBankCode;

    @ApiModelProperty(value = "工资代发开户行名称")
    private String payBankName;

    @ApiModelProperty(value = "应发金额(元)")
    private BigDecimal totalPayAmount;

    @ApiModelProperty(value = "实发金额(元)")
    private BigDecimal actualAmount;

    @ApiModelProperty(value = "是否为补发")
    private String isBackPay;

    @ApiModelProperty(value = "发放日期(yyyy-MM-dd)")
    private String balanceDate;

    @ApiModelProperty(value = "补发月份(yyyy-MM)")
    private String backPayMonth;

    @ApiModelProperty(value = "第三方工资单编号")
    private String thirdPayRollCode;

}