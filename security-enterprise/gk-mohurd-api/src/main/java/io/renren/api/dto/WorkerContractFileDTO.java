package io.renren.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.renren.annotation.aes.UrlToBase64Field;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 工人合同附件
 *
 * <AUTHOR>
 * @since 1.0.0 2020-12-24
 */
@Data
@ApiModel(value = "工人合同附件")
public class WorkerContractFileDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "工人ID")
	@JSONField(serialize = false)
	private Long ps0201;

    @ApiModelProperty(value = "附件名称")
    private String name;

    @ApiModelProperty(value = "附件地址")
    @UrlToBase64Field
    private String data;

}