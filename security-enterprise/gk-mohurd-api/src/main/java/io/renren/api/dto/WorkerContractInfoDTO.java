package io.renren.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-12-24 9:42
 */
@Data
@ApiModel(value = "工人合同信息上报对象")
public class WorkerContractInfoDTO implements Serializable {
    private static final long serialVersionUID = -3441430389859814256L;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "合同列表数据")
    private List<WorkerContractDTO> contractList;
}
