package io.renren.api.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.annotation.aes.MordFilter;
import io.renren.api.dao.*;
import io.renren.api.dto.*;
import io.renren.api.entity.*;
import io.renren.api.service.MohurdService;
import io.renren.common.ReportDataUtils;
import io.renren.common.utils.ConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-11-19 8:55
 */
@Service
public class MohurdServiceImpl implements MohurdService {
    @Autowired
    private MohurdDao mohurdDao;
    @Autowired
    private CorpInfoDao corpInfoDao;
    @Autowired
    private ProjectInfoDao projectInfoDao;
    @Autowired
    private ProjectSucontractorDao projectSucontractorDao;
    @Autowired
    private ProjectTeamInfoDao projectTeamInfoDao;
    @Autowired
    private WorkerInfoDao workerInfoDao;
    @Autowired
    private WorkerEntryExitDao workerEntryExitDao;
    @Autowired
    private WorkerContractDao workerContractDao;
    @Autowired
    private WorkerAttDao workerAttDao;
    @Autowired
    private ProjectTrainingDao projectTrainingDao;
    @Autowired
    private ProjectPayrollDao projectPayrollDao;
    /**
     * 接口响应成功状态
     */
    private final static String SUCCESS_STATUS = "0";
    /**
     * 接口响应code
     */
    private final static String RESPONSE_CODE = "code";
    /**
     * 异步接口校验成功状态
     */
    private final static String ARYN_SUCCESS_STATUS = "20";
    /**
     * 异步接口校验失败状态
     */
    private final static String ARYN_ERROR_STATUS = "10";
    /**
     * 待上报状态
     */
    private final static String PEND_MODE = "0";
    /**
     * 待核验状态
     */
    private final static String CHECK_MODE = "2";

    @Override
    public void postCorpInfo() {
        List<CorpInfoDTO> corpInfoDTOList = mohurdDao.selectCorpInfo(PEND_MODE);
        corpInfoDTOList.forEach(corpInfoDTO -> {
            String responseBody = ReportDataUtils.reportData(JSON.toJSONString(corpInfoDTO, new MordFilter()), "Corp.Upload");
            JSONObject jsonObject = JSON.parseObject(responseBody);
            CorpInfoEntity corpInfoEntity = ConvertUtils.sourceToTarget(corpInfoDTO, CorpInfoEntity.class);
            corpInfoEntity.setMessage(responseBody);
            corpInfoEntity.setUpdateDate(DateUtil.date());
            if (SUCCESS_STATUS.equals(jsonObject.getString(RESPONSE_CODE))) {
                corpInfoEntity.setStatus("2");
                corpInfoEntity.setRequestSerialCode(jsonObject.getJSONObject("data").getString("requestSerialCode"));
            } else {
                corpInfoEntity.setStatus("1");
            }
            corpInfoDao.updateById(corpInfoEntity);
        });
    }

    @Override
    public void checkCorpInfo() {
        List<CorpInfoDTO> corpInfoDTOList = mohurdDao.selectCorpInfo(CHECK_MODE);
        corpInfoDTOList.forEach(corpInfoDTO -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestSerialCode", corpInfoDTO.getRequestSerialCode());
            String responseBody = ReportDataUtils.reportData(jsonObject.toJSONString(), "AsyncHandleResult.Query");
            JSONObject parseObject = JSON.parseObject(responseBody);
            CorpInfoEntity corpInfoEntity = ConvertUtils.sourceToTarget(corpInfoDTO, CorpInfoEntity.class);
            if (SUCCESS_STATUS.equals(parseObject.getString(RESPONSE_CODE))) {
                String status = parseObject.getJSONObject("data").getString("status");
                //如果处理成功更新数据
                if (ARYN_SUCCESS_STATUS.equals(status)) {
                    corpInfoEntity.setStatus("3");
                }
                if (ARYN_ERROR_STATUS.equals(status)) {
                    corpInfoEntity.setStatus("4");
                }
            }
            corpInfoEntity.setArynMessage(responseBody);
            corpInfoEntity.setArynCheckDate(DateUtil.date());
            corpInfoDao.updateById(corpInfoEntity);
        });
    }

    @Override
    public void updateProjectInfo() {
        //查询待修改的项目信息
        List<ProjectInfoDTO> projectInfoDTOList = projectInfoDao.selectProjectInfo(PEND_MODE);

    }

    @Override
    public void postProjectSucInfo() {
        List<ProjectSucontractorDTO> projectSubcontractorsDOTList = projectSucontractorDao.selectSucInfo(PEND_MODE);
        projectSubcontractorsDOTList.forEach(projectSucontractorDTO -> {
            String responseBody = ReportDataUtils.reportData(JSON.toJSONString(projectSucontractorDTO, new MordFilter()), "ProjectSubContractor.Add");
            JSONObject jsonObject = JSON.parseObject(responseBody);
            ProjectSucontractorEntity subcontractEntity = ConvertUtils.sourceToTarget(projectSucontractorDTO, ProjectSucontractorEntity.class);
            subcontractEntity.setMessage(responseBody);
            subcontractEntity.setUpdateDate(DateUtil.date());
            if (SUCCESS_STATUS.equals(jsonObject.getString(RESPONSE_CODE))) {
                subcontractEntity.setStatus("2");
                subcontractEntity.setRequestSerialCode(jsonObject.getJSONObject("data").getString("requestSerialCode"));
            } else {
                subcontractEntity.setStatus("1");
            }
            projectSucontractorDao.updateById(subcontractEntity);
        });
    }

    @Override
    public void checkProjectSucInfo() {
        List<ProjectSucontractorDTO> projectSucontractorDTOList = projectSucontractorDao.selectSucInfo(CHECK_MODE);
        projectSucontractorDTOList.forEach(projectSucontractorDTO -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestSerialCode", projectSucontractorDTO.getRequestSerialCode());
            String responseBody = ReportDataUtils.reportData(jsonObject.toJSONString(), "AsyncHandleResult.Query");
            JSONObject parseObject = JSON.parseObject(responseBody);
            ProjectSucontractorEntity projectSucontractorEntity = ConvertUtils.sourceToTarget(projectSucontractorDTO, ProjectSucontractorEntity.class);
            if (SUCCESS_STATUS.equals(parseObject.getString(RESPONSE_CODE))) {
                String status = parseObject.getJSONObject("data").getString("status");
                //如果处理成功更新数据
                if (ARYN_SUCCESS_STATUS.equals(status)) {
                    projectSucontractorEntity.setStatus("3");
                    projectSucontractorEntity.setPmCode(parseObject.getJSONObject("data").getJSONObject("result").getString("pmCode"));
                }
                if (ARYN_ERROR_STATUS.equals(status)) {
                    projectSucontractorEntity.setStatus("4");
                }
            }
            projectSucontractorEntity.setArynMessage(responseBody);
            projectSucontractorEntity.setArynCheckDate(DateUtil.date());
            projectSucontractorDao.updateById(projectSucontractorEntity);
        });
    }

    @Override
    public void postProjectTeamInfo() {
        List<ProjectTeamInfoDTO> projectTeamInfoDTOList = projectTeamInfoDao.selectProjectTeamInfo(PEND_MODE);
        projectTeamInfoDTOList.forEach(projectTeamInfoDTO -> {
            String responseBody = ReportDataUtils.reportData(JSON.toJSONString(projectTeamInfoDTO, new MordFilter()), "Team.Add");
            JSONObject jsonObject = JSON.parseObject(responseBody);
            ProjectTeamInfoEntity teamInfoEntity = ConvertUtils.sourceToTarget(projectTeamInfoDTO, ProjectTeamInfoEntity.class);
            teamInfoEntity.setMessage(responseBody);
            teamInfoEntity.setUpdateDate(DateUtil.date());
            if (SUCCESS_STATUS.equals(jsonObject.getString(RESPONSE_CODE))) {
                teamInfoEntity.setStatus("2");
                teamInfoEntity.setRequestSerialCode(jsonObject.getJSONObject("data").getString("requestSerialCode"));
            } else {
                teamInfoEntity.setStatus("1");
            }
            projectTeamInfoDao.updateById(teamInfoEntity);
        });
    }

    @Override
    public void checkProjectTeamInfo() {
        List<ProjectTeamInfoDTO> projectTeamInfoDTOList = projectTeamInfoDao.selectProjectTeamInfo(CHECK_MODE);
        projectTeamInfoDTOList.forEach(projectTeamInfoDTO -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestSerialCode", projectTeamInfoDTO.getRequestSerialCode());
            String responseBody = ReportDataUtils.reportData(jsonObject.toJSONString(), "AsyncHandleResult.Query");
            JSONObject parseObject = JSON.parseObject(responseBody);
            ProjectTeamInfoEntity teamInfoEntity = ConvertUtils.sourceToTarget(projectTeamInfoDTO, ProjectTeamInfoEntity.class);
            if (SUCCESS_STATUS.equals(parseObject.getString(RESPONSE_CODE))) {
                String status = parseObject.getJSONObject("data").getString("status");
                //如果处理成功更新数据
                if (ARYN_SUCCESS_STATUS.equals(status)) {
                    teamInfoEntity.setStatus("3");
                    teamInfoEntity.setTeamSysNo(parseObject.getJSONObject("data").getJSONObject("result").getString("teamSysNo"));
                }
                if (ARYN_ERROR_STATUS.equals(status)) {
                    teamInfoEntity.setStatus("4");
                }
            }
            teamInfoEntity.setArynMessage(responseBody);
            teamInfoEntity.setArynCheckDate(DateUtil.date());
            projectTeamInfoDao.updateById(teamInfoEntity);
        });
    }

    @Override
    public void postProjectWorkerInfo() {
        List<ProjectWorkerDTO> projectWorkerDTOList = workerInfoDao.selectProjectWorkerInfo(PEND_MODE);
        projectWorkerDTOList.forEach(projectWorkerDTO -> {
            String responseBody = ReportDataUtils.reportData(JSON.toJSONString(projectWorkerDTO, new MordFilter()), "ProjectWorker.Add");
            JSONObject jsonObject = JSON.parseObject(responseBody);
            List<WorkerInfoEntity> workerInfoEntities = ConvertUtils.sourceToTarget(projectWorkerDTO.getWorkerList(), WorkerInfoEntity.class);
            workerInfoEntities.forEach(workerInfoEntity -> {
                workerInfoEntity.setMessage(responseBody);
                workerInfoEntity.setUpdateDate(DateUtil.date());
                if (SUCCESS_STATUS.equals(jsonObject.getString(RESPONSE_CODE))) {
                    workerInfoEntity.setStatus("2");
                    workerInfoEntity.setRequestSerialCode(jsonObject.getJSONObject("data").getString("requestSerialCode"));
                } else {
                    workerInfoEntity.setStatus("1");
                }
                workerInfoDao.updateById(workerInfoEntity);
            });
        });
    }

    @Override
    public void checkProjectWorkerInfo() {
        QueryWrapper<WorkerInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("STATUS", CHECK_MODE);
        List<WorkerInfoEntity> workerInfoEntityList = workerInfoDao.selectList(wrapper);
        workerInfoEntityList.forEach(workerInfoEntity -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestSerialCode", workerInfoEntity.getRequestSerialCode());
            String responseBody = ReportDataUtils.reportData(jsonObject.toJSONString(), "AsyncHandleResult.Query");
            JSONObject parseObject = JSON.parseObject(responseBody);
            if (SUCCESS_STATUS.equals(parseObject.getString(RESPONSE_CODE))) {
                String status = parseObject.getJSONObject("data").getString("status");
                //如果处理成功更新数据
                if (ARYN_SUCCESS_STATUS.equals(status)) {
                    workerInfoEntity.setStatus("3");
                }
                if (ARYN_ERROR_STATUS.equals(status)) {
                    workerInfoEntity.setStatus("4");
                }
            }
            workerInfoEntity.setArynMessage(responseBody);
            workerInfoEntity.setArynCheckDate(DateUtil.date());
            workerInfoDao.updateById(workerInfoEntity);
        });
    }

    @Override
    public void postWorkerEntryExit() {
        WorkerEntryExitReportDTO workerEntryExitReportDTO = workerEntryExitDao.selectWorkerEntryExit(PEND_MODE);
        String responseBody = ReportDataUtils.reportData(JSON.toJSONString(workerEntryExitReportDTO, new MordFilter()), "WorkerEntryExit.Add");
        JSONObject jsonObject = JSON.parseObject(responseBody);
        List<WorkerEntryExitEntity> workerEntryExitEntities = ConvertUtils.sourceToTarget(workerEntryExitReportDTO.getWorkerList(), WorkerEntryExitEntity.class);
        workerEntryExitEntities.forEach(workerEntryExitEntity -> {
            workerEntryExitEntity.setMessage(responseBody);
            workerEntryExitEntity.setUpdateDate(DateUtil.date());
            if (SUCCESS_STATUS.equals(jsonObject.getString(RESPONSE_CODE))) {
                workerEntryExitEntity.setStatus("2");
                workerEntryExitEntity.setRequestSerialCode(jsonObject.getJSONObject("data").getString("requestSerialCode"));
            } else {
                workerEntryExitEntity.setStatus("1");
            }
            workerEntryExitDao.updateById(workerEntryExitEntity);
        });
    }

    @Override
    public void checkWorkerEntryExit() {
        QueryWrapper<WorkerEntryExitEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("STATUS", CHECK_MODE);
        List<WorkerEntryExitEntity> workerEntryExitEntities = workerEntryExitDao.selectList(wrapper);
        workerEntryExitEntities.forEach(workerEntryExitEntity -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestSerialCode", workerEntryExitEntity.getRequestSerialCode());
            String responseBody = ReportDataUtils.reportData(jsonObject.toJSONString(), "AsyncHandleResult.Query");
            JSONObject parseObject = JSON.parseObject(responseBody);
            if (SUCCESS_STATUS.equals(parseObject.getString(RESPONSE_CODE))) {
                String status = parseObject.getJSONObject("data").getString("status");
                //如果处理成功更新数据
                if (ARYN_SUCCESS_STATUS.equals(status)) {
                    workerEntryExitEntity.setStatus("3");
                }
                if (ARYN_ERROR_STATUS.equals(status)) {
                    workerEntryExitEntity.setStatus("4");
                }
            }
            workerEntryExitEntity.setArynMessage(responseBody);
            workerEntryExitEntity.setArynCheckDate(DateUtil.date());
            workerEntryExitDao.updateById(workerEntryExitEntity);
        });
    }

    @Override
    public void postWorkerContract() {
        List<WorkerContractInfoDTO> workerContractInfoDTOList = workerContractDao.selectProjectWorkerConInfo(PEND_MODE);
        workerContractInfoDTOList.forEach(workerContractInfoDTO -> {
            String responseBody = ReportDataUtils.reportData(JSON.toJSONString(workerContractInfoDTO, new MordFilter()), "WorkerContract.Add");
            JSONObject jsonObject = JSON.parseObject(responseBody);
            List<WorkerContractEntity> workerContractEntities = ConvertUtils.sourceToTarget(workerContractInfoDTO.getContractList(), WorkerContractEntity.class);
            workerContractEntities.forEach(workerContractEntity -> {
                workerContractEntity.setMessage(responseBody);
                workerContractEntity.setUpdateDate(DateUtil.date());
                if (SUCCESS_STATUS.equals(jsonObject.getString(RESPONSE_CODE))) {
                    workerContractEntity.setStatus("2");
                    workerContractEntity.setRequestSerialCode(jsonObject.getJSONObject("data").getString("requestSerialCode"));
                } else {
                    workerContractEntity.setStatus("1");
                }
                workerContractDao.updateById(workerContractEntity);
            });
        });

    }

    @Override
    public void checkWorkerContract() {
        QueryWrapper<WorkerContractEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("STATUS", CHECK_MODE);
        List<WorkerContractEntity> workerContractEntityList = workerContractDao.selectList(wrapper);
        workerContractEntityList.forEach(workerContractEntity -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestSerialCode", workerContractEntity.getRequestSerialCode());
            String responseBody = ReportDataUtils.reportData(jsonObject.toJSONString(), "AsyncHandleResult.Query");
            JSONObject parseObject = JSON.parseObject(responseBody);
            if (SUCCESS_STATUS.equals(parseObject.getString(RESPONSE_CODE))) {
                String status = parseObject.getJSONObject("data").getString("status");
                //如果处理成功更新数据
                if (ARYN_SUCCESS_STATUS.equals(status)) {
                    workerContractEntity.setStatus("3");
                }
                if (ARYN_ERROR_STATUS.equals(status)) {
                    workerContractEntity.setStatus("4");
                }
            }
            workerContractEntity.setArynMessage(responseBody);
            workerContractEntity.setArynCheckDate(DateUtil.date());
            workerContractDao.updateById(workerContractEntity);
        });
    }

    @Override
    public void postWorkerAtt() {
        List<WorkerAttInfoDTO> workerAttInfo = workerAttDao.selectProjectWorkerAttInfo(PEND_MODE);
        workerAttInfo.forEach(workerAttInfoDTO -> {
            String responseBody = ReportDataUtils.reportData(JSON.toJSONString(workerAttInfoDTO, new MordFilter()), "WorkerAttendance.Add");
            JSONObject jsonObject = JSON.parseObject(responseBody);
            List<WorkerAttEntity> workerAttEntityList = ConvertUtils.sourceToTarget(workerAttInfoDTO.getDataList(), WorkerAttEntity.class);
            workerAttEntityList.forEach(workerAttEntity -> {
                workerAttEntity.setMessage(responseBody);
                workerAttEntity.setUpdateDate(DateUtil.date());
                if (SUCCESS_STATUS.equals(jsonObject.getString(RESPONSE_CODE))) {
                    workerAttEntity.setStatus("2");
                    workerAttEntity.setRequestSerialCode(jsonObject.getJSONObject("data").getString("requestSerialCode"));
                } else {
                    workerAttEntity.setStatus("1");
                }
                workerAttDao.updateById(workerAttEntity);
            });
        });
    }

    @Override
    public void checkWorkerAtt() {
        QueryWrapper<WorkerAttEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("STATUS", CHECK_MODE);
        List<WorkerAttEntity> workerAttEntityList = workerAttDao.selectList(wrapper);
        workerAttEntityList.forEach(workerAttEntity -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestSerialCode", workerAttEntity.getRequestSerialCode());
            String responseBody = ReportDataUtils.reportData(jsonObject.toJSONString(), "AsyncHandleResult.Query");
            JSONObject parseObject = JSON.parseObject(responseBody);
            if (SUCCESS_STATUS.equals(parseObject.getString(RESPONSE_CODE))) {
                String status = parseObject.getJSONObject("data").getString("status");
                //如果处理成功更新数据
                if (ARYN_SUCCESS_STATUS.equals(status)) {
                    workerAttEntity.setStatus("3");
                }
                if (ARYN_ERROR_STATUS.equals(status)) {
                    workerAttEntity.setStatus("4");
                }
            }
            workerAttEntity.setArynMessage(responseBody);
            workerAttEntity.setArynCheckDate(DateUtil.date());
            workerAttDao.updateById(workerAttEntity);
        });
    }

    @Override
    public void postProjectTraining() {
        List<ProjectTrainingDTO> projectTrainingDTOList = projectTrainingDao.selectProjectTrainingInfo(PEND_MODE);
        projectTrainingDTOList.forEach(projectTrainingDTO -> {
            String responseBody = ReportDataUtils.reportData(JSON.toJSONString(projectTrainingDTO, new MordFilter()), "ProjectTraining.Add");
            JSONObject jsonObject = JSON.parseObject(responseBody);
            ProjectTrainingEntity projectTrainingEntity = ConvertUtils.sourceToTarget(projectTrainingDTO, ProjectTrainingEntity.class);
            projectTrainingEntity.setMessage(responseBody);
            projectTrainingEntity.setUpdateDate(DateUtil.date());
            if (SUCCESS_STATUS.equals(jsonObject.getString(RESPONSE_CODE))) {
                projectTrainingEntity.setStatus("2");
                projectTrainingEntity.setRequestSerialCode(jsonObject.getJSONObject("data").getString("requestSerialCode"));
            } else {
                projectTrainingEntity.setStatus("1");
            }
            projectTrainingDao.updateById(projectTrainingEntity);
        });
    }

    @Override
    public void checkProjectTraining() {
        QueryWrapper<ProjectTrainingEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("STATUS", CHECK_MODE);
        List<ProjectTrainingEntity> projectTrainingEntityList = projectTrainingDao.selectList(wrapper);
        projectTrainingEntityList.forEach(projectTrainingEntity -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestSerialCode", projectTrainingEntity.getRequestSerialCode());
            String responseBody = ReportDataUtils.reportData(jsonObject.toJSONString(), "AsyncHandleResult.Query");
            JSONObject parseObject = JSON.parseObject(responseBody);
            if (SUCCESS_STATUS.equals(parseObject.getString(RESPONSE_CODE))) {
                String status = parseObject.getJSONObject("data").getString("status");
                //如果处理成功更新数据
                if (ARYN_SUCCESS_STATUS.equals(status)) {
                    projectTrainingEntity.setStatus("3");
                }
                if (ARYN_ERROR_STATUS.equals(status)) {
                    projectTrainingEntity.setStatus("4");
                }
            }
            projectTrainingEntity.setArynMessage(responseBody);
            projectTrainingEntity.setArynCheckDate(DateUtil.date());
            projectTrainingDao.updateById(projectTrainingEntity);
        });
    }

    @Override
    public void postProjectPayroll() {
        List<ProjectPayrollDTO> projectPayrollDTOS = projectPayrollDao.selectProjectPayrollInfo(PEND_MODE);
        projectPayrollDTOS.forEach(projectPayrollDTO -> {
            String responseBody = ReportDataUtils.reportData(JSON.toJSONString(projectPayrollDTO, new MordFilter()), "Payroll.Add");
            JSONObject jsonObject = JSON.parseObject(responseBody);
            ProjectPayrollEntity projectPayrollEntity = ConvertUtils.sourceToTarget(projectPayrollDTO, ProjectPayrollEntity.class);
            projectPayrollEntity.setMessage(responseBody);
            projectPayrollEntity.setUpdateDate(DateUtil.date());
            if (SUCCESS_STATUS.equals(jsonObject.getString(RESPONSE_CODE))) {
                projectPayrollEntity.setStatus("2");
                projectPayrollEntity.setRequestSerialCode(jsonObject.getJSONObject("data").getString("requestSerialCode"));
            } else {
                projectPayrollEntity.setStatus("1");
            }
            projectPayrollDao.updateById(projectPayrollEntity);
        });
    }

    @Override
    public void checkProjectPayroll() {
        QueryWrapper<ProjectPayrollEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("STATUS", CHECK_MODE);
        List<ProjectPayrollEntity> payrollEntityList = projectPayrollDao.selectList(wrapper);
        payrollEntityList.forEach(projectPayrollEntity -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("requestSerialCode", projectPayrollEntity.getRequestSerialCode());
            String responseBody = ReportDataUtils.reportData(jsonObject.toJSONString(), "AsyncHandleResult.Query");
            System.out.println("核验工资返回的数据:" + responseBody);
            JSONObject parseObject = JSON.parseObject(responseBody);
            if (SUCCESS_STATUS.equals(parseObject.getString(RESPONSE_CODE))) {
                String status = parseObject.getJSONObject("data").getString("status");
                //如果处理成功更新数据
                if (ARYN_SUCCESS_STATUS.equals(status)) {
                    projectPayrollEntity.setStatus("3");
                }
                if (ARYN_ERROR_STATUS.equals(status)) {
                    projectPayrollEntity.setStatus("4");
                }
            }
            projectPayrollEntity.setArynMessage(responseBody);
            projectPayrollEntity.setArynCheckDate(DateUtil.date());
            projectPayrollDao.updateById(projectPayrollEntity);
        });
    }
}
