package io.renren.api.service;

/**
 * <AUTHOR>
 * @Date 2020-11-19 8:54
 */
public interface MohurdService {
    /**
     * 上报企业基础信息
     */
    void postCorpInfo();

    /**
     * 校验上报的企业信息
     */
    void checkCorpInfo();

    /**
     * 更新项目信息
     */
    void updateProjectInfo();

    /**
     * 上传项目参建单位信息
     */
    void postProjectSucInfo();

    /**
     * 核验参建单位信息
     */
    void checkProjectSucInfo();

    /**
     * 上报班组信息
     */
    void postProjectTeamInfo();

    /**
     * 核验班组信息
     */
    void checkProjectTeamInfo();

    /**
     * 上报工人信息
     */
    void postProjectWorkerInfo();

    /**
     * 核验工人信息
     */
    void checkProjectWorkerInfo();

    /**
     * 上报工人进退场
     */
    void postWorkerEntryExit();

    /**
     * 核验人员上报情况
     */
    void checkWorkerEntryExit();

    /**
     * 上报工人合同
     */
    void postWorkerContract();

    /**
     * 核验工人合同
     */
    void checkWorkerContract();

    /**
     * 上报工人考勤
     */
    void postWorkerAtt();

    /**
     * 核验考勤记录
     */
    void checkWorkerAtt();

    /**
     * 上报项目培训信息
     */
    void postProjectTraining();

    /**
     * 核验项目培训
     */
    void checkProjectTraining();

    /**
     * 上报工资数据
     */
    void postProjectPayroll();

    /**
     * 核验工资数据
     */
    void checkProjectPayroll();
}
