package io.renren.api.dao;

import io.renren.api.dto.WorkerContractInfoDTO;
import io.renren.api.entity.WorkerContractEntity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 工人合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-12-24
 */
@Mapper
public interface WorkerContractDao extends BaseDao<WorkerContractEntity> {
    /**
     * 查询待上报的工人合同信息
     *
     * @param pendMode
     * @return
     */
    List<WorkerContractInfoDTO> selectProjectWorkerConInfo(String pendMode);
}