package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目参建单位信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("MHD_PROJECT_SUB_CONTRACTOR")
public class ProjectSucontractorEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long cp0201;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 统一社会信用代码，如果无统一社
     * 会信用代码，则填写组织机构代码
     */
    private String corpCode;
    /**
     * 企业名称
     */
    private String corpName;
    /**
     * 参建类型
     */
    private String corpType;
    /**
     * 进场时间(yyyy-MM-dd
     * HH:mm:ss)
     */
    private String entryTime;
    /**
     * 退场时间(yyyy-MM-dd
     * HH:mm:ss)
     */
    private String exitTime;
    /**
     * 项目经理名称
     */
    private String pmName;
    /**
     * 项目经理证件类型
     */
    private String pmIDCardType;
    /**
     * 项目经理证件号码
     */
    private String pmIDCardNumber;
    /**
     * 项目经理电话
     */
    private String pmPhone;
    /**
     * 项目管理部代码，上传管
     * 理人员及管理人员考勤时使用
     */
    private String pmCode;
    /**
     * 0待上报，1上报失败,2待核验，3核验通过，4核验失败
     */
    private String status;
    /**
     * 响应的结果
     */
    private String message;
    /**
     * 响应接口异步查询使用
     */
    private String requestSerialCode;
    /**
     * 异步校验结果
     */
    private String arynMessage;
    /**
     * 异步校验时间
     */
    private Date arynCheckDate;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
}