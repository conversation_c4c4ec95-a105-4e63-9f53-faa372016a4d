package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工人考勤记录表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-01-06
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("MHD_WORKER_ATT")
public class WorkerAttEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
	private Long id;
    /**
     * 工人ID
     */
	private Long ps0201;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 班组ID
     */
	private Long tm0101;
    /**
     * 证件类型
     */
	private String idCardType;
    /**
     * 证件号码
     */
	private String idCardNumber;
    /**
     * 刷卡时间，yyyy-MM-dd HH:mm:ss(原字段date)
     */
	private String attDate;
    /**
     * 刷卡进出方向
     */
	private String direction;
    /**
     * 刷卡近照
     */
	private String image;
    /**
     * 通道的名称
     */
	private String channel;
    /**
     * 通行方式
     */
	private String attendType;
    /**
     * WGS84经度
     */
	private BigDecimal lng;
    /**
     * WGS84纬度
     */
	private BigDecimal lat;
    /**
     * 工人体温
     */
	private BigDecimal temperature;
    /**
     * 0待上报，1上报失败,2待核验，3核验通过，4核验失败
     */
	private String status;
    /**
     * 响应的结果
     */
	private String message;
    /**
     * 响应接口异步查询使用
     */
	private String requestSerialCode;
    /**
     * 异步校验结果
     */
	private String arynMessage;
    /**
     * 异步校验时间
     */
	private Date arynCheckDate;
	/**
     * 创建时间
     */
	private Date createDate;
	/**
	 * 更新时间
	 */
	private Date updateDate;
}