package io.renren.api.dto;

import io.renren.annotation.aes.AesEncField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 工人进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-12-02
 */
@Data
@ApiModel(value = "工人进退场信息")
public class WorkerEntryExitDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "工人ID")
    private Long ps0201;

    @ApiModelProperty(value = "所属项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "所属班组ID")
    private Long tm0101;

    @ApiModelProperty(value = "证件类型")
    private String idCardType;

    @ApiModelProperty(value = "证件号码")
	@AesEncField
    private String idCardNumber;

    @ApiModelProperty(value = "工人进退场类型(实际上报字段为type)")
    private String type;

    @ApiModelProperty(value = "凭证扫描件")
    private String voucher;

    @ApiModelProperty(value = "进退场日期(yyyy-MM-dd,上报的字段实际是date)")
    private String date;

    @ApiModelProperty(value = "响应接口异步查询使用")
    private String requestSerialCode;
}