package io.renren.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.renren.annotation.aes.AesEncField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 工人合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-12-24
 */
@Data
@ApiModel(value = "工人合同信息")
public class WorkerContractDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @JSONField(serialize = false)
    private Long id;

    @ApiModelProperty(value = "工人ID")
    @JSONField(serialize = false)
    private Long ps0201;

    @ApiModelProperty(value = "所属项目ID")
    @JSONField(serialize = false)
    private Long pj0101;

    @ApiModelProperty(value = "企业统一社会信用代码或者组织机构代码")
    private String corpCode;

    @ApiModelProperty(value = "企业名称")
    private String corpName;

    @ApiModelProperty(value = "证件类型")
    private String idCardType;

    @ApiModelProperty(value = "证件号码")
    @AesEncField
    private String idCardNumber;

    @ApiModelProperty(value = "合同期限类型")
    private String contractPeriodType;

    @ApiModelProperty(value = "生效日期(yyyy-MM-dd)")
    private String startDate;

    @ApiModelProperty(value = "失效日期(yyyy-MM-dd)")
    private String endDate;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "计量单价(元)")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "响应接口异步查询使用")
	@JSONField(serialize = false)
	private String requestSerialCode;

    @ApiModelProperty(value = "工人合同附件")
    private List<WorkerContractFileDTO> attachments;
}