package io.renren.api.dao;

import io.renren.api.dto.WorkerEntryExitReportDTO;
import io.renren.api.entity.WorkerEntryExitEntity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

/**
 * 工人进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-12-02
 */
@Mapper
public interface WorkerEntryExitDao extends BaseDao<WorkerEntryExitEntity> {
    /**
     * 查询待上报的工人进退场信息
     * @param pendMode 状态
     * @return
     */
    WorkerEntryExitReportDTO selectWorkerEntryExit(String pendMode);
}