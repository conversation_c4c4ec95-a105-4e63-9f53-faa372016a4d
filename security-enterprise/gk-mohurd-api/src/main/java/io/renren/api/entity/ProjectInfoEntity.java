package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("MHD_PROJECT_INFO")
public class ProjectInfoEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private BigDecimal pj0101;
    /**
     * 总承包单位统一社会信用代码，
     * 如果无统一社会信用代码，则填
     * 写组织机构代码
     */
    private String contractorCorpCode;
    /**
     * 总承包单位名称
     */
    private String contractorCorpName;
    /**
     * 项目名称
     */
    private String name;
    /**
     * 项目简介
     */
    private String description;
    /**
     * 项目分类
     */
    private String category;
    /**
     * 建设单位名称
     */
    private String buildCorpName;
    /**
     * 建设单位统一社会信用代码，如
     * 果无统一社会信用代码，则填写
     * 组织机构代码
     */
    private String buildCorpCode;
    /**
     * 建设用地规划许可证编号
     */
    private String buildPlanNum;
    /**
     * 建设工程规划许可证编号
     */
    private String prjPlanNum;
    /**
     * 项目所在地
     */
    private String areaCode;
    /**
     * 总投资(万元)
     */
    private BigDecimal invest;
    /**
     * 总面积(平方米)
     */
    private BigDecimal buildingArea;
    /**
     * 总长度(米)
     */
    private BigDecimal buildingLength;
    /**
     * 开工日期(yyyy-MM-dd)
     */
    private String startDate;
    /**
     * 竣工日期(yyyy-MM-dd)
     */
    private String completeDate;
    /**
     * 联系人姓名
     */
    private String linkMan;
    /**
     * 联系人办公电话
     */
    private String linkPhone;
    /**
     * 项目状态
     */
    private String prjStatus;
    /**
     * WGS84 经度
     */
    private BigDecimal lat;
    /**
     * WGS84 纬度
     */
    private BigDecimal lng;
    /**
     * 项目地点
     */
    private String address;
    /**
     * 立项文号
     */
    private String approvalNum;
    /**
     * 立项级别
     */
    private String approvalLevelNum;
    /**
     * 建设规模
     */
    private String prjSize;
    /**
     * 建设性质
     */
    private String propertyNum;
    /**
     * 工程用途
     */
    private String functionNum;
    /**
     * 国籍或地区
     */
    private String nationNum;
    /**
     * 住建部项目CODE
     */
    private String projectCode;
    /**
     * 0待上报，1上报失败,2待核验，3核验通过，4核验失败
     */
    private String status;
    /**
     * 响应的结果
     */
    private String message;
    /**
     * 响应接口异步查询使用
     */
    private String requestSerialCode;
    /**
     * 异步校验结果
     */
    private String arynMessage;
    /**
     * 异步校验时间
     */
    private Date arynCheckDate;

    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
}