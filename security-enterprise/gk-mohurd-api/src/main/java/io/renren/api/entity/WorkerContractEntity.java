package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 工人合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-12-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("MHD_WORKER_CONTRACT")
public class WorkerContractEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 工人ID
     */
    private Long ps0201;
    /**
     * 所属项目ID
     */
    private Long pj0101;
    /**
     * 企业统一社会信用代码或者组织机构代码
     */
    private String corpCode;
    /**
     * 企业名称
     */
    private String corpName;
    /**
     * 证件类型
     */
    private String idCardType;
    /**
     * 证件号码
     */
    private String idCardNumber;
    /**
     * 合同期限类型
     */
    private String contractPeriodType;
    /**
     * 生效日期(yyyy-MM-dd)
     */
    private String startDate;
    /**
     * 失效日期(yyyy-MM-dd)
     */
    private String endDate;
    /**
     * 计量单位
     */
    private String unit;
    /**
     * 计量单价(元)
     */
    private BigDecimal unitPrice;
    /**
     * 0待上报，1上报失败,2待核验，3核验通过，4核验失败
     */
    private String status;
    /**
     * 响应的结果
     */
    private String message;
    /**
     * 响应接口异步查询使用
     */
    private String requestSerialCode;
    /**
     * 异步校验结果
     */
    private String arynMessage;
    /**
     * 异步校验时间
     */
    private Date arynCheckDate;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
}