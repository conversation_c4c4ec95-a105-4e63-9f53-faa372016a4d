package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目人员工资
 *
 * <AUTHOR>
 * @since 1.0.0 2021-07-13
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("MHD_PROJECT_PAYROLL")
public class ProjectPayrollEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
	private Long id;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 所属班组ID
     */
	private Long tm0101;
    /**
     * 所属企业统一社会信用代码
     */
	private String corpCode;
    /**
     * 工人所属企业名称
     */
	private String corpName;
    /**
     * 发放工资的月份(yyyy-MM)
     */
	private String payMonth;
    /**
     * 0待上报，1上报失败,2待核验，3核验通过，4核验失败
     */
	private String status;
    /**
     * 响应的结果
     */
	private String message;
    /**
     * 响应接口异步查询使用
     */
	private String requestSerialCode;
    /**
     * 异步校验结果
     */
	private String arynMessage;
    /**
     * 异步校验时间
     */
	private Date arynCheckDate;
	/**
     * 创建时间
     */
	private Date createDate;
	/**
	 * 更新时间
	 */
	private Date updateDate;
}