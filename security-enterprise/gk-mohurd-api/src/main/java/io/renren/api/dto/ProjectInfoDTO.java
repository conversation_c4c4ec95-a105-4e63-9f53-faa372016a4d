package io.renren.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Data
@ApiModel(value = "项目信息")
public class ProjectInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @JSONField(serialize = false)
    private Long pj0101;

    @ApiModelProperty(value = "总承包单位统一社会信用代码")
    private String contractorCorpCode;

    @ApiModelProperty(value = "总承包单位名称")
    private String contractorCorpName;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "项目简介")
    private String description;

    @ApiModelProperty(value = "项目分类")
    private String category;

    @ApiModelProperty(value = "建设单位名称")
    private String buildCorpName;

    @ApiModelProperty(value = "建设单位统一社会信用代码")
    private String buildCorpCode;

    @ApiModelProperty(value = "建设用地规划许可证编号")
    private String buildPlanNum;

    @ApiModelProperty(value = "建设工程规划许可证编号")
    private String prjPlanNum;

    @ApiModelProperty(value = "项目所在地")
    private String areaCode;

    @ApiModelProperty(value = "总投资(万元)")
    private BigDecimal invest;

    @ApiModelProperty(value = "总面积(平方米)")
    private BigDecimal buildingArea;

    @ApiModelProperty(value = "总长度(米)")
    private BigDecimal buildingLength;

    @ApiModelProperty(value = "开工日期")
    private String startDate;

    @ApiModelProperty(value = "竣工日期")
    private String completeDate;

    @ApiModelProperty(value = "联系人姓名")
    private String linkMan;

    @ApiModelProperty(value = "联系人办公电话")
    private String linkPhone;

    @ApiModelProperty(value = "项目状态")
    private String prjStatus;

    @ApiModelProperty(value = "WGS84 经度")
    private BigDecimal lat;

    @ApiModelProperty(value = "WGS84 纬度")
    private BigDecimal lng;

    @ApiModelProperty(value = "项目地点")
    private String address;

    @ApiModelProperty(value = "立项文号")
    private String approvalNum;

    @ApiModelProperty(value = "立项级别")
    private String approvalLevelNum;

    @ApiModelProperty(value = "建设规模")
    private String prjSize;

    @ApiModelProperty(value = "建设性质")
    private String propertyNum;

    @ApiModelProperty(value = "工程用途")
    private String functionNum;

    @ApiModelProperty(value = "国籍或地区")
    private String nationNum;

    @ApiModelProperty(value = "住建部项目CODE")
    private String projectCode;

    @ApiModelProperty(value = "响应接口异步查询使用")
    @JSONField(serialize = false)
    private String requestSerialCode;

    @ApiModelProperty(value = "施工许可证")
    private List<ProjectBuilderLicenseDTO> projectBuilderLicenseDTOList;
}