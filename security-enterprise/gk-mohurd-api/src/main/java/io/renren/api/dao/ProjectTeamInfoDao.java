package io.renren.api.dao;


import io.renren.api.dto.ProjectTeamInfoDTO;
import io.renren.api.entity.ProjectTeamInfoEntity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目班组信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Mapper
public interface ProjectTeamInfoDao extends BaseDao<ProjectTeamInfoEntity> {
    /**
     * 查询班组信息
     * @param pendMode
     * @return
     */
    List<ProjectTeamInfoDTO> selectProjectTeamInfo(String pendMode);
}