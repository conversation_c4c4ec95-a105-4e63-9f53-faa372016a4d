package io.renren.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 工人进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-12-02
 */
@Data
@ApiModel(value = "工人进退场信息")
public class WorkerEntryExitReportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "住建部项目CODE")
    private String projectCode;

    @ApiModelProperty(value = "所在企业统一社会信用代码")
    private String corpCode;

    @ApiModelProperty(value = "所在企业名称")
    private String corpName;

    @ApiModelProperty(value = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "班组编号")
    private String teamSysNo;

    @ApiModelProperty(value = "人员进退场情况")
    List<WorkerEntryExitDTO> workerList;

}