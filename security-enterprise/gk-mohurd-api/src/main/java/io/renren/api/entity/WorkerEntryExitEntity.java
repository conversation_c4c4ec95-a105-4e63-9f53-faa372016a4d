package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 工人进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-12-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("MHD_WORKER_ENTRY_EXIT")
public class WorkerEntryExitEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 工人ID
     */
    private Long ps0201;
    /**
     * 所属项目ID
     */
    private Long pj0101;
    /**
     * 所属班组ID
     */
    private Long tm0101;
    /**
     * 证件类型
     */
    private String idCardType;
    /**
     * 证件号码
     */
    private String idCardNumber;
    /**
     * 工人进退场类型(实际上报字段为type)
     */
    private String entryExitType;
    /**
     * 凭证扫描件
     */
    private String voucher;
    /**
     * 进退场日期(yyyy-MM-dd,上报的字段实际是date)
     */
    private String entryExitDate;
    /**
     * 0待上报，1上报失败,2待核验，3核验通过，4核验失败
     */
    private String status;
    /**
     * 响应的结果
     */
    private String message;
    /**
     * 响应接口异步查询使用
     */
    private String requestSerialCode;
    /**
     * 异步校验结果
     */
    private String arynMessage;
    /**
     * 异步校验时间
     */
    private Date arynCheckDate;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
}