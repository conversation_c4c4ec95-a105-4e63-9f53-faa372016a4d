package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业基本信息上报表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("MHD_CORP_INFO")
public class CorpInfoEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 企业统一社会信用代码或者组织机构代码
     */
    private String corpCode;
    /**
     * 企业名称
     */
    private String corpName;
    /**
     * 单位性质
     */
    private String corpType;
    /**
     * 工商营业执照注册号
     */
    private String licenseNum;
    /**
     * 企业注册地区编码
     */
    private String areaCode;
    /**
     * 企业营业地址
     */
    private String address;
    /**
     * 邮政编码
     */
    private String zipCode;
    /**
     * 法定代表人姓名
     */
    private String legalMan;
    /**
     * 法定代表人职务
     */
    private String legalManDuty;
    /**
     * 法定代表人职称
     */
    private String legaManProTitle;
    /**
     * 法定代表人证件类型
     */
    private String legalManIDCardType;
    /**
     * 法定代表人证件号码
     */
    private String legalManIDCardNumber;
    /**
     * 注册资本（万元）
     */
    private BigDecimal regCapital;
    /**
     * 实收资本(万元)
     */
    private BigDecimal factRegCapital;
    /**
     * 注册资本币种
     */
    private String capitalCurrencyType;
    /**
     * 注册日期
     */
    private String registerDate;
    /**
     * 成立日期
     */
    private String establishDate;
    /**
     * 办公电话
     */
    private String officePhone;
    /**
     * 传真号码
     */
    private String faxNumber;
    /**
     * 联系人姓名
     */
    private String linkman;
    /**
     * 联系人办公电话
     */
    private String linkTel;
    /**
     * 企业联系邮箱
     */
    private String email;
    /**
     * 企业网址
     */
    private String website;
    /**
     * 企业备注
     */
    private String remark;
    /**
     * 0待上报，1待核验，2核验通过，3核验失败
     */
    private String status;
    /**
     * 响应的结果
     */
    private String message;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 异步接口响应
     */
    private String requestSerialCode;
    /**
     * 异步校验信息
     */
    private String arynMessage;
    /**
     * 异步校验时间
     */
    private Date arynCheckDate;

}