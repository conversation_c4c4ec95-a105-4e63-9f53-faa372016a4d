package io.renren.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.renren.annotation.aes.AesEncField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 项目参建单位信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Data
@ApiModel(value = "项目参建单位信息")
public class ProjectSucontractorDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @JSONField(serialize = false)
    private Long cp0201;

    @ApiModelProperty(value = "项目ID")
    @JSONField(serialize = false)
    private Long pj0101;

    @ApiModelProperty(value = "统一社会信用代码")
    private String corpCode;

    @ApiModelProperty(value = "住建部项目Code")
    private String projectCode;

    @ApiModelProperty(value = "企业名称")
    private String corpName;

    @ApiModelProperty(value = "参建类型")
    private String corpType;

    @ApiModelProperty(value = "进场时间")
    private String entryTime;

    @ApiModelProperty(value = "退场时间")
    private String exitTime;

    @ApiModelProperty(value = "项目经理名称")
    private String pmName;

    @ApiModelProperty(value = "项目经理证件类型")
    private String pmIDCardType;

    @ApiModelProperty(value = "项目经理证件号码")
    @AesEncField
    private String pmIDCardNumber;

    @ApiModelProperty(value = "项目经理电话")
    private String pmPhone;

    @ApiModelProperty(value = "项目管理部代码")
    private String pmCode;

    @ApiModelProperty(value = "响应接口异步查询使用")
    @JSONField(serialize = false)
    private String requestSerialCode;

    @ApiModelProperty(value = "发放工资的银行信息")
    private List<SucontractorBankInfoDTO> bankInfoDTOList;
}