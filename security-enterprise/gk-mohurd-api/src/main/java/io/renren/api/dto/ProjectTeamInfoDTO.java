package io.renren.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.renren.annotation.aes.AesEncField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目班组信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Data
@ApiModel(value = "项目班组信息")
public class ProjectTeamInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
	@JSONField(serialize = false)
	private Long tm0101;

    @ApiModelProperty(value = "项目ID")
	@JSONField(serialize = false)
	private Long pj0101;

    @ApiModelProperty(value = "所属参建单位ID")
	@JSONField(serialize = false)
	private Long cp0201;

	@ApiModelProperty(value = "项目code(住建部)")
	private String projectCode;

    @ApiModelProperty(value = "班组所在企业统一社会信用代码")
    private String corpCode;

    @ApiModelProperty(value = "班组所在企业名称")
    private String corpName;

    @ApiModelProperty(value = "班组名称(同一个项目下面不能重复)")
    private String teamName;

    @ApiModelProperty(value = "责任人姓名(班组所在企业负责人)")
    private String responsiblePersonName;

    @ApiModelProperty(value = "责任人联系电话")
    private String responsiblePersonPhone;

    @ApiModelProperty(value = "责任人证件类型(此处字段超过长度，以文档为准)")
    private String responsiblePersonIDCardType;

    @ApiModelProperty(value = "责任人证件号码")
    @AesEncField
    private String responsiblePersonIDNumber;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "进场日期(yyyy-MM-dd)")
    private String entryTime;

    @ApiModelProperty(value = "退场日期(yyyy-MM-dd)")
    private String exitTime;

    @ApiModelProperty(value = "响应接口异步查询使用")
    @JSONField(serialize = false)
    private String requestSerialCode;

}