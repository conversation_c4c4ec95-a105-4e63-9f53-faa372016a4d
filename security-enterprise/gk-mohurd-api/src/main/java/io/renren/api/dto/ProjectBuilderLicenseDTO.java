package io.renren.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目施工许可证
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Data
@ApiModel(value = "项目施工许可证")
public class ProjectBuilderLicenseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "工程名称")
    private String prjName;

    @ApiModelProperty(value = "施工许可证编号")
    private String builderLicenseNum;

}