package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 项目工资单明细表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-07-13
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("MHD_PROJECT_PAYROLL_WORKER")
public class ProjectPayrollWorkerEntity  {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId
	private Long payrollWorkerId;
    /**
     * 项目工资单ID
     */
	private Long id;
    /**
     * 证件类型
     */
	private String idCardType;
    /**
     * 证件号码
     */
	private String idCardNumber;
    /**
     * 出勤天数
     */
	private Short days;
    /**
     * 总工时(小时)
     */
	private String workHours;
    /**
     * 工人工资卡号
     */
	private String payRollBankCardNumber;
    /**
     * 工人工资卡银行代码
     */
	private String payRollBankCode;
    /**
     * 工人工资卡开户行名称
     */
	private String payRollBankName;
    /**
     * 工资代发银行卡号
     */
	private String payBankCardNumber;
    /**
     * 工资代发银行代码
     */
	private String payBankCode;
    /**
     * 工资代发开户行名称
     */
	private String payBankName;
    /**
     * 应发金额(元)
     */
	private BigDecimal totalPayAmount;
    /**
     * 实发金额(元)
     */
	private BigDecimal actualAmount;
    /**
     * 是否为补发
     */
	private String isBackPay;
    /**
     * 发放日期(yyyy-MM-dd)
     */
	private String balanceDate;
    /**
     * 补发月份(yyyy-MM)
     */
	private String backPayMonth;
    /**
     * 第三方工资单编号
     */
	private String thirdPayRollCode;

}