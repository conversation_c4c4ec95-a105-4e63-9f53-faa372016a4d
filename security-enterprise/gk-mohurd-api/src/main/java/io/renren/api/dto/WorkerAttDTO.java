package io.renren.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.renren.annotation.aes.AesEncField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工人考勤记录表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-01-06
 */
@Data
@ApiModel(value = "工人考勤记录表")
public class WorkerAttDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
	@JSONField(serialize = false)
	private Long id;

    @ApiModelProperty(value = "工人ID")
	@JSONField(serialize = false)
	private Long ps0201;

    @ApiModelProperty(value = "项目ID")
	@JSONField(serialize = false)
	private Long pj0101;

    @ApiModelProperty(value = "班组ID")
	@JSONField(serialize = false)
	private Long tm0101;

    @ApiModelProperty(value = "证件类型")
    private String idCardType;

    @ApiModelProperty(value = "证件号码")
    @AesEncField
    private String idCardNumber;

    @ApiModelProperty(value = "刷卡时间，yyyy-MM-dd HH:mm:ss(原字段date)")
    private String date;

    @ApiModelProperty(value = "刷卡进出方向")
    private String direction;

    @ApiModelProperty(value = "刷卡近照")
    private String image;

    @ApiModelProperty(value = "通道的名称")
    private String channel;

    @ApiModelProperty(value = "通行方式")
    private String attendType;

    @ApiModelProperty(value = "WGS84经度")
    private BigDecimal lng;

    @ApiModelProperty(value = "WGS84纬度")
    private BigDecimal lat;

    @ApiModelProperty(value = "工人体温")
    private BigDecimal temperature;

    @ApiModelProperty(value = "响应接口异步查询使用")
	@JSONField(serialize = false)
	private String requestSerialCode;

}