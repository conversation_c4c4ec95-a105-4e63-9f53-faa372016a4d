package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目班组信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("MHD_PROJECT_TEAM_INFO")
public class ProjectTeamInfoEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
	private Long tm0101;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 所属参建单位ID
     */
	private Long cp0201;
    /**
     * 班组所在企业统一社会信用代码
     */
	private String corpCode;
    /**
     * 班组所在企业名称
     */
	private String corpName;
    /**
     * 班组名称(同一个项目下面不能重复)
     */
	private String teamName;
    /**
     * 责任人姓名(班组所在企业负责人)
     */
	private String responsiblePersonName;
    /**
     * 责任人联系电话
     */
	private String responsiblePersonPhone;
    /**
     * 责任人证件类型(此处字段超过长度，以文档为准)
     */
	private String respPersonIDCardType;
    /**
     * 责任人证件号码
     */
	private String responsiblePersonIDNumber;
    /**
     * 备注
     */
	private String remark;
    /**
     * 进场日期(yyyy-MM-dd)
     */
	private String entryTime;
    /**
     * 退场日期(yyyy-MM-dd)
     */
	private String exitTime;
    /**
     * 班组编号(住建部返回)
     */
	private String teamSysNo;
    /**
     * 0待上报，1上报失败,2待核验，3核验通过，4核验失败
     */
	private String status;
    /**
     * 响应的结果
     */
	private String message;
    /**
     * 响应接口异步查询使用
     */
	private String requestSerialCode;
    /**
     * 异步校验结果
     */
	private String arynMessage;
    /**
     * 异步校验时间
     */
	private Date arynCheckDate;
	/**
     * 创建时间
     */
	private Date createDate;
	/**
	 * 更新时间
	 */
	private Date updateDate;
}