package io.renren.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工人合同附件
 *
 * <AUTHOR>
 * @since 1.0.0 2020-12-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("MHD_WORKER_CONTRACT_FILE")
public class WorkerContractFileEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 工人ID
     */
    private Long ps0201;
    /**
     * 附件名称
     */
    private String name;
    /**
     * 附件地址
     */
    private String data;
}