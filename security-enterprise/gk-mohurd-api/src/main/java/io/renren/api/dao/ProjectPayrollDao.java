package io.renren.api.dao;

import io.renren.api.dto.ProjectPayrollDTO;
import io.renren.api.entity.ProjectPayrollEntity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目人员工资
 *
 * <AUTHOR>
 * @since 1.0.0 2021-07-13
 */
@Mapper
public interface ProjectPayrollDao extends BaseDao<ProjectPayrollEntity> {
    /**
     * 查询待上报的工资数据
     * @param pendMode
     * @return
     */
    List<ProjectPayrollDTO> selectProjectPayrollInfo(String pendMode);
}