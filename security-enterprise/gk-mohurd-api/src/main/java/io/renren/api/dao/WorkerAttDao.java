package io.renren.api.dao;

import io.renren.api.dto.WorkerAttInfoDTO;
import io.renren.api.entity.WorkerAttEntity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 工人考勤记录表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-01-06
 */
@Mapper
public interface WorkerAttDao extends BaseDao<WorkerAttEntity> {
    /**
     * 查询待上报的考勤记录
     * @param pendMode
     * @return
     */
    List<WorkerAttInfoDTO> selectProjectWorkerAttInfo(String pendMode);
}