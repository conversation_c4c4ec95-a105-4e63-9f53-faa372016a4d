package io.renren.api.dto;

import io.renren.annotation.aes.AesEncField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 参建单位发放工资银行信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Data
@ApiModel(value = "参建单位发放工资银行信息")
public class SucontractorBankInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "参建单位ID")
    private Long cp0201;

    @ApiModelProperty(value = "银行代码")
    private String bankCode;

    @ApiModelProperty(value = "银行支行名称")
    private String bankName;

    @ApiModelProperty(value = "银行卡号")
    @AesEncField
    private String bankNumber;

    @ApiModelProperty(value = "银行联号")
    private String bankLinkNumber;

}