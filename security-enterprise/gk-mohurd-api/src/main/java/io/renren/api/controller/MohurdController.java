package io.renren.api.controller;

import io.renren.api.service.MohurdService;
import io.renren.common.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2020-11-19 8:52
 */
@RestController
@RequestMapping("mohurd/reportData")
@Api(tags = "住建部接口上报")
public class MohurdController {
    @Autowired
    private MohurdService mohurdService;


    /**
     * 上报企业信息
     *
     * @return
     */
    @PostMapping("postCorpInfo")
    @ApiOperation("企业上报")
    public Result postCorpInfo() {
        mohurdService.postCorpInfo();
        return new Result();
    }

    /**
     * 企业上报核验
     *
     * @return
     */
    @PostMapping("checkCorpInfo")
    @ApiOperation("企业上报核验")
    public Result checkCorpInfo() {
        mohurdService.checkCorpInfo();
        return new Result();
    }

    /**
     * 更新项目信息
     *
     * @return
     */
    @PostMapping("updateProjectInfo")
    @ApiOperation("更新项目信息")
    public Result updateProjectInfo() {
        mohurdService.updateProjectInfo();
        return new Result();
    }

    /**
     * 上传参建单位信息
     *
     * @return
     */
    @PostMapping("postProjectSucInfo")
    @ApiOperation("参建单位上报")
    public Result postProjectSucInfo() {
        mohurdService.postProjectSucInfo();
        return new Result();
    }

    /**
     * 核验参建单位
     *
     * @return
     */
    @PostMapping("checkProjectSucInfo")
    @ApiOperation("参建单位核验")
    public Result checkProjectSucInfo() {
        mohurdService.checkProjectSucInfo();
        return new Result();
    }

    /**
     * 上报班组信息
     *
     * @return
     */
    @PostMapping("postProjectTeamInfo")
    @ApiOperation("班组上报")
    public Result postProjectTeamInfo() {
        mohurdService.postProjectTeamInfo();
        return new Result();
    }

    /**
     * 核验班组信息
     *
     * @return
     */
    @PostMapping("checkProjectTeamInfo")
    @ApiOperation("班组核验")
    public Result checkProjectTeamInfo() {
        mohurdService.checkProjectTeamInfo();
        return new Result();
    }

    /**
     * 上报项目工人信息
     *
     * @return
     */
    @PostMapping("postProjectWorkerInfo")
    @ApiOperation("工人上报")
    public Result postProjectWorkerInfo() {
        mohurdService.postProjectWorkerInfo();
        return new Result();
    }

    /**
     * 核验项目工人信息
     *
     * @return
     */
    @PostMapping("checkProjectWorkerInfo")
    @ApiOperation("工人信息核验")
    public Result checkProjectWorkerInfo() {
        mohurdService.checkProjectWorkerInfo();
        return new Result();
    }

    /**
     * 上报工人进退场情况
     *
     * @return
     */
    @PostMapping("postWorkerEntryExit")
    @ApiOperation("工人进退场上报")
    public Result postWorkerEntryExit() {
        mohurdService.postWorkerEntryExit();
        return new Result();
    }

    /**
     * 核验工人进退场情况
     *
     * @return
     */
    @PostMapping("checkWorkerEntryExit")
    @ApiOperation("核验工人进退场")
    public Result checkWorkerEntryExit() {
        mohurdService.checkWorkerEntryExit();
        return new Result();
    }

    /**
     * 上报工人合同
     *
     * @return
     */
    @PostMapping("postWorkerContract")
    @ApiOperation("工人合同上报")
    public Result postWorkerContract() {
        mohurdService.postWorkerContract();
        return new Result();
    }

    /**
     * 核验工人合同
     *
     * @return
     */
    @PostMapping("checkWorkerContract")
    @ApiOperation("核验工人合同")
    public Result checkWorkerContract() {
        mohurdService.checkWorkerContract();
        return new Result();
    }
    /**
     * 上报工人考勤
     *
     * @return
     */
    @PostMapping("postWorkerAtt")
    @ApiOperation("考勤上报")
    public Result postWorkerAtt() {
        mohurdService.postWorkerAtt();
        return new Result();
    }
    /**
     * 核验考勤上报数据
     *
     * @return
     */
    @PostMapping("checkWorkerAtt")
    @ApiOperation("考勤核验")
    public Result checkWorkerAtt() {
        mohurdService.checkWorkerAtt();
        return new Result();
    }
    /**
     * 上报项目培训数据
     *
     * @return
     */
    @PostMapping("postProjectTraining")
    @ApiOperation("项目培训上报")
    public Result postProjectTraining() {
        mohurdService.postProjectTraining();
        return new Result();
    }
    /**
     * 项目培训核验
     *
     * @return
     */
    @PostMapping("checkProjectTraining")
    @ApiOperation("培训核验")
    public Result checkProjectTraining() {
        mohurdService.checkProjectTraining();
        return new Result();
    }
    /**
     * 上报项目人员工资数据
     *
     * @return
     */
    @PostMapping("postProjectPayroll")
    @ApiOperation("上报项目人员工资数据")
    public Result postProjectPayroll() {
        mohurdService.postProjectPayroll();
        return new Result();
    }
    /**
     * 核验项目人员工资数据
     *
     * @return
     */
    @PostMapping("checkProjectPayroll")
    @ApiOperation("核验项目人员工资数据")
    public Result checkProjectPayroll() {
        mohurdService.checkProjectPayroll();
        return new Result();
    }
}
