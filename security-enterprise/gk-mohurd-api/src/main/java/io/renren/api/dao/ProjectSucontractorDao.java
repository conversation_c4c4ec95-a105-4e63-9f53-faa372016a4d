package io.renren.api.dao;

import io.renren.api.dto.ProjectSucontractorDTO;
import io.renren.api.entity.ProjectSucontractorEntity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目参建单位信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-23
 */
@Mapper
public interface ProjectSucontractorDao extends BaseDao<ProjectSucontractorEntity> {
    /**
     * 查询待上报的参建单位信息
     * @param pendMode 处理状态
     * @return
     */
    List<ProjectSucontractorDTO> selectSucInfo(String pendMode);
}