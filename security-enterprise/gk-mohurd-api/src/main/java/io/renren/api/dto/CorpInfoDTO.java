package io.renren.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.renren.annotation.aes.AesEncField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 企业基本信息上报表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-19
 */
@Data
@ApiModel(value = "企业基本信息上报表")
public class CorpInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @JSONField(serialize = false)
    private Long id;

    @ApiModelProperty(value = "企业统一社会信用代码或者组织机构代码")
    private String corpCode;

    @ApiModelProperty(value = "企业名称")
    private String corpName;

    @ApiModelProperty(value = "单位性质")
    private String corpType;

    @ApiModelProperty(value = "工商营业执照注册号")
    private String licenseNum;

    @ApiModelProperty(value = "企业注册地区编码")
    private String areaCode;

    @ApiModelProperty(value = "企业营业地址")
    private String address;

    @ApiModelProperty(value = "邮政编码")
    private String zipCode;

    @ApiModelProperty(value = "法定代表人姓名")
    private String legalMan;

    @ApiModelProperty(value = "法定代表人职务")
    private String legalManDuty;

    @ApiModelProperty(value = "法定代表人职称")
    private String legaManProTitle;

    @ApiModelProperty(value = "法定代表人证件类型")
    private String legalManIDCardType;

    @ApiModelProperty(value = "法定代表人证件号码")
    @AesEncField
    private String legalManIDCardNumber;

    @ApiModelProperty(value = "注册资本（万元）")
    private BigDecimal regCapital;

    @ApiModelProperty(value = "实收资本(万元)")
    private BigDecimal factRegCapital;

    @ApiModelProperty(value = "注册资本币种")
    private String capitalCurrencyType;

    @ApiModelProperty(value = "注册日期")
    private String registerDate;

    @ApiModelProperty(value = "成立日期")
    private String establishDate;

    @ApiModelProperty(value = "办公电话")
    private String officePhone;

    @ApiModelProperty(value = "传真号码")
    private String faxNumber;

    @ApiModelProperty(value = "联系人姓名")
    private String linkman;

    @ApiModelProperty(value = "联系人办公电话")
    private String linkTel;

    @ApiModelProperty(value = "企业联系邮箱")
    private String email;

    @ApiModelProperty(value = "企业网址")
    private String website;

    @ApiModelProperty(value = "企业备注")
    private String remark;

    @ApiModelProperty(value = "接口校验字段")
    @JSONField(serialize = false)
    private String requestSerialCode;
}