package io.renren.api.dao;

import io.renren.api.dto.ProjectWorkerDTO;
import io.renren.api.entity.WorkerInfoEntity;
import io.renren.common.dao.BaseDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-11-24
 */
@Mapper
public interface WorkerInfoDao extends BaseDao<WorkerInfoEntity> {
    /**
     * 查询待上报的人员信息
     * @param pendMode 状态
     * @return
     */
    List<ProjectWorkerDTO> selectProjectWorkerInfo(String pendMode);
}