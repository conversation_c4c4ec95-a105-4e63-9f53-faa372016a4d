package io.renren.api.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 项目人员工资
 *
 * <AUTHOR>
 * @since 1.0.0 2021-07-13
 */
@Data
@ApiModel(value = "项目人员工资")
public class ProjectPayrollDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @JSONField(serialize = false)
    private Long id;

	@ApiModelProperty(value = "项目code(住建部)")
	private String projectCode;

    @ApiModelProperty(value = "所属企业统一社会信用代码")
    private String corpCode;

    @ApiModelProperty(value = "工人所属企业名称")
    private String corpName;

    @ApiModelProperty(value = "班组编号")
    private String teamSysNo;

    @ApiModelProperty(value = "发放工资的月份(yyyy-MM)")
    private String payMonth;

    @ApiModelProperty(value = "工资单详情")
    private List<ProjectPayrollWorkerDTO> detailList;

    @ApiModelProperty(value = "响应接口异步查询使用")
    @JSONField(serialize = false)
    private String requestSerialCode;

}