server:
  undertow:
    # 指定工作者线程的 I/0 线程数，默认为 2 或者 CPU 的个数
    io-threads: 2
    # 指定工作者线程个数,默认为 I/O线程个数的8倍
    worker-threads: 16
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分，不要设置太大，以免影响其他应用，合适即可
    buffer-size: 1024
    # 是否分配的直接内存(NIO直接分配的堆外内存)
    direct-buffers: true
  servlet:
    context-path: /
    session:
      cookie:
        http-only: true
  port: 8083
# mysql
spring:
  # 环境 dev|test|prod
  profiles:
    active: dev
  messages:
    encoding: UTF-8
    basename: i18n/messages
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  redis:
    database: 0
    host: **********
    port: 6379
    password: jzgxygk@123 # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
renren:
  redis:
    open: false  # 是否开启redis缓存  true开启   false关闭

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: ID_WORKER
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      #驼峰下划线转换
      column-underline: true
      #db-type: mysql
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
#住建部接口上传相关配置
#企业秘钥
mohurd:
  url: http://118.113.15.111:8090/open.api
  appSecret: 55aa004dd80778642f66855381b46334
  appId: 5101042020112012001
  key: 3e43e64832ea4d298c277e52a96e407e
