<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.dao.WorkerAttDao">
    <resultMap type="io.renren.api.dto.WorkerAttInfoDTO" id="workerAttMap">
        <result property="projectCode" column="project_code"/>
        <result property="teamSysNo" column="team_sys_no"/>
        <collection property="dataList" ofType="io.renren.api.dto.WorkerAttDTO">
            <result property="id" column="ID"/>
            <result property="ps0201" column="PS0201"/>
            <result property="pj0101" column="PJ0101"/>
            <result property="tm0101" column="TM0101"/>
            <result property="idCardType" column="ID_CARD_TYPE"/>
            <result property="idCardNumber" column="ID_CARD_NUMBER"/>
            <result property="date" column="ATT_DATE"/>
            <result property="direction" column="DIRECTION"/>
            <result property="image" column="IMAGE"/>
            <result property="channel" column="CHANNEL"/>
            <result property="attendType" column="ATTEND_TYPE"/>
            <result property="lng" column="LNG"/>
            <result property="lat" column="LAT"/>
            <result property="temperature" column="TEMPERATURE"/>
            <result property="requestSerialCode" column="REQUEST_SERIAL_CODE"/>
        </collection>
    </resultMap>
    <select id="selectProjectWorkerAttInfo" resultMap="workerAttMap">
        select t.*, b.team_sys_no, a.project_code
        from mhd_worker_att t,
             mhd_project_info a,
             mhd_project_team_info b
        where t.pj0101 = a.pj0101
          and t.tm0101 = b.tm0101
          and b.pj0101 = a.pj0101
          and b.status = '3'
          and t.STATUS = #{pendMode}
    </select>
</mapper>