<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.dao.ProjectSucontractorDao">
    <resultMap id="sucInfo" type="io.renren.api.dto.ProjectSucontractorDTO">
        <result property="cp0201" column="CP0201"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="projectCode" column="PROJECT_CODE"/>
        <result property="corpCode" column="CORP_CODE"/>
        <result property="corpName" column="CORP_NAME"/>
        <result property="corpType" column="CORP_TYPE"/>
        <result property="entryTime" column="ENTRY_TIME"/>
        <result property="exitTime" column="EXIT_TIME"/>
        <result property="pmName" column="PM_NAME"/>
        <result property="pmIDCardType" column="PM_I_D_CARD_TYPE"/>
        <result property="pmIDCardNumber" column="PM_I_D_CARD_NUMBER"/>
        <result property="pmPhone" column="PM_PHONE"/>
        <result property="pmCode" column="PM_CODE"/>
        <result property="requestSerialCode" column="REQUEST_SERIAL_CODE"/>
        <collection property="bankInfoDTOList" ofType="io.renren.api.dto.SucontractorBankInfoDTO">
            <result column="BANK_CODE" property="bankCode"/>
            <result column="BANK_LINK_NUMBER" property="bankLinkNumber"/>
            <result column="BANK_NAME" property="bankName"/>
            <result column="BANK_NUMBER" property="bankNumber"/>
        </collection>
    </resultMap>
    <select id="selectSucInfo" resultMap="sucInfo">
        select t.*,
               a.PROJECT_CODE,
               b.CP0201,
               b.BANK_NAME,
               b.BANK_CODE,
               b.BANK_LINK_NUMBER,
               b.BANK_NUMBER
        FROM mhd_project_sub_contractor t
                     left join mhd_sub_contractor_bank_info b on t.cp0201 = t.cp0201,
             mhd_project_info a
        WHERE t.pj0101 = a.pj0101
          and a.status = '3'
          and t.STATUS = #{pendMode}
    </select>
</mapper>