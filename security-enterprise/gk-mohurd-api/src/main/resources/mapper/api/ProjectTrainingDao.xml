<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.dao.ProjectTrainingDao">
    <resultMap type="io.renren.api.dto.ProjectTrainingDTO" id="projectTrainingMap">
        <result property="id" column="ID"/>
        <result property="trainingDate" column="TRAINING_DATE"/>
        <result property="trainingDuration" column="TRAINING_DURATION"/>
        <result property="trainingName" column="TRAINING_NAME"/>
        <result property="trainingTypeCode" column="TRAINING_TYPE_CODE"/>
        <result property="trainer" column="TRAINER"/>
        <result property="trainingOrg" column="TRAINING_ORG"/>
        <result property="trainingAddress" column="TRAINING_ADDRESS"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="status" column="STATUS"/>
        <result property="message" column="MESSAGE"/>
        <result property="requestSerialCode" column="REQUEST_SERIAL_CODE"/>
        <result property="projectCode" column="project_code"/>
    </resultMap>
    <select id="selectProjectTrainingInfo" resultMap="projectTrainingMap">
        select t.*, a.project_code
        from MHD_PROJECT_TRAINING t,
             mhd_project_info a
        where t.pj0101 = a.pj0101
          and a.status = '3'
          and t.status = #{pendMode}
    </select>
</mapper>