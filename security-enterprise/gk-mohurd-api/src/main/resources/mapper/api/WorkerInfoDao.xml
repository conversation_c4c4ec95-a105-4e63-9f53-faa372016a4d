<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.dao.WorkerInfoDao">
    <resultMap type="io.renren.api.dto.ProjectWorkerDTO" id="workerInfoMap">
        <result property="projectCode" column="project_code"/>
        <result property="corpCode" column="CORP_CODE"/>
        <result property="corpName" column="CORP_NAME"/>
        <result property="teamSysNo" column="team_sys_no"/>
        <result property="teamName" column="TEAM_NAME"/>
        <collection property="workerList" ofType="io.renren.api.dto.WorkerInfoDTO">
            <result property="ps0201" column="PS0201"/>
            <result property="pj0101" column="PJ0101"/>
            <result property="tm0101" column="TM0101"/>
            <result property="workerName" column="WORKER_NAME"/>
            <result property="isTeamLeader" column="IS_TEAM_LEADER"/>
            <result property="idCardType" column="ID_CARD_TYPE"/>
            <result property="idCardNumber" column="ID_CARD_NUMBER"/>
            <result property="workType" column="WORK_TYPE"/>
            <result property="workRole" column="WORK_ROLE"/>
            <result property="manageType" column="MANAGE_TYPE"/>
            <result property="issueCardDate" column="ISSUE_CARD_DATE"/>
            <result property="issueCardPic" column="ISSUE_CARD_PIC"/>
            <result property="cardNumber" column="CARD_NUMBER"/>
            <result property="payRollBankCardNumber" column="PAY_ROLL_BANK_CARD_NUMBER"/>
            <result property="payRollBankName" column="PAY_ROLL_BANK_NAME"/>
            <result property="bankLinkNumber" column="BANK_LINK_NUMBER"/>
            <result property="payRollTopBankCode" column="PAY_ROLL_TOP_BANK_CODE"/>
            <result property="hasBuyInsurance" column="HAS_BUY_INSURANCE"/>
            <result property="nation" column="NATION"/>
            <result property="address" column="ADDRESS"/>
            <result property="headImage" column="HEAD_IMAGE"/>
            <result property="politicsType" column="POLITICS_TYPE"/>
            <result property="joinedTime" column="JOINED_TIME"/>
            <result property="cellPhone" column="CELL_PHONE"/>
            <result property="cultureLevelType" column="CULTURE_LEVEL_TYPE"/>
            <result property="specialty" column="SPECIALTY"/>
            <result property="hasBadMedicalHistory" column="HAS_BAD_MEDICAL_HISTORY"/>
            <result property="urgentLinkMan" column="URGENT_LINK_MAN"/>
            <result property="urgentLinkManPhone" column="URGENT_LINK_MAN_PHONE"/>
            <result property="workDate" column="WORK_DATE"/>
            <result property="maritalStatus" column="MARITAL_STATUS"/>
            <result property="grantOrg" column="GRANT_ORG"/>
            <result property="positiveIDCardImage" column="POSITIVE_I_D_CARD_IMAGE"/>
            <result property="negativeIDCardImage" column="NEGATIVE_I_D_CARD_IMAGE"/>
            <result property="startDate" column="START_DATE"/>
            <result property="expiryDate" column="EXPIRY_DATE"/>
            <result property="requestSerialCode" column="REQUEST_SERIAL_CODE"/>
        </collection>
    </resultMap>
    <select id="selectProjectWorkerInfo" resultMap="workerInfoMap">
        select t.*, b.team_sys_no, a.project_code, b.CORP_CODE, b.CORP_NAME, b.TEAM_NAME
        from mhd_worker_info t,
             mhd_project_info a,
             mhd_project_team_info b
        where t.pj0101 = a.pj0101
          and t.tm0101 = b.tm0101
          and b.pj0101 = a.pj0101
          and b.status = '3'
          and t.STATUS = #{pendMode}
          and rownum = 1
    </select>
</mapper>