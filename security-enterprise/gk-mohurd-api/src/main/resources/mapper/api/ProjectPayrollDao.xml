<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.dao.ProjectPayrollDao">
    <resultMap type="io.renren.api.dto.ProjectPayrollDTO" id="projectPayrollMap">
        <result property="id" column="ID"/>
        <result property="corpCode" column="CORP_CODE"/>
        <result property="corpName" column="CORP_NAME"/>
        <result property="payMonth" column="PAY_MONTH"/>
        <result property="requestSerialCode" column="REQUEST_SERIAL_CODE"/>
        <result property="projectCode" column="project_code"/>
        <result property="teamSysNo" column="teamSysNo"/>
        <result property="teamSysNo" column="team_sys_no"/>
        <collection property="detailList" ofType="io.renren.api.dto.ProjectPayrollWorkerDTO">
            <result property="payrollWorkerId" column="PAYROLL_WORKER_ID"/>
            <result property="idCardType" column="ID_CARD_TYPE"/>
            <result property="idCardNumber" column="ID_CARD_NUMBER"/>
            <result property="days" column="DAYS"/>
            <result property="workHours" column="WORK_HOURS"/>
            <result property="payRollBankCardNumber" column="PAY_ROLL_BANK_CARD_NUMBER"/>
            <result property="payRollBankCode" column="PAY_ROLL_BANK_CODE"/>
            <result property="payRollBankName" column="PAY_ROLL_BANK_NAME"/>
            <result property="payBankCardNumber" column="PAY_BANK_CARD_NUMBER"/>
            <result property="payBankCode" column="PAY_BANK_CODE"/>
            <result property="payBankName" column="PAY_BANK_NAME"/>
            <result property="totalPayAmount" column="TOTAL_PAY_AMOUNT"/>
            <result property="actualAmount" column="ACTUAL_AMOUNT"/>
            <result property="isBackPay" column="IS_BACK_PAY"/>
            <result property="balanceDate" column="BALANCE_DATE"/>
            <result property="backPayMonth" column="BACK_PAY_MONTH"/>
            <result property="thirdPayRollCode" column="THIRD_PAY_ROLL_CODE"/>
            <result property="payrollWorkerId" column="PAYROLL_WORKER_ID"/>
        </collection>
    </resultMap>
    <select id="selectProjectPayrollInfo" resultMap="projectPayrollMap">
        select t.*, c.*, b.team_sys_no, a.project_code
        from MHD_PROJECT_PAYROLL t,
             mhd_project_info a,
             mhd_project_team_info b,
             mhd_project_payroll_worker c
        where t.pj0101 = a.pj0101
          and t.tm0101 = b.tm0101
          and b.pj0101 = a.pj0101
          and t.id = c.id
          and b.status = '3'
          and t.STATUS = #{pendMode}
    </select>
</mapper>