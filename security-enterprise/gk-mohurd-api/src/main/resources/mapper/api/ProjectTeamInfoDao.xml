<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.dao.ProjectTeamInfoDao">
    <resultMap type="io.renren.api.dto.ProjectTeamInfoDTO" id="projectTeamInfoMap">
        <result property="tm0101" column="TM0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="cp0201" column="CP0201"/>
        <result property="corpCode" column="CORP_CODE"/>
        <result property="corpName" column="CORP_NAME"/>
        <result property="teamName" column="TEAM_NAME"/>
        <result property="projectCode" column="project_code"/>
        <result property="responsiblePersonName" column="RESPONSIBLE_PERSON_NAME"/>
        <result property="responsiblePersonPhone" column="RESPONSIBLE_PERSON_PHONE"/>
        <result property="responsiblePersonIDCardType" column="RESP_PERSON_I_D_CARD_TYPE"/>
        <result property="responsiblePersonIDNumber" column="RESPONSIBLE_PERSON_I_D_NUMBER"/>
        <result property="remark" column="REMARK"/>
        <result property="entryTime" column="ENTRY_TIME"/>
        <result property="exitTime" column="EXIT_TIME"/>
    </resultMap>
    <select id="selectProjectTeamInfo" resultMap="projectTeamInfoMap">
        select t.*, b.project_code
        from MHD_PROJECT_TEAM_INFO t,
             MHD_PROJECT_SUB_CONTRACTOR a,
             mhd_project_info b
        where t.STATUS = #{pendMode}
          and t.CP0201 = a.CP0201
          and t.pj0101 = b.pj0101
          and a.STATUS = '3'
    </select>
</mapper>