<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.dao.ProjectPayrollWorkerDao">

    <resultMap type="io.renren.api.entity.ProjectPayrollWorkerEntity" id="projectPayrollWorkerMap">
        <result property="payrollWorkerId" column="PAYROLL_WORKER_ID"/>
        <result property="idCardType" column="ID_CARD_TYPE"/>
        <result property="idCardNumber" column="ID_CARD_NUMBER"/>
        <result property="days" column="DAYS"/>
        <result property="workHours" column="WORK_HOURS"/>
        <result property="payRollBankCardNumber" column="PAY_ROLL_BANK_CARD_NUMBER"/>
        <result property="payRollBankCode" column="PAY_ROLL_BANK_CODE"/>
        <result property="payRollBankName" column="PAY_ROLL_BANK_NAME"/>
        <result property="payBankCardNumber" column="PAY_BANK_CARD_NUMBER"/>
        <result property="payBankCode" column="PAY_BANK_CODE"/>
        <result property="payBankName" column="PAY_BANK_NAME"/>
        <result property="totalPayAmount" column="TOTAL_PAY_AMOUNT"/>
        <result property="actualAmount" column="ACTUAL_AMOUNT"/>
        <result property="isBackPay" column="IS_BACK_PAY"/>
        <result property="balanceDate" column="BALANCE_DATE"/>
        <result property="backPayMonth" column="BACK_PAY_MONTH"/>
        <result property="thirdPayRollCode" column="THIRD_PAY_ROLL_CODE"/>
        <result property="payrollWorkerId" column="PAYROLL_WORKER_ID"/>
    </resultMap>


</mapper>