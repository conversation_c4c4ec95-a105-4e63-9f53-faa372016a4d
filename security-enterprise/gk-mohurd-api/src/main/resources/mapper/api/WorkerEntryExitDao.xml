<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.dao.WorkerEntryExitDao">
    <resultMap type="io.renren.api.dto.WorkerEntryExitReportDTO" id="workerEntryExitMap">
        <result property="projectCode" column="project_code"/>
        <result property="corpCode" column="CORP_CODE"/>
        <result property="corpName" column="CORP_NAME"/>
        <result property="teamSysNo" column="team_sys_no"/>
        <result property="teamName" column="TEAM_NAME"/>
        <collection property="workerList" ofType="io.renren.api.dto.WorkerEntryExitDTO">
            <result property="id" column="ID"/>
            <result property="ps0201" column="PS0201"/>
            <result property="pj0101" column="PJ0101"/>
            <result property="tm0101" column="TM0101"/>
            <result property="idCardType" column="ID_CARD_TYPE"/>
            <result property="idCardNumber" column="ID_CARD_NUMBER"/>
            <result property="type" column="ENTRY_EXIT_TYPE"/>
            <result property="voucher" column="VOUCHER"/>
            <result property="date" column="ENTRY_EXIT_DATE"/>
            <result property="requestSerialCode" column="REQUEST_SERIAL_CODE"/>
        </collection>
    </resultMap>
    <select id="selectWorkerEntryExit" resultMap="workerEntryExitMap">
    select t.*, b.team_sys_no, a.project_code, b.CORP_CODE, b.CORP_NAME, b.TEAM_NAME
        from MHD_WORKER_ENTRY_EXIT t,
             mhd_project_info a,
             mhd_project_team_info b,
             MHD_WORKER_INFO c
        where t.pj0101 = a.pj0101
          and t.tm0101 = b.tm0101
          and b.pj0101 = a.pj0101
          and t.PS0201=c.PS0201
          and c.status = '3'
          and t.STATUS = #{pendMode}
          and rownum = 1
</select>
</mapper>