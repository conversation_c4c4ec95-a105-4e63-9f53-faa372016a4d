<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.api.dao.WorkerContractDao">
    <resultMap type="io.renren.api.dto.WorkerContractInfoDTO" id="workerContractMap">
        <result property="projectCode" column="PROJECT_CODE"/>
        <collection property="contractList" ofType="io.renren.api.dto.WorkerContractDTO">
            <result property="id" column="ID"/>
            <result property="ps0201" column="PS0201"/>
            <result property="pj0101" column="PJ0101"/>
            <result property="corpCode" column="CORP_CODE"/>
            <result property="corpName" column="CORP_NAME"/>
            <result property="idCardType" column="ID_CARD_TYPE"/>
            <result property="idCardNumber" column="ID_CARD_NUMBER"/>
            <result property="contractPeriodType" column="CONTRACT_PERIOD_TYPE"/>
            <result property="startDate" column="START_DATE"/>
            <result property="endDate" column="END_DATE"/>
            <result property="unit" column="UNIT"/>
            <result property="unitPrice" column="UNIT_PRICE"/>
            <result property="requestSerialCode" column="REQUEST_SERIAL_CODE"/>
            <collection property="attachments" ofType="io.renren.api.dto.WorkerContractFileDTO">
                <result property="ps0201" column="PS0201"/>
                <result property="name" column="NAME"/>
                <result property="data" column="DATA"/>
            </collection>
        </collection>
    </resultMap>
    <select id="selectProjectWorkerConInfo" resultMap="workerContractMap">
        select a.PROJECT_CODE,
               t.ID,
               t.PS0201,
               t.PJ0101,
               t.CORP_CODE,
               t.CORP_NAME,
               t.ID_CARD_TYPE,
               t.ID_CARD_NUMBER,
               t.CONTRACT_PERIOD_TYPE,
               t.START_DATE,
               t.END_DATE,
               t.UNIT,
               t.UNIT_PRICE,
               t.STATUS,
               t.MESSAGE,
               t.REQUEST_SERIAL_CODE,
               t.CREATE_DATE,
               t.UPDATE_DATE,
               t.ARYN_MESSAGE,
               t.ARYN_CHECK_DATE,
               c.NAME,
               c.DATA
        from (select *
              from mhd_worker_contract e
              where e.status = #{pendMode}
                and rownum = 1
              order by e.id) t,
             MHD_PROJECT_INFO a,
             MHD_WORKER_ENTRY_EXIT b,
             MHD_WORKER_CONTRACT_FILE c
        where t.PJ0101 = a.PJ0101
          and t.PS0201 = b.PS0201
          and t.PS0201 = c.PS0201
          and b.STATUS = '3'
    </select>
</mapper>