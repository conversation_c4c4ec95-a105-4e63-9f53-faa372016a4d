package io.renren.realname.service;

import io.renren.realname.dto.ManagerInfo;
import io.renren.common.page.PageData;
import io.renren.common.service.BaseService;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/15 13:28
 */
public interface ManagerInfoService extends BaseService<ManagerInfo> {
    /**
     * 分页查询
     * @param params 查询参数
     * @return PageData<TeamInfo>
     */
    PageData<ManagerInfo> page(Map<String, Object> params);
}
