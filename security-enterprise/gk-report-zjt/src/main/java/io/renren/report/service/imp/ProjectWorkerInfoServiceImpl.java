package io.renren.report.service.imp;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.annotation.MordFilter;
import io.renren.common.constant.TableName;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.report.baseobject.*;
import io.renren.report.dao.*;
import io.renren.report.dto.ProjectWorkerInfoDTO;
import io.renren.report.entity.*;
import io.renren.report.service.*;
import io.renren.util.ValidatorUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-06-02
 */
@Service
public class ProjectWorkerInfoServiceImpl extends CrudServiceImpl<ProjectWorkerInfoDao, ProjectWorkerInfoEntity, ProjectWorkerInfoDTO> implements ProjectWorkerInfoService {

    @Value("${dataBase.name}")
    private String dataBaseName;

    @Autowired
    private UtilDao utilDao;

    @Autowired
    private TeamMasterInfoDao teamMasterInfoDao;

    @Autowired
    private ProjectPmInfoDao projectPmInfoDao;

    @Autowired
    private ProjectWorkerInfoDao projectWorkerInfoDao;

    @Autowired
    private WorkerContractInfoDao workerContractInfoDao;

    @Autowired
    private ProjectWorkerInoutInfoDao projectWorkerInoutInfoDao;

    @Autowired
    private AttInfoService attInfoService;

    @Autowired
    private LogService logService;

    @Autowired
    private SourceDataDetailService sourceDataDetailService;

    @Autowired
    private SourceDataService sourceDataService;


    @Override
    public QueryWrapper<ProjectWorkerInfoEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<ProjectWorkerInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public void dealMsg(JSONObject data,Long sourceDataId) {
        sourceDataId = sourceDataService.insertOrNotBySourceDataId(data,sourceDataId);
        Long pj0101 = data.getLong("pj0101");
        Ps01DTO ps01DTO = data.getJSONObject("ps01").toJavaObject(Ps01DTO.class);
        Ps02DTO ps02DTO = data.getJSONObject("ps02").toJavaObject(Ps02DTO.class);
        Cp01DTO cp01DTO = data.getJSONObject("cp01").toJavaObject(Cp01DTO.class);
        cp01DTO = JSONObject.parseObject(JSON.toJSONString(cp01DTO, new MordFilter())).toJavaObject(Cp01DTO.class);
        ps02DTO = JSONObject.parseObject(JSON.toJSONString(ps02DTO, new MordFilter())).toJavaObject(Ps02DTO.class);
        ps01DTO = JSONObject.parseObject(JSON.toJSONString(ps01DTO, new MordFilter())).toJavaObject(Ps01DTO.class);
        //判断班组信息是都存在
        Integer teamMasterInfoCount = teamMasterInfoDao.selectCount(
                new QueryWrapper<TeamMasterInfoEntity>()
                        .eq("PJ0101", pj0101)
                        .eq("TM0101", ps02DTO.getTm0101())
        );
        if(teamMasterInfoCount == 0){
            //记失败的处理明细
            sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.PROJECT_WORKER_INFO,TableName.ERROR,TableName.WORKER);

            //班组信息未添加
            System.out.println("此工人的班组未添加，项目PJ0101：" + pj0101 + "班组TM0101:" + ps02DTO.getTm0101());
            return;
        }

        String teamName = utilDao.getTeamNameByTm0101(dataBaseName, ps02DTO.getTm0101());

        //处理头像
        String photo = ps02DTO.getIssuecardpicurl();
        //查询项目的areaCode
        String areacode = utilDao.selectAreacodeByPj0101(dataBaseName, pj0101);
        //得到附件id
        String attInfoId = attInfoService.dealFile(photo, areacode);
        //添加工人信息
        Long teamSysNo = utilDao.getTeamSysNoByTm0101(ps02DTO.getTm0101());
        //查询工人是否有合同信息
        String hasContract = "是";

        int ps03 = utilDao.getPs03CountByPs0201(dataBaseName, ps02DTO.getPs0201());
        if (0 == ps03) {
            hasContract = "否";
        }
        ProjectWorkerInfoEntity projectWorkerInfoEntity = new ProjectWorkerInfoEntity()
                .setPj0101(pj0101)
                .setCorpCode(null == cp01DTO.getCorpcode()? "":cp01DTO.getCorpcode())
                .setCorpName(null == cp01DTO.getCorpname()? "":cp01DTO.getCorpname())
                .setTeamSysNo(teamSysNo)
                .setTeamName(teamName)
                .setWorkerName(ps01DTO.getName())
                .setIsTeamLeader(ps02DTO.getIsteamleader())
                .setIdCardType(1L)
                .setIdCardNumber(ps01DTO.getIdcardnumber())
                .setAge(getAge(null == ps01DTO.getBirthday() ? getBirthday(ps01DTO.getIdcardnumber()):ps01DTO.getBirthday()))
                .setGender(ps01DTO.getGender())
                .setNation(ps01DTO.getNation().length() == 1 ? ("99".equals(ps01DTO.getNation())? "01":"0" + ps01DTO.getNation()) : ps01DTO.getNation())
                .setAddress(null == ps01DTO.getAddress()? "":ps01DTO.getAddress())
                .setHeadImage(attInfoId)
                .setPoliticsType(Long.valueOf("0".equals(ps01DTO.getPoliticstype())?"13":ps01DTO.getPoliticstype()))
                .setCultureLevelType(Long.valueOf(ps01DTO.getCultureleveltype()))
                .setGrantOrg(null == ps01DTO.getGrantorg()? "":ps01DTO.getGrantorg())
                .setWorkType("990".equals(ps02DTO.getWorktypecode()) ? "100" : (ps02DTO.getWorktypecode().length()==2 ? "0"+ ps02DTO.getWorktypecode() : ps02DTO.getWorktypecode()))
                .setNativePlace("".equals(ps01DTO.getAreacode()) ? ps01DTO.getIdcardnumber().substring(0, 6) : ps01DTO.getAreacode())
                .setMobile(null == ps01DTO.getCellphone()|| ps01DTO.getCellphone().length() == 11 ? "13888888888": ps01DTO.getCellphone())
                .setIssueCardPicUrl("")
                .setHasContract(hasContract)
                .setHasBuyInsurance("")
                .setReportStatus("0");
        boolean b = ValidatorUtil.validateEntity(projectWorkerInfoEntity);
        if (b) {
            //判断是否存在此工人得信息
            int countEntity = projectWorkerInfoDao.selectCount(
                    new QueryWrapper<ProjectWorkerInfoEntity>()
                            .eq("PJ0101", pj0101)
                            .eq("ID_CARD_NUMBER", ps01DTO.getIdcardnumber())
            );
            if (countEntity == 0) {
                //不存在则添加
                int insert = projectWorkerInfoDao.insert(projectWorkerInfoEntity);
                if (insert>0){
                    //记成功的处理明细
                    sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.PROJECT_WORKER_INFO,TableName.OK,TableName.WORKER);
                    System.out.println("成功添加工人信息PJ0101："+pj0101 + ps01DTO.getName());
                }
            } else {
                //记成功的处理明细
                sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.PROJECT_WORKER_INFO,TableName.OK,TableName.WORKER);
                projectWorkerInfoDao.update(projectWorkerInfoEntity, new QueryWrapper<ProjectWorkerInfoEntity>()
                        .eq("PJ0101", pj0101)
                        .eq("ID_CARD_NUMBER", ps01DTO.getIdcardnumber())
                );
            }
        }else {
            //记失败的处理明细
            sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.PROJECT_WORKER_INFO,TableName.ERROR,TableName.WORKER);

            System.out.println("工人数据校验未通过项目PJ0101:" + pj0101 + "工人ID："+ ps02DTO.getPs0201());
        }

        //处理进退场信息
        ProjectWorkerInoutInfoEntity projectWorkerInoutInfoEntity = new ProjectWorkerInoutInfoEntity()
                .setPj0101(ps02DTO.getPj0101())
                .setIdCardNumber(ps01DTO.getIdcardnumber())
                .setInOut(ps02DTO.getInOrOut())
                .setOccurTime("2".equals(ps02DTO.getInOrOut()) ? ps02DTO.getExittime() : ps02DTO.getEntrytime())
                .setReportStatus("0");


        dealWprkerInOutInfo(sourceDataId,projectWorkerInoutInfoEntity,TableName.WORKER);
        //处理合同信息
        ps02DTO.setPs01DTO(ps01DTO);
        dealWorkerContratInfo(sourceDataId,cp01DTO, ps02DTO);

    }

    /**
    *处理退场信息
     */
    @Override
    public void exitPerson(JSONObject data,Long sourceDataId) {
        sourceDataId = sourceDataService.insertOrNotBySourceDataId(data,sourceDataId);
        Long pj0101 = data.getLong("pj0101");
        Long userId = data.getLong("id");

        ProjectWorkerInoutInfoEntity projectWorkerInoutInfoEntity = new ProjectWorkerInoutInfoEntity();
        //判断是工人还是管理人员[查询ps02中是否存在]
        InOrOutDTO inOrOutDTO = utilDao.selectWorker(dataBaseName,pj0101, userId);
        if (ObjectUtil.isNull(inOrOutDTO)){
            //查询此管理人员信息
            inOrOutDTO = utilDao.selectManager(dataBaseName,pj0101, userId);
            //查询此管理人员是否添加
            Integer projectPmInfoCount = projectPmInfoDao.selectCount(
                    new QueryWrapper<ProjectPmInfoEntity>()
                            .eq("PJ0101", pj0101)
                            .eq("PM_ID_CARD_NUMBER", inOrOutDTO.getIdcardnumber())
            );
            if (projectPmInfoCount == 0){
                //记失败的处理明细
                sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.PROJECT_WORKER_INOUT_INFO,TableName.ERROR,TableName.EXIT);
                //考勤人员未添加
                System.out.println("退场管理人员未添加--项目PJ0101：" + pj0101 +"管理人员ID：" + userId);
                return;
            }
            //管理人员退场处理
            inOrOutDTO = utilDao.selectManager(dataBaseName,pj0101, userId);
            projectWorkerInoutInfoEntity
                    .setPj0101(inOrOutDTO.getPj0101())
                    .setIdCardNumber(inOrOutDTO.getIdcardnumber())
                    .setInOut(inOrOutDTO.getInOrOut())
                    .setOccurTime("2".equals(inOrOutDTO.getInOrOut()) ? inOrOutDTO.getExittime() : inOrOutDTO.getEntrytime())
                    .setReportStatus("0");
        }else {
            //查询此工人是否添加
            //工人
            Integer projectWorkerInfoCount = projectWorkerInfoDao.selectCount(
                    new QueryWrapper<ProjectWorkerInfoEntity>()
                            .eq("PJ0101", pj0101)
                            .eq("ID_CARD_NUMBER", inOrOutDTO.getIdcardnumber())
            );
            if (projectWorkerInfoCount == 0){
                //记失败的处理明细
                sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.PROJECT_WORKER_INOUT_INFO,TableName.ERROR,TableName.EXIT);
                //考勤人员未添加
                System.out.println("退场工人未添加--项目PJ0101：" + pj0101 +"工人ID：" + userId);
                return;
            }
            //工人退场处理
            projectWorkerInoutInfoEntity
                    .setPj0101(inOrOutDTO.getPj0101())
                    .setIdCardNumber(inOrOutDTO.getIdcardnumber())
                    .setInOut(inOrOutDTO.getInOrOut())
                    .setOccurTime("2".equals(inOrOutDTO.getInOrOut()) ? inOrOutDTO.getExittime() : inOrOutDTO.getEntrytime())
                    .setReportStatus("0");
        }
        dealWprkerInOutInfo(sourceDataId,projectWorkerInoutInfoEntity,TableName.EXIT);
    }


    /**
     * 处理合同信息
     */
    private void dealWorkerContratInfo(Long sourceDataId,Cp01DTO cp01DTO, Ps02DTO ps02DTO) {
        //判断合同信息是否存在
        int count = workerContractInfoDao.selectCount(
                new QueryWrapper<WorkerContractInfoEntity>()
                        .eq("PJ0101", ps02DTO.getPj0101())
                        .eq("ID_CARD_NUMBER", ps02DTO.getPs01DTO().getIdcardnumber())
        );
        //如果合同信息已经存在则不处理
        if (count > 0 ){
            //记成功的处理明细
            sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.WORKER_CONTRACT_INFO,TableName.OK,TableName.WORKER);
            return;
        }
        //获取项目编号序列
        String contractCode = utilDao.getcontractCode();
        //根据ps0201 查询合同信息 以及附件路径
        Ps03DTO ps03ByOt01Max = utilDao.getPs03DtoByPs0201(dataBaseName, ps02DTO.getPs0201());
        WorkerContractInfoEntity workerContractInfoEntity = new WorkerContractInfoEntity();
        //合同可能为多个附件 但是只传最小那个ot0101 的附件
        //查询项目的areaCode
        String areacode = utilDao.selectAreacodeByPj0101(dataBaseName, ps02DTO.getPj0101());
        //如果合同不存在 则不处理
        if (ObjectUtil.isNull(ps03ByOt01Max)){
            //记成功的处理明细
            sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.WORKER_CONTRACT_INFO,TableName.OK,TableName.WORKER);
            return;
        }
        //处理合同附件 得到附件id 放到合同表
        String fileId = attInfoService.dealFile(ps03ByOt01Max.getFileUrl(), areacode);
        workerContractInfoEntity
                .setPj0101(ps02DTO.getPj0101())
                .setCorpCode(cp01DTO.getCorpcode())
                .setCorpName(cp01DTO.getCorpname())
                .setWorkerName(ps02DTO.getPs01DTO().getName())
                .setIdCardType(ps02DTO.getPs01DTO().getIdcardtype())
                .setIdCardNumber(ps02DTO.getPs01DTO().getIdcardnumber())
                .setContractCode(contractCode)
                //ps03DTO.getContractperiodtype()
                .setContractPeriodType("1")
                .setSignDate(null == ps03ByOt01Max.getSigndate()? new Date():ps03ByOt01Max.getSigndate())
                .setStartDate(null == ps03ByOt01Max.getStartdate()? new Date():ps03ByOt01Max.getStartdate())
                .setEndDate( ps03ByOt01Max.getStartdate())
                .setUnit(ps03ByOt01Max.getUnit())
                .setUnitPrice(ps03ByOt01Max.getUnitprice())
                .setContractAtt(fileId)
                .setReportStatus("0");
        //校验数据
        boolean b = ValidatorUtil.validateEntity(workerContractInfoEntity);
        if (b) {
            //已经存在则修改
//            if (count > 0) {
                //使用之前的合同编号 保证合同编号一致
//                workerContractInfoEntity.setContractCode(count.getContractCode());
//                workerContractInfoDao.update(workerContractInfoEntity,  new QueryWrapper<WorkerContractInfoEntity>()
//                        .eq("PJ0101", ps02DTO.getPj0101())
//                        .eq("ID_CARD_NUMBER", ps02DTO.getPs01DTO().getIdcardnumber())
//                );
//            }else {
                //不存在则添加
                int insert = workerContractInfoDao.insert(workerContractInfoEntity);
                if (insert>0){
                    //记成功的处理明细
                    sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.WORKER_CONTRACT_INFO,TableName.OK,TableName.WORKER);

                    System.out.println("成功添加工人合同信息项目：" + ps02DTO.getPj0101() + "工人" + ps02DTO.getPs01DTO().getName());
                }
//            }
        }else {
            //记失败的处理明细
            sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.WORKER_CONTRACT_INFO,TableName.ERROR,TableName.WORKER);
            System.out.println("工人合同信息校验失败项目：" + ps02DTO.getPj0101() + "工人ID" + ps02DTO.getPs0201());
        }
//        else {
            //不存在合同信息时 不处理
//            return;
//            //查询合同信息
//            Ps03DTO ps03DTO = utilDao.getPs03DtoByPs0201(dataBaseName, ps02DTO.getPs0201());
//            workerContractInfoEntity
//                    .setPj0101(ps02DTO.getPj0101())
//                    .setCorpCode(cp01DTO.getCorpcode())
//                    .setCorpName(cp01DTO.getCorpname())
//                    .setWorkerName(ps02DTO.getPs01DTO().getName())
//                    .setIdCardType(ps02DTO.getPs01DTO().getIdcardtype())
//                    .setIdCardNumber(ps02DTO.getPs01DTO().getIdcardnumber())
//                    .setContractCode(contractCode)
//                    //ps03DTO.getContractperiodtype()
//                    .setContractPeriodType("1")
//                    .setSignDate(ps03DTO.getSigndate())
//                    .setStartDate(ps03DTO.getStartdate())
//                    .setEndDate(ps03DTO.getEnddate())
//                    .setUnit(ps03DTO.getUnit())
//                    .setUnitPrice(ps03DTO.getUnitprice())
//                    .setContractAtt("")
//                    .setReportStatus("0");
//        }

    }


    /**
     * 处理工人进退场信息
     */
    private void dealWprkerInOutInfo(Long sourceDataId,ProjectWorkerInoutInfoEntity projectWorkerInoutInfoEntity,String type) {
        //查询此工人最新的进退场信息
        ProjectWorkerInoutInfoEntity selectLast = projectWorkerInoutInfoDao.selectLast(projectWorkerInoutInfoEntity.getPj0101(), projectWorkerInoutInfoEntity.getIdCardNumber());
        //如果最新的一条数据和本次的进出场状态相同也不处理
        if (ObjectUtil.isNotNull(selectLast) && selectLast.getInOut().equals(projectWorkerInoutInfoEntity.getInOut())) {
            //记成功的处理明细
            sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.PROJECT_WORKER_INOUT_INFO,TableName.OK,type);
            return;
        }
        //验证数据
        boolean b = ValidatorUtil.validateEntity(projectWorkerInoutInfoEntity);
        if (b) {
            int insert = projectWorkerInoutInfoDao.insert(projectWorkerInoutInfoEntity);
            if (insert>0){
                //记成功的处理明细
                sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.PROJECT_WORKER_INOUT_INFO,TableName.OK,type);

                System.out.println("成功添加工人进退场信息PJ0101"+projectWorkerInoutInfoEntity.getPj0101()+"工人身份证："+projectWorkerInoutInfoEntity.getIdCardNumber());
            }
        }else {
            //记失败的处理明细
            sourceDataDetailService.insertOrUpdateBySourceDataIdAndTableName(sourceDataId, TableName.PROJECT_WORKER_INOUT_INFO,TableName.ERROR,type);
            System.out.println("工人进退场信息校验失败--PJ0101"+projectWorkerInoutInfoEntity.getPj0101()+"工人身份证："+projectWorkerInoutInfoEntity.getIdCardNumber());
        }
    }


    /**
     * 计算年龄
     */
    private Long getAge(Date Birthday) {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        Long now = (long) c.get(Calendar.YEAR);
        c.setTime(Birthday);
        Long bir = (long) c.get(Calendar.YEAR);
        return now - bir;
    }

    /**
     * 根据身份证获取生日
     */
    private Date getBirthday(String idCardNumber){
        String year = idCardNumber.substring(6, 10);//调用substring方法返回相关字段，注意索引从0开始
        String month =idCardNumber.substring(10, 12);
        String day = idCardNumber.substring(12, 14);
        String birthday = year + "-" + month + "-" + day;
        DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");//定义一个时间转换格式“年-月-日”
        Date date = null;
        try {     //捕获类型转换（解析）异常
            date = fmt.parse(birthday);
            return date;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }
}