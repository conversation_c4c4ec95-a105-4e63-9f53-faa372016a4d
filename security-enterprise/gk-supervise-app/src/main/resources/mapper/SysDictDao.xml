<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.SysDictDao">
    <resultMap type="io.renren.dto.DictParentDTO" id="getDict">
        <result property="dictType" column="dict_type"/>
        <collection property="dictChildrenDTOS" ofType="io.renren.dto.DictChildrenDTO">
            <result property="dictName" column="DICT_LABEL"/>
            <result property="dictValue" column="DICT_VALUE"/>
        </collection>
    </resultMap>
    <select id="getDictValue" resultType="java.lang.String">
        select a.dict_value from SYS_DICT_TYPE t,sys_dict_data a where t.id=a.dict_type_id and t.dict_type=#{dictType} and a.dict_label=#{dictLabel}
    </select>
    <select id="getFullDict" resultMap="getDict">
         select   b.dict_type, a.dict_label,a.DICT_VALUE
        from sys_dict_data a  ,sys_dict_type b  where  a.dict_type_id = b.id
    </select>
</mapper>