<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.qualityAndSafety.dao.Zj02Dao">
    <select id="pageList" resultType="io.renren.modules.qualityAndSafety.dto.Zj02DTO">
        select t.*, a.name as projectname
          from ZJ_ZJ02 t, b_pj01 a
         where t.pj0101 = a.pj0101
           and t.creator = #{userId}
        <if test="projectname != null and projectname != ''">
            and a.name like '%'||#{projectname}||'%'
        </if>
        <if test="troubletype != null and troubletype != ''">
            and t.troubletype = #{troubletype}
        </if>
        <if test="status != null and status != ''">
            and t.status = #{status}
        </if>
        <if test="areacode != null and areacode != ''">
            and t.areacode = #{areacode}
        </if>
        order by t.CREATE_DATE desc
    </select>
    <select id="selectInfoById" resultType="io.renren.modules.qualityAndSafety.dto.Zj02DTO">
        select t.*, a.name as projectname,b.cp0101
          from ZJ_ZJ02 t, b_pj01 a,b_cp01 b
         where t.pj0101 = a.pj0101
           and t.zj0201 = #{zj0201}
           and t.CORP_CODE = b.CORPCODE
    </select>
</mapper>