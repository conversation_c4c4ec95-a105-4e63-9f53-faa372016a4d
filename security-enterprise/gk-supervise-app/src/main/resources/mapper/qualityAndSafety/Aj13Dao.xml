<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.qualityAndSafety.dao.Aj13Dao">
    <select id="pageList" resultType="io.renren.modules.qualityAndSafety.dto.Aj13DTO">
        select t.*, a.name as projectname
          from AJ_AJ13 t, b_pj01 a
         where t.pj0101 = a.pj0101
           and t.creator = #{userId}
        <if test="projectname != null and projectname != ''">
            and a.name like '%'||#{projectname}||'%'
        </if>
        <if test="troubletype != null and troubletype != ''">
            and t.troubletype = #{troubletype}
        </if>
        <if test="status != null and status != ''">
            and t.status = #{status}
        </if>
        <if test="areacode != null and areacode != ''">
            and t.areacode = #{areacode}
        </if>
        order by t.CREATE_DATE desc
    </select>
    <select id="selectInfoById" resultType="io.renren.modules.qualityAndSafety.dto.Aj13DTO">
        select t.*, a.name as projectname,b.cp0101
          from AJ_AJ13 t, b_pj01 a,b_cp01 b
         where t.pj0101 = a.pj0101
           and t.aj1301 = #{aj1301}
           and t.CORP_CODE = b.CORPCODE
    </select>
</mapper>