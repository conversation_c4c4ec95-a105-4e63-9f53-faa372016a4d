<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.AppSuperviseCommonDao">
    <!--查询实名制系统的项目SQL-->
    <sql id="selectRealNameProject">
        select pj0101
        from b_pj01_own_role
        where SYSTEM_TYPE = 'A'
        group by pj0101
    </sql>
    <!--查询扬尘系统的项目SQL-->
    <sql id="selectDustProject">
        select pj0101
        from b_pj01_own_role
        where SYSTEM_TYPE = 'B'
        group by pj0101
    </sql>
</mapper>