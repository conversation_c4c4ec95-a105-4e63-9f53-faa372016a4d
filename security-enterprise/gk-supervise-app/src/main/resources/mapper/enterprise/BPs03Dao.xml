<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.dao.Ps03Dao">
    <select id="getOt01ByPs0201" resultType="io.renren.modules.enterprise.dto.Ot01DTO">
        SELECT t.OT0101, t.BUSISYSNO, t.NAME, t.url, t.VIEW_TYPE, t.ORIGINAL_NAME
        FROM B_OT01 t
        WHERE t.WHETHER = 1
          AND t.BUSISYSNO = #{ps0301}
    </select>

    <select id="getPs0301ByPs0201" resultType="io.renren.modules.enterprise.dto.Ps03DTO">
        SELECT p.PS0301, p.CONTRACTCODE, p.CONTRACTPERIODTYPE, p.SIGNDATE, p.STARTDATE, p.ENDDATE
        FROM B_PS03 p,
             B_PS02 t
        WHERE p.PS0201 = t.PS0201
          AND t.PS0201 = #{ps0201}
    </select>
</mapper>