<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.dao.Ps02Dao">
    <select id="getPs02ByPj0101" resultType="io.renren.modules.enterprise.dto.ProjectPersonDTO">
        SELECT p.PS0201 userid, s.NAME, p.ISSUECARDPICURL url, t.NAME proname, s.CELLPHONE phone, 1 type
        FROM B_PJ01 t,
             B_PS02 p
                     LEFT JOIN B_PS01 s on p.PS0101 = s.PS0101 WHERE t.PJ0101 = p.PJ0101
                                                                 AND t.PJ0101 = #{pj0101}
        <if test="inorout != null and inorout != ''">
            AND p.IN_OR_OUT = #{inorout}
        </if>
        <if test="name != null and name != ''">
            and s.NAME like '%' || #{name} || '%'
        </if>
    </select>

    <select id="getAllPersonByPj0101" resultType="io.renren.modules.enterprise.dto.ProjectPersonDTO">
        SELECT tmp.*, pj.NAME proname FROM ( SELECT t.PS0201          userid,
                                                    p.NAME,
                                                    t.ISSUECARDPICURL url,
                                                    p.CELLPHONE
                                                                      phone,
                                                    t.PJ0101,
                                                    1                 type
                                             FROM B_PS02 t,
                                                  B_PS01 p WHERE t.PS0101 = p.PS0101
        <if test="inorout != null and inorout != ''">
            AND t.IN_OR_OUT = #{inorout}
        </if>
        UNION ALL
        SELECT t.PS0401 userid, p.NAME, t.PHOTO url, p.CELLPHONE phone, t.PJ0101, 2 type
        FROM B_PS04 t,
             B_PS01 p
                WHERE t.PS0101 = p.PS0101
        <if test="inorout != null and inorout != ''">
            AND t.IN_OR_OUT = #{inorout}
        </if>
        ) tmp,
                B_PJ01 pj WHERE tmp.PJ0101 = pj.PJ0101
                            AND pj.PJ0101 = #{pj0101}
        <if test="name != null and name != ''">
            and tmp.NAME like '%' || #{name} || '%'
        </if>
    </select>

    <select id="getPersonByAreaCode" resultType="io.renren.modules.enterprise.dto.ProjectPersonDTO">
        SELECT tmp.*, t.NAME proname FROM B_PJ01 t,( SELECT p.PJ0101,
                                                            p.ISSUECARDPICURL url,
                                                            s.NAME,
                                                            s.CELLPHONE
                                                                              phone,
                                                            p.PS0201          userid,
                                                            1                 type
                                                     FROM B_PS02 p
                                                                  LEFT JOIN B_PS01 s on p.PS0101 = s.PS0101 WHERE 1 = 1

        <if test="inorout != null and inorout != ''">
            AND p.IN_OR_OUT = #{inorout}
        </if>
        UNION ALL
        SELECT p.PJ0101,
               p.PHOTO     url,
               s.NAME,
               s.CELLPHONE phone,
               p.PS0401    userid,
               2           type
        FROM B_PS04 p
                     LEFT JOIN B_PS01 s
                on p.PS0101 = s.PS0101 WHERE 1 = 1
        <if test="inorout != null and inorout != ''">
            AND p.IN_OR_OUT = #{inorout}
        </if>
        ) tmp WHERE tmp.PJ0101 = t.PJ0101
                AND t.PRJSTATUS = 3
                and t.PJ0101 in (<include
            refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
        <if test="areacode != null and areacode != ''">
            AND t.AREACODE like '%' || #{areacode} || '%'
        </if>
        <if test="name != null and name != ''">
            and tmp.NAME like '%' || #{name} || '%'
        </if>
        <if test="type != null and type != ''">
            and tmp.type = #{type}
        </if>
    </select>

    <select id="getListData" resultType="io.renren.modules.enterprise.dto.ProjectPersonDTO">
        SELECT s1.name, s1.CELLPHONE phone, s2.PS0201 userid, s2.ISSUECARDPICURL url, m.TEAMNAME
        FROM b_ps01 s1,
             b_ps02 s2,
             b_tm01 m
                WHERE s1.ps0101 = s2.ps0101
                  and m.tm0101 = s2.tm0101
                  and s2.in_or_out = '1'
                  and s2.pj0101 = #{pj0101}
        <if test="name != null and name != ''">
            and s1.name like '%' || #{name} || '%'
        </if>
        <if test="cp0201 != null and cp0201 != ''">
            and m.cp0201 = #{cp0201}
        </if>
    </select>

    <select id="getPs02Detail" resultType="io.renren.modules.enterprise.dto.Ps02DTO">
        SELECT t.pj0101,
               p.NAME,
               p.IDCARDTYPE,
               p.IDCARDNUMBER,
               p.GENDER,
               p.NATION,
               p.POLITICSTYPE,
               p.CELLPHONE,
               p.EDULEVEL,
               p.URGENTLINKMAN,
               p.URGENTLINKMANPHONE,
               p.ADDRESS,
               t.PS0201,
               m.TEAMNAME,
               t.ISTEAMLEADER,
               t.WORKTYPECODE,
               t.ISSUECARDPICURL url
        FROM b_ps02 t,
             B_PS01 p,
             B_TM01 m
        WHERE t.PS0101 = p.PS0101
          AND t.TM0101 = m.TM0101
          AND t.PS0201 = #{ps0201}
    </select>
</mapper>