<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.dao.Ot01Dao">
    <update id="updateByIdBusily">
        update B_OT01 t
        set t.BUSISYSNO=#{busies}
                where t.OT0101 in
        <foreach collection="ot0101" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="getBusinessData" resultType="io.renren.modules.enterprise.dto.Ot01DTO">
        select t.BUSITYPE, t.BUSISYSNO, t.OT0101, t.NAME, t.URL, t.VIEW_TYPE, t.ORIGINAL_NAME
        from B_OT01 t
        where t.BUSISYSNO = #{id}
          and t.BUSITYPE = #{busType}
          and t.WHETHER = '1'
    </select>
    <delete id="deleteByOt0101AndBusisysno">
        delete from B_OT01 t where t.BUSISYSNO = #{busisysno} and t.ot0101 not in
        <foreach collection="ot0101" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
</mapper>