<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.dao.Cp02Dao">
    <select id="getList" resultType="io.renren.modules.enterprise.dto.Cp02DTO">
        select b.CORPNAME, a.CORPTYPE, a.CP0101, a.CP0201, b.CORPCODE, b.LINKMAN, b.LINKCELLPHONE
        from B_CP02 a
                     left join B_CP01 b on a.CP0101 = b.CP0101 where a.PJ0101 = #{pj0101}
        <if test="corpname != null and corpname != ''">
            and b.CORPNAME like '%' || #{corpname} || '%'
        </if>
        order by a.cp0201 desc
    </select>

    <select id="getDetail" resultType="io.renren.modules.enterprise.dto.Cp01DTO">
        SELECT (SELECT listagg(name, '') within GROUP (order by id) regarea
                FROM SYS_REGION
                WHERE ID != 100000
                START WITH ID =
                           (SELECT t.id FROM SYS_REGION t WHERE t.VALUE = c.AREACODE)
                CONNECT BY PRIOR PID = ID) areacode,
               p.CORPTYPE,
               c.CORPCODE,
               c.ORGANIZATIONTYPE,
               c.CORPNAME,
               c.ADDRESS,
               c.LEGALMAN,
               c.REGCAPITAL,
               c.CAPITALCURRENCYTYPE,
               c.ESTABLISHDATE,
               c.LINKMAN,
               c.LINKCELLPHONE,
               c.BUSINESSSTATUS,
               c.ENTSCOPE
        FROM B_CP01 c,
             B_CP02 p
        WHERE c.cp0101 = p.CP0101
          AND p.cp0201 = #{cp0201}
    </select>
    <select id="getCompanySelect" resultType="io.renren.modules.common.dto.SysDictDTO">
        select '（' || (select x.dict_label
                         from sys_dict_data x, sys_dict_type y
                        where x.dict_type_id = y.id
                          and y.dict_type = 'CORPTYPE'
                          and x.dict_value = t.corptype) || '）' || '-' ||
               a.corpname as label,
               t.cp0101 as value
          from B_CP02 t, b_cp01 a
         where t.cp0101 = a.cp0101
           and t.pj0101 = #{pj0101}
    </select>
</mapper>