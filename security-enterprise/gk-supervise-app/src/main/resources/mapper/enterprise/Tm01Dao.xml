<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.dao.Tm01Dao">
    <select id="getList" resultType="io.renren.modules.enterprise.dto.Tm01DTO">
        SELECT t.*, (SELECT count(*) FROM B_PS02 p WHERE p.TM0101 = t.TM0101 AND p.IN_OR_OUT = 1) teamnum
        FROM b_tm01 t  WHERE t.pj0101 = #{pj0101}
                         and t.in_or_out = '1'
        <if test="teamname != null and teamname != ''">
            and t.teamname like '%' || #{teamname} || '%'
        </if>
    </select>

    <select id="getTm01PersonInfo" resultType="io.renren.modules.enterprise.dto.Tm01PersonDTO">
        SELECT s.NAME, s.CELLPHONE phone, p.PS0201 userid, p.ISSUECARDPICURL url, 1 type
        FROM B_TM01 t,
             B_PS02 p,
             B_PS01 s WHERE p.TM0101 = t.TM0101
                        AND p.PS0101 = s.PS0101
                        AND t.TM0101 = #{tm0101}
        <if test="name != null and name != ''">
            and s.NAME like '%' || #{name} || '%'
        </if>
        <if test="inorout != null and inorout != ''">
            and p.IN_OR_OUT = #{inorout}
        </if>
    </select>
</mapper>