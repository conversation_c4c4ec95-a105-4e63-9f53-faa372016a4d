<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.dao.Kq02Dao">
    <select id="getAllPersonKQByPj0101" resultType="io.renren.modules.enterprise.dto.ProjectPersonDTO">
        select a.userid, a.name, a.phone, a.url, a.type
        from (SELECT t.PS0201          userid,
                     p.NAME,
                     p.CELLPHONE       phone,
                     t.ISSUECARDPICURL url,
                     t.pj0101,
                     '1'               type
              FROM B_PS02 t,
                   B_PS01 p
              WHERE t.PS0101 = p.PS0101
              UNION ALL
              SELECT t.PS0401    userid,
                     p.NAME,
                     p.CELLPHONE phone,
                     t.PHOTO     url,
                     t.pj0101,
                     '2'         type
              FROM B_PS04 t,
                   B_PS01 p
              WHERE t.PS0101 = p.PS0101) a,
             b_kq02 b
                where a.pj0101 = #{pj0101}
                  and a.userid = b.user_id
        <if test="dayDate != '' and dayDate != null">
            and to_char(b.checkdate, 'yyyy-MM-dd') = #{dayDate}
        </if>
        <if test="type != null and type != ''">
            and a.type = #{type}
        </if>
        <if test="name != null and name != ''">
            and a.NAME like '%' || #{name} || '%'
        </if>
        group by a.userid, a.name, a.phone, a.url, a.type
    </select>


    <select id="getPs02KQByPj0101" resultType="io.renren.modules.enterprise.dto.ProjectPersonDTO">
        SELECT t.USER_ID userid, p.ISSUECARDPICURL url, s.NAME, s.CELLPHONE phone, pj.NAME proname, t.PERSON_TYPE type
        FROM B_KQ02 t,
             B_PJ01 pj,
             B_PS02 p,
             B_PS01 s WHERE t.PJ0101 = pj.PJ0101
                        AND t.USER_ID = p.PS0201
                        AND p.PS0101 = s.PS0101
                        AND t.PERSON_TYPE = 1
                        AND t.PJ0101 = #{pj0101}
                        AND TO_CHAR(t.CHECKDATE, 'yyyyMMdd')
                = #{date}
        <if test="name != null and name != ''">
            and s.NAME like '%' || #{name} || '%'
        </if>
        order by t.CHECKDATE desc
    </select>

    <select id="getPs04KQByPj0101" resultType="io.renren.modules.enterprise.dto.ProjectPersonDTO">
        SELECT t.USER_ID userid, p.PHOTO url, s.NAME, s.CELLPHONE phone, pj.NAME proname, t.PERSON_TYPE type
        FROM B_KQ02 t,
             B_PJ01 pj,
             B_PS04 p,
             B_PS01 s WHERE t.PJ0101 = pj.PJ0101
                        AND t.USER_ID = p.PS0401
                        AND p.PS0101 = s.PS0101
                        AND t.PERSON_TYPE = 2
                        AND t.PJ0101 = #{pj0101}
                        AND TO_CHAR(t.CHECKDATE, 'yyyyMMdd') = #{date}
                        and pj.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
        <if test="name != null and name != ''">
            and s.NAME like '%' || #{name} || '%'
        </if>
        order by t.CHECKDATE desc
    </select>

    <select id="getPersonKQByAreaCode" resultType="io.renren.modules.enterprise.dto.ProjectPersonDTO">
        SELECT t.NAME proname, k.PERSON_NAME name, k.PERSON_TYPE type, k.IMAGE_URL url, k.CHECKDATE checkdate
        FROM B_PJ01 t,
             B_KQ02 k WHERE k.PJ0101 = t.PJ0101
                        AND t.PRJSTATUS = 3
                        AND TO_CHAR(k.CHECKDATE, 'yyyyMMdd') &gt;= #{startdate}
                        AND TO_CHAR(k.CHECKDATE, 'yyyyMMdd') &lt;= #{enddate}
                        and t.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
        <if test="areacode != null and areacode != ''">
            AND t.AREACODE like '%' || #{areacode} || '%'
        </if>
        <if test="name != null and name != ''">
            and k.PERSON_NAME like '%' || #{name} || '%'
        </if>
        <if test="type != null and type != ''">
            and k.PERSON_TYPE = #{type}
        </if>
        order by k.CHECKDATE desc
    </select>

    <select id="getPersonKQInfo" resultType="java.lang.String">
        SELECT to_char(t.CHECKDATE, 'yyyy-MM-dd') CHECKDATE
        FROM B_KQ02 t
        WHERE t.USER_ID = #{userId}
          AND TO_CHAR(t.CHECKDATE, 'yyyymm') = #{checkdate}
          AND t.PJ0101 = #{pj0101}
    </select>

    <select id="getPersonKQInfoDetail" resultType="io.renren.modules.enterprise.dto.ProjectPersonDTO">
        SELECT t.PERSON_NAME name, t.CHECKDATE, t.PERSON_TYPE type, t.IMAGE_URL url
        FROM B_KQ02 t
        WHERE t.USER_ID = #{userId}
          AND TO_CHAR(t.CHECKDATE, 'yyyyMMdd') = #{checkdate}
          AND t.PJ0101 = #{pj0101}
        ORDER BY t.CHECKDATE DESC
    </select>
</mapper>