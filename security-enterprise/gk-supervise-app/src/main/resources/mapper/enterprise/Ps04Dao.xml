<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.dao.Ps04Dao">
    <select id="getPs04ByPj0101" resultType="io.renren.modules.enterprise.dto.ProjectPersonDTO">
        SELECT p.PS0401 userid, s.NAME, p.PHOTO url, t.NAME proname, s.CELLPHONE phone, 2 type
        FROM B_PJ01 t,
             B_PS04 p
                     LEFT JOIN B_PS01 s on p.PS0101 = s.PS0101 WHERE t.PJ0101 = p.PJ0101
                                                                 AND t.PJ0101 = #{pj0101}
        <if test="inorout != null and inorout != ''">
            AND p.IN_OR_OUT = #{inorout}
        </if>
        <if test="name != null and name != ''">
            and s.NAME like '%' || #{name} || '%'
        </if>
    </select>

    <select id="getPs04Detail" resultType="io.renren.modules.enterprise.dto.Ps04DTO">
        SELECT t.pj0101,
               p.NAME,
               p.IDCARDTYPE,
               p.IDCARDNUMBER,
               p.GENDER,
               p.NATION,
               p.POLITICSTYPE,
               p.CELLPHONE,
               p.EDULEVEL,
               p.URGENTLINKMAN,
               p.URGENTLINKMANPHONE,
               p.ADDRESS,
               t.PS0401,
               c1.CORPNAME,
               t.JOBTYPE,
               t.HASBUYINSURANCE,
               t.PHOTO url
        FROM b_ps04 t,
             B_PS01 p,
             B_CP02 c,
             B_CP01 c1
        WHERE t.PS0101 = p.PS0101
          AND t.CP0201 = c.CP0201
          AND c.CP0101 = c1.CP0101
          AND t.PS0401 = #{ps0401}
    </select>

    <select id="getListData" resultType="io.renren.modules.enterprise.dto.ProjectPersonDTO">
        SELECT s1.name, s1.CELLPHONE phone, s2.PS0401 userid, s2.PHOTO url, c1.CORPNAME
        FROM b_ps01 s1,
             b_PS04 s2,
             b_CP02 c,
             B_CP01 c1
                WHERE s1.ps0101 = s2.ps0101
                  AND s2.CP0201 = c.CP0201
                  AND c.CP0101 = c1.CP0101
                  and s2.in_or_out = 1
                  and s2.pj0101 = #{pj0101}
        <if test="name != null and name != ''">
            and s1.name like '%' || #{name} || '%'
        </if>
        <if test="cp0201 != null and cp0201 != ''">
            and c.cp0201 = #{cp0201}
        </if>
    </select>
</mapper>