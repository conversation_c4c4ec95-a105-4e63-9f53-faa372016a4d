<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.SysAreaDao">

    <resultMap type="io.renren.entity.SysAreaEntity" id="sysAreaMap">
        <result property="id" column="ID"/>
        <result property="pid" column="PID"/>
        <result property="areaName" column="AREA_NAME"/>
        <result property="areaValue" column="AREA_VALUE"/>
        <result property="rank" column="RANK"/>
        <result property="lng" column="LNG"/>
        <result property="lat" column="LAT"/>
        <result property="whether" column="WHETHER"/>
        <result property="postalcode" column="POSTALCODE"/>
        <result property="remarks" column="REMARKS"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="sorts" column="SORTS"/>
    </resultMap>
    <!--通过行政区划代码查询行政区划信息-->
    <select id="selectByAreavalue" resultType="io.renren.entity.SysAreaEntity" parameterType="java.lang.String">
      select a.id,a.pid,a.area_name,a.area_value from sys_area a where a.area_value=#{areacode}
    </select>

    <!--通过pid查询子集-->
    <select id="selectByParentId" resultType="io.renren.entity.SysAreaEntity" parameterType="java.lang.Long">
        select a.id,a.pid,a.area_name,a.area_value from sys_area a where a.pid=#{pid} order by to_number(a.sorts)  asc
    </select>

    <!--查询所有行政区划-->
    <select id="selectAll" resultType="io.renren.entity.SysAreaEntity">
        select a.area_name,a.area_value from sys_area a
    </select>

    <!--通过行政区划代码查询行政区划信息（只查询可用（真实）行政区划）-->
    <select id="selectByAreavalueAndWhether" resultType="io.renren.entity.SysAreaEntity" parameterType="java.lang.String">
        select a.* from sys_area a where a.area_value=#{areacode} and a.whether='1'  order by a.area_value asc
    </select>

    <!--查询行政区划列表（默认查询可用的区划，按照区划值升序排序）-->
    <select id="selectByAreavalueList" resultType="io.renren.entity.SysAreaEntity" parameterType="java.lang.String">
        select a.* from sys_area a where a.area_value like #{areacode}||'__' and a.whether='1' order by a.area_value asc
    </select>

    <select id="selectByOne" resultType="io.renren.entity.SysAreaEntity">
        select * from SYS_AREA t where t.area_value like  #{areacode}||'%' and t.area_name like '%'||#{city}||'%'
    </select>

</mapper>