<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.TokenDao">



    <select id="getByToken" resultType="io.renren.entity.TokenEntity">
        select * from SYS_USER_TOKEN where token = #{value}
    </select>

    <select id="getByUserId" resultType="io.renren.entity.TokenEntity">
        select * from sys_user_token where user_id = #{value}
    </select>

    <select id="getDictVersion" resultType="Long">
        select status from SYS_TRIGGER where NAME='SYS_DICT'
    </select>

    <delete id="delToken">
        delete from SYS_USER_TOKEN where USER_ID = #{userId}
    </delete>
</mapper>