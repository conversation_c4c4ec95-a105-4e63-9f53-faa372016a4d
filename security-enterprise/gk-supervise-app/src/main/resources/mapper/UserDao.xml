<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.UserDao">

    <select id="getUserByMobile" resultType="io.renren.entity.UserEntity">
        select * from SYS_USER where mobile = #{value}
    </select>

    <select id="getUserByUserId" resultType="io.renren.entity.UserEntity">
        select * from SYS_USER where id = #{value}
    </select>


</mapper>