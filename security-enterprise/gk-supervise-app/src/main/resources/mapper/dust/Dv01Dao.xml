<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.Dv01Dao">
    <select id="getDv01ByPj0101" resultType="io.renren.modules.dust.dto.Dv01DTO">
        SELECT t.DEVICENAME, t.DEVICESTATE, t.URL, t.DV0301
        from YC_DV03 t
        where t.pj0101 = #{pj0101}
    </select>
</mapper>