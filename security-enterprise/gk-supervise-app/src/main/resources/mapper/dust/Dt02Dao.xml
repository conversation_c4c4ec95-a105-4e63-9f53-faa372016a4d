<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.Dt02Dao">
    <select id="projectHistoryWarnData" resultType="io.renren.modules.dust.dto.Dt02DTO">
        SELECT *
        FROM (SELECT t.DT0201,
                     t.PM25VALUE,
                     t.PM10VALUE,
                     t.VOICE,
                     t.TEMPERATURE,
                     t.WINDDIRECTION,
                     t.WINDSPEED,
                     t.<PERSON>ON<PERSON>ORINGTIME
              FROM YC_DT02 t
              WHERE to_char(t.MONITORINGTIME, 'yyyy-MM-dd') = #{startDate}
                AND t.PJ0101 = #{pj0101}
                AND T.WARNTYPE = #{type}
              ORDER BY t.MONITORINGTIME DESC
                     ) tmpd
    </select>
</mapper>