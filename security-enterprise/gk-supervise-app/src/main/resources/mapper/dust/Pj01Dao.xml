<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.common.dao.Pj01Dao">
    <select id="getDetail" resultType="io.renren.modules.dust.dto.YcProDetailDTO">
        select t.pj0101,
               t.name,
               t.address,
               t.linkman,
               t.linkphone,
               (select sum(x.warcount)
                from yc_yj01 x
                where x.pj0101 = t.pj0101
                  and to_char(x.wardate, 'yyyyMMdd') = to_char(sysdate, 'yyyyMMdd')) as warncount,
               b.*
        from b_pj01 t
                     left join (Select Pj0101, Max(Dt0101) Mx From Yc_Dt01 t Group By Pj0101) a
                on t.pj0101 = a.pj0101
                     left join yc_dt01 b
                on b.dt0101 = a.mx
        where t.pj0101 = #{pj0101}
    </select>


    <select id="getPj01List" resultType="io.renren.modules.common.dto.Pj0101DTO">
        select * from (Select t.pj0101,
                              t.name,
                              t.address,
                              t.update_date,
                              fun_get_dv03state(t.pj0101)                                           as camera,
                              (select x.LONGITUDE from yc_dv01 x where x.pj0101 = t.pj0101)         as lng,
                              (select x.LATITUDE from yc_dv01 x where x.pj0101 = t.pj0101)          as lat,
                              (select sum(x.warcount)
                               from yc_yj01 x
                               where x.pj0101 = t.pj0101
                                 and to_char(x.wardate, 'yyyyMMdd') = to_char(sysdate, 'yyyyMMdd')) as warnnum,
                              c.pm10value,
                              c.pm25value,
                              c.voice,
                              c.monitoringtime
                       From b_Pj01 t
                                    left join (Select Pj0101, Max(Dt0101) Mx From Yc_Dt01 t Group By Pj0101) b
                               on t.pj0101 = b.pj0101
                                    left join yc_dt01 c
                               on c.dt0101 = b.mx,
                            r_Pj01_Dept a,
                            sys_dept d
                Where t.Pj0101 = a.Pj0101
                  and t.dept_id = d.id
                  And a.Dept_Id = #{deptId}
                  and t.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectDustProject">
    </include>)
        <if test="areaCode != null and areaCode != ''">
            and d.areacode = #{areaCode}
        </if>
        <if test="name != null and name != ''">
            and t.name like '%' || #{name} || '%'
        </if>) t where 1 = 1
        <if test="isWarn != null and isWarn != ''">
            and t.warnnum is not null
        </if>
        order by t.update_date desc
    </select>


    <select id="getArea" resultType="io.renren.modules.dust.dto.AreaDTO">
        SELECT a.NAME areaname, a.VALUE areacode
        from SYS_REGION a
        WHERE a.value like #{areaCode} || '%'
          and a.leaf != 1
    </select>

    <select id="getTotalPj01ByDeptId" resultType="int">
        SELECT count(*)
                FROM (SELECT t.PJ0101
                      FROM R_PJ01_DEPT t,
                           B_PJ01 p
                WHERE t.PJ0101 = p.PJ0101
                  AND p.PRJSTATUS = 3
                  AND t.DEPT_ID = #{deptId}
                  and p.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
            GROUP BY t.PJ0101)
    </select>

    <select id="addInfo" resultType="int">
        SELECT count(*)
                FROM (SELECT t.PJ0101
                      FROM R_PJ01_DEPT t,
                           B_PJ01 p
                WHERE t.PJ0101 = p.PJ0101
                  AND t.DEPT_ID = #{deptId}
                  AND TO_CHAR(p.CREATE_DATE, 'YYYY-MM') = TO_CHAR(SYSDATE, 'YYYY-MM')
                  and p.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
            GROUP BY t.PJ0101)
    </select>

    <select id="getTodayNumber" resultType="int">
        SELECT count(*)
        FROM (SELECT p2.PJ0101, p2.IN_OR_OUT
              FROM b_ps02 p2
              UNION ALL
              SELECT p4.PJ0101,
                     p4.IN_OR_OUT
              FROM B_PS04 p4) t
                WHERE t.PJ0101 in
                (SELECT r.PJ0101
                 FROM R_PJ01_DEPT r,
                      B_PJ01 p
                WHERE r.PJ0101 = p.PJ0101
                  AND r.DEPT_ID = #{deptId}
                  AND p.PRJSTATUS = '3'
                  and p.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>))
            AND t.IN_OR_OUT = 1
    </select>

    <select id="getTodayKQNumber" resultType="int">
        SELECT count(distinct (tmp.user_id))
                FROM (SELECT k.USER_ID
                      FROM B_KQ02 k
                WHERE k.PJ0101 IN
                (SELECT r.PJ0101
                 FROM R_PJ01_DEPT r,
                      B_PJ01 p
                WHERE r.PJ0101 = p.PJ0101
                  AND r.DEPT_ID = #{deptId}
                  AND p.PRJSTATUS = 3
                  and p.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>))
            AND TO_CHAR(k.CHECKDATE, 'yyyy-MM-dd') = TO_CHAR(SYSDATE, 'yyyy-MM-dd')) tmp
    </select>

    <select id="getProjectNum" resultType="int">
        SELECT count(*)
                FROM (select c.pj0101
                      from b_pj01 c
                where c.pj0101 not in (select distinct (a.pj0101)
                                       from b_ps04 a
                                       where a.jobtype in (select b.jobtype from KEYJOB_CONFIG b)
                                         AND a.IN_OR_OUT = 1)
                  AND c.pj0101 in (SELECT r.PJ0101
                                   FROM R_PJ01_DEPT r,
                                        B_PJ01 p
                                   WHERE r.PJ0101 = p.PJ0101
                                     AND r.DEPT_ID = #{deptId})
                  and c.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
            AND c.PRJSTATUS = 3)
    </select>

    <select id="getNotKQProjectNum" resultType="int">
        select count(0)
        from b_pj01 a,
             r_pj01_dept b
                where a.pj0101 = b.pj0101
                  and a.prjstatus = '3'
                  and b.dept_id = #{deptId}
                  and a.PJ0101 not in (select c.pj0101
                                       from b_kq02 c
                                       where to_char(c.checkdate, 'yyyy-MM-dd') =
                                             to_char(sysdate, 'yyyy-MM-dd')
                                       group by c.pj0101)
                  and a.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
    </select>

    <select id="getTotal" resultType="io.renren.modules.enterprise.dto.HomeDTO">
        SELECT p.NAME,
               p.VALUE,
                (select count(0)
                 from b_pj01 a
                where a.areacode like '' || p.value || '%'
                  AND a.PRJSTATUS = 3
                  and a.PJ0101 in (<include
            refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)) projectSum,
            (select count(0)
             from b_pj01 b,
                  (SELECT c.PJ0101
                   FROM b_ps02 c
                   WHERE c.IN_OR_OUT = 1
                   UNION ALL
                   SELECT s.PJ0101
                   FROM B_PS04 s
                   WHERE s.IN_OR_OUT = 1) tmp
            where b.pj0101 = tmp.pj0101
              and b.areacode like '' || p.value || '%'
              and tmp.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
            AND b.PRJSTATUS = 3)    personSum,
            (select count(e.user_id)
             from b_pj01 d,
                  b_kq02 e
            where d.pj0101 = e.pj0101
              and TO_CHAR(e.CHECKDATE, 'yyyy-MM-dd') =
                  TO_CHAR(SYSDATE, 'yyyy-MM-dd')
              and d.areacode like '' || p.value || '%'
              AND d.PRJSTATUS = 3
              and d.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)) personKqSum
            from SYS_REGION p
            START WITH p.pid = (select f.id
                                from sys_region f,
                                     sys_dept t
                                where f.value = t.areacode
                                  and t.id = #{deptId})
            CONNECT BY PRIOR p.id = p.pid
            order by p.value
    </select>

    <select id="getTotalBuildNum" resultType="io.renren.modules.enterprise.dto.BuildDTO">
        SELECT (SELECT listagg(name, '') within GROUP (order by id) regarea
                FROM SYS_REGION
                WHERE ID != 100000
                  AND ID !=
                      510000
                START WITH ID = (SELECT t.id FROM SYS_REGION t WHERE t.VALUE = s.VALUE)
                CONNECT BY PRIOR PID = ID) areaname,
               p.NAME,
               p.LINKMAN,
               p.LINKPHONE,
               p.PJ0101
        FROM R_PJ01_DEPT r,
             B_PJ01 p,
             SYS_REGION s WHERE r.PJ0101 = p.PJ0101
                            AND p.AREACODE = s.VALUE
                            AND r.DEPT_ID = #{deptId}
                            AND p.PRJSTATUS = 3
                            and p.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
        <if test="name != null and name != ''">
            and p.NAME like '%' || #{name} || '%'
        </if>
        ORDER BY s.VALUE
    </select>

    <select id="getMonthAddNum" resultType="io.renren.modules.enterprise.dto.BuildDTO">
        SELECT (SELECT listagg(name, '') within GROUP (order by id) regarea
                FROM SYS_REGION
                WHERE ID != 100000
                  AND ID !=
                      510000
                START WITH ID = (SELECT t.id FROM SYS_REGION t WHERE t.VALUE = s.VALUE)
                CONNECT BY PRIOR PID = ID) areaname,
               p.NAME,
               p.LINKMAN,
               p.LINKPHONE,
               p.PJ0101
        FROM R_PJ01_DEPT r,
             B_PJ01 p,
             SYS_REGION s WHERE r.PJ0101 = p.PJ0101
                            AND p.AREACODE = s.VALUE
                            AND r.DEPT_ID = #{deptId}
                            AND TO_CHAR(p.CREATE_DATE, 'YYYY-MM') = TO_CHAR(SYSDATE, 'YYYY-MM')
                            and p.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
        <if test="name != null and name != ''">
            and p.NAME like '%' || #{name} || '%'
        </if>
        ORDER BY s.VALUE
    </select>

    <select id="getTodayNum" resultType="io.renren.modules.enterprise.dto.PersonDTO">
        SELECT (select a.name from sys_region a where p.areacode = a.value) areaname,
               p.NAME,
               p.PJ0101,
               (SELECT count(*)
                FROM B_PS02 p2
                WHERE p2.PJ0101 = p.PJ0101
                  AND p2.IN_OR_OUT = 1)                                     ps02num,
               (SELECT count(*)
                FROM B_PS04 p4
                WHERE p4.PJ0101 = p.PJ0101
                  AND p4.IN_OR_OUT = 1)                                     ps04num
        FROM R_PJ01_DEPT t,
             B_PJ01 p
                WHERE t.PJ0101 = p.PJ0101
                  AND t.DEPT_ID = #{deptId}
                  and p.prjstatus = '3'
                  and p.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
        <if test="name != null and name != ''">
            and p.NAME like '%' || #{name} || '%'
        </if>
        <if test="areacode != null and areacode != ''">
            and p.AREACODE = #{areacode}
        </if>
        ORDER BY p.AREACODE, p.PJ0101
    </select>

    <select id="getTodayKQNum" resultType="io.renren.modules.enterprise.dto.PersonDTO">
        SELECT (select d.name from sys_region d where p.areacode = d.value) as areaname,
               p.NAME,
               p.PJ0101,
                (SELECT count(distinct (k.user_id))
                 FROM B_KQ02 k
                WHERE p.PJ0101 = k.PJ0101
                  AND k.PERSON_TYPE = '1'
        <if test="dayDate != ''and dayDate != null">
            AND TO_CHAR(k.CHECKDATE, 'yyyy-MM-dd') = #{dayDate}
        </if>
        )                                              ps02num,
                (SELECT count(distinct (k.user_id))
                 FROM B_KQ02 k
                WHERE p.PJ0101 = k.PJ0101
                  AND k.PERSON_TYPE = '2'
        <if test="dayDate != '' and dayDate != null">
            AND TO_CHAR(k.CHECKDATE, 'yyyy-MM-dd') = #{dayDate}
        </if>
        ) ps04num
                FROM R_PJ01_DEPT t,
                     B_PJ01 p
                WHERE t.PJ0101 = p.PJ0101
                  and p.prjstatus = '3'
                  AND t.DEPT_ID = #{deptId}
                  and p.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
        <if test="name != null and name != ''">
            and p.NAME like '%' || #{name} || '%'
        </if>
        <if test="areacode != null and areacode != ''">
            and p.AREACODE = #{areacode}
        </if>
        ORDER BY p.AREACODE, p.PJ0101
    </select>

    <select id="getNoKeyPositionsPro" resultType="io.renren.modules.enterprise.dto.BuildDTO">
        SELECT (SELECT listagg(name, '') within GROUP (order by id) regarea
                FROM SYS_REGION
                WHERE ID != 100000
                  AND ID
                        != 510000
                START WITH ID = (SELECT t.id FROM SYS_REGION t WHERE t.VALUE = p.AREACODE)
                CONNECT BY PRIOR PID = ID) areaname,
               p.NAME,
               p.PJ0101,
               p.LINKMAN,
               p.LINKPHONE
        FROM R_PJ01_DEPT t,
             B_PJ01 p WHERE t.PJ0101 = p.PJ0101
                        AND t.DEPT_ID = #{deptId}
                        AND p.PJ0101 not in
                            (select distinct (a.pj0101)
                             from b_ps04 a
                             where a.jobtype in (select b.jobtype from KEYJOB_CONFIG b)
                               AND a.IN_OR_OUT = 1)
                        AND p.PRJSTATUS = 3
                        and p.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
        <if test="name != null and name != ''">
            and p.NAME like '%' || #{name} || '%'
        </if>
        ORDER BY p.AREACODE
    </select>

    <select id="getNotKQProject" resultType="io.renren.modules.enterprise.dto.BuildDTO">
        select e.PJ0101,
               e.areaname,
               e.NAME,
               e.LINKMAN,
               e.LINKPHONE,
                (SELECT max(f.checkdate)
                 FROM b_kq02 f
                WHERE e.pj0101 = f.pj0101
        <if test="date != null and date != ''">
            and to_char(f.CREATE_DATE, 'yyyy-MM-dd') &lt; #{date}
        </if>
        ) lastkqdate
                from (select t.pj0101,
                             t.name,
                             t.linkman,
                             t.linkphone,
                             (select d.name from sys_region d where t.areacode = d.value) as areaname
                      from b_pj01 t,
                           r_pj01_dept a
                where t.pj0101 = a.pj0101
                  and a.dept_id = #{deptId}
        <if test="name != null and name != ''">
            and t.NAME like '%' || #{name} || '%'
        </if>
        and t.prjstatus = '3'
        and t.PJ0101 not in (select b.pj0101
                             from b_kq02 b
                             where to_char(b.checkdate, 'yyyy-MM-dd') = #{date}
                             group by b.pj0101)
                ORDER BY t.AREACODE) e where e.PJ0101 in (<include
            refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
    </select>

    <select id="getTotalProject" resultType="io.renren.modules.enterprise.dto.BuildDTO">
        SELECT (SELECT listagg(name, '') within GROUP (order by id) regarea
                FROM SYS_REGION
                WHERE ID != 100000
                  AND ID !=
                      510000
                START WITH ID = (SELECT t.id FROM SYS_REGION t WHERE t.VALUE = p.AREACODE)
                CONNECT BY PRIOR PID = ID)
                       areaname,
               p.NAME,
               p.PJ0101,
               p.LINKMAN,
               p.LINKPHONE
        FROM R_PJ01_DEPT t,
             B_PJ01 p WHERE t.PJ0101 = p.PJ0101
                        AND t.DEPT_ID
                = #{deptId}
                        AND p.PRJSTATUS = 3
                        and p.PJ0101 in (<include
            refid="io.renren.dao.AppSuperviseCommonDao.selectRealNameProject">
    </include>)
        <if test="name != null and name != ''">
            and p.NAME like '%' || #{name} || '%'
        </if>
        <if test="areacode != null and areacode != ''">
            AND p.AREACODE like '%' || #{areacode} || '%'
        </if>
    </select>
    <select id="getStatistics" resultType="io.renren.modules.common.dto.StatisticsDTO">
        SELECT t.name,
               (SELECT COUNT(m.tm0101) FROM b_tm01 m WHERE m.pj0101 = t.pj0101 and m.in_or_out = '1')     as teamNum,
               (SELECT COUNT(c.cp0201) FROM b_cp02 c WHERE c.pj0101 = t.pj0101)                           as cpNum,
               (SELECT COUNT(p2.ps0201) FROM b_ps02 p2 WHERE p2.pj0101 = t.pj0101 and p2.in_or_out = '1') as workerNum,
               (SELECT COUNT(p4.ps0401) FROM b_ps04 p4 WHERE p4.pj0101 = t.pj0101 and p4.in_or_out = '1') as managerNum,
               (SELECT COUNT(distinct (k.user_id))
                FROM b_kq02 k
                WHERE k.pj0101 = t.pj0101
                  and to_char(k.checkdate, 'yyyy-MM-dd') =
                      to_char(sysdate, 'yyyy-MM-dd'))                                                     as kqNum
        FROM b_pj01 t
        WHERE t.pj0101 = #{pj0101}
    </select>

    <select id="getPj01Info" resultType="io.renren.modules.common.dto.Pj01DTO">
        select *
        from b_pj01 t
        where t.pj0101 = #{pj0101}
    </select>

    <select id="getSpecialAccountInfo" resultType="io.renren.modules.common.dto.Pa01DTO">
        select *
        from b_pa01 t
        where t.pj0101 = #{pj0101}
    </select>

    <select id="getBuilderLicenseNum" resultType="io.renren.modules.enterprise.dto.Pj02DTO">
        SELECT t.PRJNAME, t.BUILDERLICENSENUM, t.ORGANNAME, t.ORGANDATE
        FROM B_PJ02 t
        WHERE t.PJ0101 = #{pj0101}
    </select>

    <select id="getContractInfo" resultType="io.renren.modules.common.dto.ContractDTO">
        select t.name, t.url
        from B_OT01 t
        WHERE t.busitype = '01'
          and t.whether = '1'
          and t.busisysno = #{pj0101}
    </select>

    <select id="getDustProAndDeviceNum" resultType="java.util.Map">
        SELECT (SELECT count(DISTINCT t.PJ0101)
                FROM YC_DV01 t,
                     B_PJ01 p,
                     R_PJ01_DEPT d
                WHERE t.PJ0101 = p.PJ0101
                  AND p.PJ0101 = d.PJ0101
                  AND d.DEPT_ID = #{deptid}
                  and p.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectDustProject">
    </include>))                          proNum,
            (SELECT count(*)
             FROM YC_DV01 t,
                  B_PJ01 p,
                  R_PJ01_DEPT d
            WHERE t.DEVICESTATE = 0
              AND t.PJ0101 = p.PJ0101
              AND p.PJ0101 = d.PJ0101
              AND d.DEPT_ID = #{deptid}
              and p.PJ0101 in (<include refid="io.renren.dao.AppSuperviseCommonDao.selectDustProject">
    </include>)) deviceNum
            FROM DUAL
    </select>
    <select id="getProjectSelect" resultType="io.renren.modules.common.dto.SysDictDTO">
        select t.pj0101 as value, t.name as label
          from B_PJ01 t, r_pj01_dept a
         where t.pj0101 = a.pj0101
           and a.dept_id = #{deptId}
    </select>
</mapper>