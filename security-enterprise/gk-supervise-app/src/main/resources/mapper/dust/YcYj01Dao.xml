<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcYj01Dao">
    <select id="getYj01List" resultType="io.renren.modules.dust.dto.YcYj01DTO">
        SELECT t.PJ0101, t.WARTYPE, to_char(t.WARDATE, 'yyyy-MM-dd') WARDATE, t.WARCOUNT
        from YC_YJ01 t WHERE t.PJ0101 = #{pj0101}
        <if test="startDate != null and startDate != ''">
            and to_char(t.WARDATE, 'yyyy-MM-dd') = #{startDate}
        </if>
        order by t.WARDATE desc
    </select>
</mapper>