<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.Dt01Dao">
    <select id="newData" resultType="io.renren.modules.dust.dto.Dt01DTO">
        select *
        from (select t.monitoringtime,
                     t.pm10value,
                     t.pm25value,
                     t.voice,
                     t.temperature,
                     t.humidity,
                     t.windspeed,
                     t.winddirection,
                     t.atmospheric
              from yc_dt01 t
              where t.pj0101 = #{pj0101}
              order by t.monitoringtime desc)
        where rownum &lt; 2
    </select>
    <select id="historyData" resultType="io.renren.modules.dust.dto.Dt01DTO">
        select *
        from (select t.monitoringtime, t.${target}, '0' as isexceed
              from yc_dt01 t
              where t.pj0101 = #{pj0101}
              union all
              select x.monitoringtime, x.${target}, '1' as isexceed
              from yc_dt02 x
              where x.pj0101 = #{pj0101}) t
                where 1 = 1
        <if test="startDate != null and startDate != ''">
            and t.monitoringtime &gt;= to_date(#{startDate} || ' 00:00:00', 'yyyyMMdd hh24:mi:ss')
        </if>
        <if test="startDate != null and startDate != ''">
            and t.monitoringtime &lt;= to_date(#{endDate} || ' 23:59:59', 'yyyyMMdd hh24:mi:ss')
        </if>
        order by t.monitoringtime desc
    </select>

    <select id="projectHistoryData" resultType="io.renren.modules.dust.dto.Dt01DTO">
        SELECT *
        FROM (SELECT t.PM25VALUE,
                     t.PM10VALUE,
                     t.VOICE,
                     t.TEMPERATURE,
                     t.WINDDIRECTION,
                     t.WINDSPEED,
                     t.MONITORINGTIME
              FROM YC_DT01 t
              WHERE to_char(t.MONITORINGTIME, 'yyyy-MM-dd') = #{startDate}
                AND t.PJ0101 = #{pj0101}
              ORDER BY t.MONITORINGTIME DESC
                     ) tmp
    </select>


    <select id="getPageDetail" resultType="io.renren.modules.dust.dto.YcCountyDTO">
        SELECT y.AREACODE,
               y.AREANAME,
               y.PROJECTS,
               y.ACCESSPROJECTS,
               y.ACCESSPRECENT,
               y.OFFLINENUM,
               y.EXCEEDPRECENT,
               y.RUNPRECENT,
               y.TJDATE
        FROM YC_YJ02 y
        WHERE to_char(y.TJDATE, 'yyyy-mm-DD') = to_char(to_date(#{date}, 'yyyy-mm-dd'), 'yyyy-mm-DD')
          AND y.AREACODE like concat(#{areacode}, '%')
        ORDER BY y.EXCEEDPRECENT DESC
    </select>

    <select id="getYcyj02List" resultType="io.renren.modules.dust.dto.YcYj02DTO">
        select *
        from YC_YJ02 y
        where TO_CHAR(y.TJDATE, 'yyyymmdd') = TO_CHAR(#{date}, 'yyyymmddd')
          AND y.AREACODE like concat(#{areacode}, '%')
    </select>
</mapper>