<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.Dv03Dao">
    <select id="getDeviceInfo" resultType="io.renren.modules.dust.dto.Dv03DTO">
        SELECT p.NAME, d.DEVICENAME, d.DEVICESTATE, d.URL, d.DV0301
        from B_PJ01 p,
             YC_DV03 d
        WHERE p.PJ0101 = d.PJ0101
          and p.AREACODE like concat(#{areacode}, '%')
    </select>
</mapper>