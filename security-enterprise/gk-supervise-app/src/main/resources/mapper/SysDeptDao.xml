<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.SysDeptDao">

    <select id="getList" resultType="io.renren.entity.SysDeptEntity">
        select t1.*,(select t2.name from sys_dept t2 where t2.id=t1.pid)parentName from sys_dept t1
        <where>
            <if test="deptIdList != null">
                t1.id in
                <foreach item="id" collection="deptIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by t1.sort asc
    </select>

    <select id="getById" resultType="io.renren.entity.SysDeptEntity">
        select t1.*, (select t2.name from sys_dept t2 where t2.id = t1.pid) parentName
        from sys_dept t1
        where t1.id = #{value}
    </select>

    <select id="getIdAndPidList" resultType="io.renren.entity.SysDeptEntity">
        select t1.id, t1.pid
        from sys_dept t1
    </select>

    <select id="getAll" resultType="io.renren.entity.SysDeptEntity">
        select t1.*
        from sys_dept t1
        where 1=1
        <if test="orgtype != null  and orgtype != ''">
            and t1.ORGTYPE = #{orgtype}
        </if>
    </select>

    <select id="getSubDeptIdList" resultType="long">
        select id
        from sys_dept
        where pids like #{id}
    </select>
    <select id="getDeptId" resultType="java.lang.Long">
        select a.id
        from Sys_Dept a
        <choose>
            <when test="param2 == '-1'.toString()">
                start with a.id in (select t.id from sys_dept t where t.areacode=(select areacode from sys_dept where
                id=#{param1}))
            </when>
            <otherwise>
                start with a.id = #{param1}
            </otherwise>
        </choose>
        connect by prior a.id = a.pid
    </select>
    <select id="getType" resultType="java.lang.String">
        select t.orgtype
        from sys_dept t
        where t.id = #{deptId}
    </select>
	

</mapper>