<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.SysRoleDao">

    <select id="selectUserRoleList" resultType="io.renren.dto.SysRoleDTO">
        select a.id, a.name
        from sys_role a
        where a.id in (select t.role_id
                       from sys_role_user t,
                            sys_user b
                       where t.user_id = b.id
                         and b.username = #{username})
    </select>


</mapper>