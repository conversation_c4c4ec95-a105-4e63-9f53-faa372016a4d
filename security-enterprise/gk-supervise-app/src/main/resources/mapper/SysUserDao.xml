<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.dao.SysUserDao">

    <select id="getCitySx" resultType="java.lang.String">
        select SUBSTR(get_pyjm(t.area_name),1,${param1}) from SYS_AREA t where t.area_value = #{param2}
    </select>

    <select id="getMaxUser" resultType="java.lang.String">
        select SUBSTR(max(t.username),3,6) from SYS_USER t, sys_dept b where t.dept_id = b.id and t.username like #{param1}||'%' and b.orgtype = 1 and length(t.username) &gt; 5
    </select>

    <select id="getSysUserByIdCardByName" resultType="io.renren.dto.SysUserDTO">
        select t.* from sys_user t
        where t.mobile=#{mobile} and t.real_name=#{realName} and t.idnumber=#{idcardnumber}
    </select>


    <update id="updatePwd" parameterType="java.util.Map">
        update sys_user  set password = #{newPwd}  where id = #{userId}

    </update>

</mapper>