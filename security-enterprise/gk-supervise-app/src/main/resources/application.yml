# undertow
server:
  undertow:
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分，不要设置太大，以免影响其他应用，合适即可
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      # 不要设置过大，如果过大，启动项目会报错：打开文件数过多
      io: 4
      # 阻塞任务线程池, 当执行类似servlet请求阻塞IO操作, undertow会从这个线程池中取得线程
      # 它的值设置取决于系统线程执行任务的阻塞系数，默认值是IO线程数*8
      worker: 20
  #    uri-encoding: UTF-8
  #    max-threads: 1000
  #    min-spare-threads: 30
  #  金筑工测试环境端口 port: 8102
  #  金筑工正式环境端口 port: 8003
  port: 8003
  servlet:
    context-path: /
    session:
      cookie:
        http-only: true

# mysql
spring:
  # 环境 dev|test|prod
  profiles:
    active: dev
  messages:
    encoding: UTF-8
    basename: i18n/messages
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
renren:
  redis:
    open: false  # 是否开启redis缓存  true开启   false关闭
# 附件储存方式：1、本地储存；2、附件服务器储存 ，默认附件服务器存储
upload:
  isOpenLocal: false
#mybatis
mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: ID_WORKER
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
