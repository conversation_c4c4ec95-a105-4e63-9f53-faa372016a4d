spring:
  datasource:
    druid:
      driver-class-name: oracle.jdbc.OracleDriver
      #url: *******************************************
      url: **************************************
      username: gk_nmg_smz_dev
      password: gk_nmg_smz_dev
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
    # mq配置
  rabbitmq:
    host: **********
    port: 5672
    username: xygk
    password: xygk@123
    virtual-host: /
      #redis配置
  redis:
    database: 0
    host: **********
    port: 6379
    password:    # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  #文件下载
download:
  installpackUrl: D:/公司扬尘设备对接服务/download/金筑工.apk
camera:
  url: http://**********:8103