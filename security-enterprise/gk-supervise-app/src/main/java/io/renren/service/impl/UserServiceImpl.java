/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package io.renren.service.impl;

import cn.hutool.core.comparator.VersionComparator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.exception.ErrorCode;
import io.renren.common.exception.RenException;
import io.renren.common.service.impl.BaseServiceImpl;
import io.renren.common.validator.AssertUtils;
import io.renren.dao.*;
import io.renren.dto.SysRoleDTO;
import io.renren.dto.SysUserDTO;
import io.renren.entity.*;
import io.renren.security.password.PasswordUtils;
import io.renren.service.TokenService;
import io.renren.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class UserServiceImpl extends BaseServiceImpl<UserDao, UserEntity> implements UserService {
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysUserDao sysUserDao;
    @Autowired
    private BAa01Dao bAa01Dao;
    @Value("${download.installpackUrl}")
    private String installpackUrl;
    @Autowired
    private SysRoleDao sysRoleDao;
    @Autowired
    private SysDeptDao sysDeptDao;


    @Override
    public UserEntity getUserByUserId(Long userId) {
        return baseDao.getUserByUserId(userId);
    }

    @Override
    public Map<String, Object> login(SysUserDTO dto) {
        SysUserEntity sysUserEntity = sysUserDao.selectOne(new QueryWrapper<SysUserEntity>()
                .eq("username", dto.getUsername()).in("USER_TYPE", "0", "4"));
        if (sysUserEntity == null) {
            throw new RenException("用户不存在，请确认用户名是否正确！");
        }
        AssertUtils.isNull(sysUserEntity, ErrorCode.ACCOUNT_PASSWORD_ERROR);

        //密码错误
        if (!PasswordUtils.matches(dto.getPassword(), sysUserEntity.getPassword())) {
            throw new RenException(ErrorCode.ACCOUNT_PASSWORD_ERROR);
        }
        List<SysRoleDTO> userRoleDTOList = sysRoleDao.selectUserRoleList(dto.getUsername());

        //获取登录token
        TokenEntity tokenEntity = tokenService.createToken(sysUserEntity.getId());
        Map<String, Object> map = new HashMap<>(2);
        map.put("token", tokenEntity.getToken());
        map.put("userName", sysUserEntity.getUsername());
        map.put("userId", sysUserEntity.getId());
        map.put("realname", sysUserEntity.getRealName());
        map.put("userRole",userRoleDTOList);
        map.put("deptid",sysUserEntity.getDeptId());
        //实名制首页查询汇总数据用
        SysDeptEntity sysDeptEntity = sysDeptDao.getById(sysUserEntity.getDeptId());
        map.put("areacode",sysDeptEntity.getAreacode());
        map.put("deptName", sysDeptEntity.getName());
        return map;
    }

    @Override
    public Integer updatePwd(Map<String, String> map) {
        if (map.get("oldPwd").isEmpty() || map.get("newPwd").isEmpty()) {
            throw new RenException("旧密码或新密码不能为空！");
        }
        if (map.get("oldPwd").equals(map.get("newPwd").isEmpty())) {
            throw new RenException("旧密码与新密码相同！");
        }
        String ooldPwd = sysUserDao.selectById(map.get("userId")).getPassword();
        if (!PasswordUtils.matches(map.get("oldPwd"), ooldPwd)) {
            throw new RenException("旧密码不正确,请确认！");
        }
        String nNewPwd = PasswordUtils.encode(map.get("newPwd"));
        map.put("newPwd", nNewPwd);
        return sysUserDao.updatePwd(map);
    }

    @Override
    public int checkForUpdate(String version) {
        //检查版本号，若与数据库最新版本号一致，提示已是最新版本，否则提供下载流
        BAa01Entity aa01 = bAa01Dao.selectOne(new QueryWrapper<BAa01Entity>().eq("AA0102", "1"));
        // 系统app版本
        String aa0103 = aa01.getAa0103();
        return VersionComparator.INSTANCE.compare(aa0103, version);
    }

    @Override
    public void download(HttpServletResponse response) {
        try {
            // path是指欲下载的文件的路径。
            File file = new File(installpackUrl);
            // 取得文件名。
            String filename = file.getName();
            // 取得文件的后缀名。
            String ext = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();

            // 以流的形式下载文件。
            InputStream fis = new BufferedInputStream(new FileInputStream(installpackUrl));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(filename.getBytes("utf-8"), "ISO-8859-1"));
            response.addHeader("Content-Length", "" + file.length());
            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            toClient.write(buffer);
            toClient.flush();
            toClient.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }
}