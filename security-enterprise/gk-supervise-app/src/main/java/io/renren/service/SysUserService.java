/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.service;

import io.renren.common.page.PageData;
import io.renren.common.service.BaseService;
import io.renren.dto.SysUserDTO;
import io.renren.entity.SysUserEntity;


import java.util.List;
import java.util.Map;


/**
 * 系统用户
 * 
 * <AUTHOR>
 */
public interface SysUserService extends BaseService<SysUserEntity> {


    List<SysUserDTO> getSysUserByIdCardByName(Map<String,Object> params);


}
