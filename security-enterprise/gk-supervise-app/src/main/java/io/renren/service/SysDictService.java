/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.service;

import io.renren.common.service.BaseService;
import io.renren.common.utils.Result;
import io.renren.entity.SysDeptEntity;
import io.renren.entity.SysDictEntity;

/**
 * 码表管理
 * 
 * <AUTHOR> sunlight<PERSON>@gmail.com
 */
public interface SysDictService extends BaseService<SysDictEntity> {


    Result getFullDict();

}