/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.service;

import io.renren.common.service.BaseService;
import io.renren.dto.SysUserDTO;
import io.renren.entity.UserEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 用户
 * 
 * <AUTHOR>
 */
public interface UserService extends BaseService<UserEntity> {

	UserEntity getUserByUserId(Long userId);

	/**
	 * 用户登录
	 * @param dto    登录表单
	 * @return        返回登录信息
	 */
	Map<String, Object> login(SysUserDTO dto);

	Integer updatePwd(Map<String, String> map);

	/**
	 * 安装包下载
	 * @param response
	 * @return
	 */
    void download(HttpServletResponse response);

	/**
	 * 检查更新
	 * @param
	 * @return
	 */
	int checkForUpdate(String version);
}
