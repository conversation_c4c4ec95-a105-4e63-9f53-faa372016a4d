/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.BaseServiceImpl;
import io.renren.common.utils.ConvertUtils;

import io.renren.dao.SysUserDao;
import io.renren.dto.SysUserDTO;
import io.renren.entity.SysUserEntity;
import io.renren.service.SysUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * 系统用户
 * 
 * <AUTHOR> <PERSON>@gmail.com
 */
@Service
public class SysUserServiceImpl extends BaseServiceImpl<SysUserDao, SysUserEntity> implements SysUserService {
@Autowired
private SysUserDao sysUserDao;


    @Override
    public List<SysUserDTO> getSysUserByIdCardByName(Map<String, Object> params) {
        return sysUserDao.getSysUserByIdCardByName(params);
    }


}
