/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package io.renren.service.impl;


import io.renren.common.service.impl.BaseServiceImpl;
import io.renren.common.utils.Result;
import io.renren.dao.SysDictDao;
import io.renren.dto.DictParentDTO;
import io.renren.entity.SysDictEntity;
import io.renren.service.SysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class SysDictServiceImpl extends BaseServiceImpl<SysDictDao, SysDictEntity> implements SysDictService {

    @Autowired
    private SysDictDao dictDao;

    @Override
    public Result getFullDict() {
        List<DictParentDTO> list = dictDao.getFullDict();
        return new Result().ok(list);
    }

}
