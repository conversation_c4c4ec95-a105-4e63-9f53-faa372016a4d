package io.renren.modules.enterprise.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-26
 */
@Data
@ApiModel(value = "项目管理人员信息")
public class Ps04PageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long userid;

    @ApiModelProperty(value = "人员姓名")
    private String name;

    @ApiModelProperty(value = "头像采集照片")
    private String url;

    @ApiModelProperty(value = "项目名称")
    private String proname;

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "人员类型 1工人 2 管理人员")
    private String type;



}