package io.renren.modules.qualityAndSafety.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.modules.enterprise.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 安全监督信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-04
 */
@Data
@ApiModel(value = "安全监督信息")
public class Aj13DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long aj1301;

    @ApiModelProperty(value = "项目ID", hidden = true)
    private Long pj0101;

    @ApiModelProperty(value = "报监受理部门", required = true)
    private String superDepartment;

    @ApiModelProperty(value = "报监受理人员", required = true)
    private String assignee;

    @ApiModelProperty(value = "报监受理意见", required = true)
    @NotBlank(message = "报监受理意见不能为空")
    private String acceptComment;

    @ApiModelProperty(value = "监督开始日期", required = true, example = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    @ApiModelProperty(value = "监督终止日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    @ApiModelProperty(value = "监督机构", required = true)
    private String oversightDept;

    @ApiModelProperty(value = "监督机构统一社会信用代码", required = true)
    private String corpCode;

    @ApiModelProperty(value = "企业ID", hidden = true)
    private Long cp0101;

    @ApiModelProperty(value = "问题类型")
    private String troubletype;

    @ApiModelProperty(value = "问题附件")
    private List<Ot01DTO> troubleFiles;

    @ApiModelProperty(value = "问题描述")
    private String troubleinfo;

    @ApiModelProperty(value = "整改反馈")
    private String feedback;

    @ApiModelProperty(value = "反馈附件")
    private List<Ot01DTO> feedbackFiles;

    @ApiModelProperty(value = "问题状态")
    private String status;

    @ApiModelProperty(value = "审核操作")
    private String audit;

    @ApiModelProperty(value = "项目名称")
    private String projectname;
}