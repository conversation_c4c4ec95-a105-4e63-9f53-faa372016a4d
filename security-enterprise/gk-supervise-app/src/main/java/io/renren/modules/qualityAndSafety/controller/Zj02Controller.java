package io.renren.modules.qualityAndSafety.controller;

import io.renren.annotation.Login;
import io.renren.annotation.LoginUser;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.entity.SysUserEntity;
import io.renren.entity.UserEntity;
import io.renren.modules.qualityAndSafety.dto.Zj02DTO;
import io.renren.modules.qualityAndSafety.service.Zj02Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 质量监督信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-03
 */
@RestController
@RequestMapping("supervise/zj02")
@Api(tags = "质量监督信息")
public class Zj02Controller {
    @Autowired
    private Zj02Service zj02Service;

    @Login
    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    public Result<PageData<Zj02DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params, @ApiIgnore @LoginUser UserEntity user) {
        PageData<Zj02DTO> page = zj02Service.pageList(params, user);

        return new Result<PageData<Zj02DTO>>().ok(page);
    }

    @Login
    @GetMapping("info")
    @ApiOperation("整改详情")
    public Result info(@RequestParam Long zj0201) {

        Result result = zj02Service.info(zj0201);

        return result;
    }

    @Login
    @PostMapping("save")
    @ApiOperation("整改发布")
    public Result save(@RequestBody Zj02DTO dto, @ApiIgnore @LoginUser UserEntity user) {

        Result result = zj02Service.saveInfo(dto, user);

        return result;
    }

    @Login
    @PostMapping("audit")
    @ApiOperation("整改审核")
    public Result audit(@RequestBody Zj02DTO dto, @ApiIgnore @LoginUser UserEntity user) {

        Result result = zj02Service.audit(dto, user);

        return result;
    }

}