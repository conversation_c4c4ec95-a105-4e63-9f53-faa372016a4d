package io.renren.modules.enterprise.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.dao.Kq02Dao;
import io.renren.modules.enterprise.dto.Kq02DTO;
import io.renren.modules.enterprise.dto.ProjectPersonDTO;
import io.renren.modules.enterprise.entity.Kq02Entity;
import io.renren.modules.enterprise.entity.Ps02Entity;
import io.renren.modules.enterprise.entity.Ps04Entity;
import io.renren.modules.enterprise.service.Kq02Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yang<PERSON>
 * @Date : 2021-05-26
 **/
@Service
public class Kq02ServiceImpl extends CrudServiceImpl<Kq02Dao, Kq02Entity, Kq02DTO> implements Kq02Service {

    @Autowired
    private Kq02Dao kq02Dao;

    @Override
    public QueryWrapper<Kq02Entity> getWrapper(Map<String, Object> params) {
        return null;
    }


    @Override
    public PageData<ProjectPersonDTO> getAllPersonKQByPj0101(Map<String, Object> params) {
        IPage<Kq02Entity> page = getPage(params, "", false);
        List<ProjectPersonDTO> ps02PageDTO = kq02Dao.getAllPersonKQByPj0101(params);
        return getPageData(ps02PageDTO, page.getTotal(), ProjectPersonDTO.class);
    }

    @Override
    public PageData<ProjectPersonDTO> getPs02KQByPj0101(Map<String, Object> params) {
        IPage<Kq02Entity> page = getPage(params, "", false);
        List<ProjectPersonDTO> ps02PageDTO = kq02Dao.getPs02KQByPj0101(params);
        return getPageData(ps02PageDTO, page.getTotal(), ProjectPersonDTO.class);
    }

    @Override
    public PageData<ProjectPersonDTO> getPs04KQByPj0101(Map<String, Object> params) {
        IPage<Kq02Entity> page = getPage(params, "", false);
        List<ProjectPersonDTO> ps04PageDTO = kq02Dao.getPs04KQByPj0101(params);
        return getPageData(ps04PageDTO, page.getTotal(), ProjectPersonDTO.class);
    }

    @Override
    public PageData<ProjectPersonDTO> getPersonKQByAreaCode(Map<String, Object> params) {
        IPage<Kq02Entity> page = getPage(params, "", false);
        List<ProjectPersonDTO> ps04PageDTO = kq02Dao.getPersonKQByAreaCode(params);
        return getPageData(ps04PageDTO, page.getTotal(), ProjectPersonDTO.class);
    }

    @Override
    public JSONArray getPersonKQInfo(Map<String, Object> params) {
        List<String> list = kq02Dao.getPersonKQInfo(params);
        JSONArray jsonArray = new JSONArray();
        for (String str : list) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("date", str);
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    @Override
    public List<ProjectPersonDTO> getPersonKQInfoDetail(Map<String, Object> params) {
        return kq02Dao.getPersonKQInfoDetail(params);
    }
}
