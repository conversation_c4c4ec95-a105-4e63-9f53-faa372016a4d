package io.renren.modules.enterprise.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020-7-8 08:37:20
 * 工人和管理人员信息
 */
@Data
@ApiModel(value = "工人和管理人员信息")
public class ProjectPersonDTO  implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long userid;

    @ApiModelProperty(value = "人员姓名")
    private String name;

    @ApiModelProperty(value = "图片路径")
    private String url;

    @ApiModelProperty(value = "项目名称")
    private String proname;

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "人员类型 1工人 2 管理人员")
    private String type;

    @ApiModelProperty(value = "考勤时间")
    private String checkdate;

    @ApiModelProperty(value = "所属班组")
    private String teamname;

    @ApiModelProperty(value = "所属企业")
    private String corpname;
}
