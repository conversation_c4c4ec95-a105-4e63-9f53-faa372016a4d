package io.renren.modules.qualityAndSafety.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.entity.UserEntity;
import io.renren.modules.enterprise.dao.Cp01Dao;
import io.renren.modules.enterprise.dao.Ot01Dao;
import io.renren.modules.enterprise.dto.Ot01DTO;
import io.renren.modules.enterprise.entity.Cp01Entity;
import io.renren.modules.enterprise.entity.Ot01Entity;
import io.renren.modules.qualityAndSafety.dao.Aj13Dao;
import io.renren.modules.qualityAndSafety.dto.Aj13DTO;
import io.renren.modules.qualityAndSafety.entity.Aj13Entity;
import io.renren.modules.qualityAndSafety.service.Aj13Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 安全监督信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-04
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Aj13ServiceImpl extends CrudServiceImpl<Aj13Dao, Aj13Entity, Aj13DTO> implements Aj13Service {
    @Autowired
    private Ot01Dao ot01Dao;
    @Autowired
    private Cp01Dao cp01Dao;

    @Override
    public QueryWrapper<Aj13Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<Aj13Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }

    @Override
    public PageData<Aj13DTO> pageList(Map<String, Object> params, UserEntity user) {
        IPage<Aj13Entity> page = getPage(params, null, false);
        params.put("userId", user.getId());
        List<Aj13DTO> list = baseDao.pageList(params);
        return getPageData(list, page.getTotal(), Aj13DTO.class);
    }

    @Override
    public Result info(Long aj1301) {
        Result<Object> result = new Result<>();
        Aj13DTO aj13dto = baseDao.selectInfoById(aj1301);
        //查询问题附件
        List<Ot01DTO> troubleFiles = ot01Dao.getBusinessData(aj1301, "31");
        if (troubleFiles.size() != 0) {
            aj13dto.setTroubleFiles(troubleFiles);
        }
        //查询反馈附件
        List<Ot01DTO> feedbackFiles = ot01Dao.getBusinessData(aj1301, "32");
        if (feedbackFiles.size() != 0) {
            aj13dto.setFeedbackFiles(feedbackFiles);
        }
        return result.ok(aj13dto);
    }

    @Override
    public Result saveInfo(Aj13DTO dto, UserEntity user) {
        Result<Object> result = new Result<>();
        Aj13Entity aj13 = new Aj13Entity();
        BeanUtil.copyProperties(dto, aj13, CopyOptions.create().setIgnoreNullValue(true));
        Cp01Entity cp01 = cp01Dao.selectById(dto.getCp0101());
        aj13.setCorpCode(cp01.getCorpcode());
        aj13.setOversightDept(cp01.getCorpname());
        aj13.setStartDate(new Date());
        aj13.setCreateDate(new Date());
        aj13.setCreator(user.getId());
        baseDao.insert(aj13);
        List<Ot01DTO> troubleFiles = dto.getTroubleFiles();
        for (Ot01DTO file : troubleFiles) {
            Ot01Entity ot01 = ot01Dao.selectById(file.getOt0101());
            ot01.setBusisysno(aj13.getAj1301());
            ot01Dao.updateById(ot01);
        }
        return result;
    }

    @Override
    public Result audit(Aj13DTO dto, UserEntity user) {
        Result<Object> result = new Result<>();
        String audit = dto.getAudit();
        Aj13Entity aj13 = baseDao.selectById(dto.getAj1301());
        aj13.setAcceptComment(dto.getAcceptComment());
        //驳回
        if ("2".equals(audit)) {
            aj13.setStatus("3");
        } else {
            aj13.setStatus("4");
            aj13.setEndDate(new Date());
        }
        baseDao.updateById(aj13);
        return result;
    }
}