package io.renren.modules.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.ConvertUtils;
import io.renren.modules.enterprise.dao.Tm01Dao;
import io.renren.modules.enterprise.dto.Tm01DTO;
import io.renren.modules.enterprise.dto.Tm01PersonDTO;
import io.renren.modules.enterprise.entity.Tm01Entity;
import io.renren.modules.enterprise.service.Tm01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Tm01ServiceImpl extends CrudServiceImpl<Tm01Dao, Tm01Entity, Tm01DTO> implements Tm01Service {

    @Autowired
    private Tm01Dao tm01Dao;

    @Override
    public QueryWrapper<Tm01Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<Tm01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Tm01DTO> pageList(Map<String, Object> params) {
        paramsToLike(params, "teamname");
        IPage<Tm01Entity> page = getPage(params, "", false);
        List<Tm01DTO> list = baseDao.getList(params);
        return getPageData(list, page.getTotal(), Tm01DTO.class);
    }

    @Override
    public Tm01DTO getTm01Info(Long tm0101) {
        Tm01Entity bTm01Entity = tm01Dao.selectById(tm0101);
        Tm01DTO bTm01DTO = ConvertUtils.sourceToTarget(bTm01Entity, Tm01DTO.class);
        return bTm01DTO;
    }

    @Override
    public PageData<Tm01PersonDTO> getTm01PersonInfo(Map<String, Object> params) {
        IPage<Tm01Entity> page = getPage(params, "", false);
        List<Tm01PersonDTO> list = tm01Dao.getTm01PersonInfo(params);
        return getPageData(list, page.getTotal(), Tm01PersonDTO.class);
    }

}