package io.renren.modules.enterprise.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 考勤数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_KQ02")
public class Kq02Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long kq0201;
    /**
     * 人员ID
     */
    private Long userId;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 人员姓名
     */
    private String personName;
    /**
     * 设备序列号
     */
    private String deviceserialno;
    /**
     * 考勤时间
     */
    private Date checkdate;
    /**
     * 人员类型
     */
    private String personType;
    /**
     * 进出方向
     */
    private String direction;
    /**
     * 通行方式
     */
    private String attendtype;
    /**
     * 经度
     */
    private BigDecimal lng;
    /**
     * 纬度
     */
    private BigDecimal lat;
    /**
     * 刷卡近照
     */
    private String imageUrl;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}