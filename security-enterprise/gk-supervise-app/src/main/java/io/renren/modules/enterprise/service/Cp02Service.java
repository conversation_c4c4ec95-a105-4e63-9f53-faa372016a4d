package io.renren.modules.enterprise.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.dto.Cp01DTO;
import io.renren.modules.enterprise.dto.Cp02DTO;
import io.renren.modules.enterprise.entity.Cp02Entity;

import java.util.Map;

/**
 * 参建单位信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
public interface Cp02Service extends CrudService<Cp02Entity, Cp02DTO> {
    /**
     * 功能描述: <br> 参建单位列表分页查询 <br/>
     * 创建时间:  2020-06-29 8:56
     *
     * @param params params
     * @return PageData<Cp02PageDTO>
     * <AUTHOR>
     */
    PageData<Cp02DTO> pageList(Map<String, Object> params);


    Cp01DTO getDetail(Long cp0201);

    Result getCompanySelect(String pj0101);
}