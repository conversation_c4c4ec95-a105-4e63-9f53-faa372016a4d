package io.renren.modules.enterprise.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.ConvertUtils;
import io.renren.modules.enterprise.dao.Ps03Dao;
import io.renren.modules.enterprise.dto.Ot01DTO;
import io.renren.modules.enterprise.dto.Ps03DTO;
import io.renren.modules.enterprise.entity.Ps03Entity;
import io.renren.modules.enterprise.service.Ps03Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 建筑工人合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Ps03ServiceImpl extends CrudServiceImpl<Ps03Dao, Ps03Entity, Ps03DTO> implements Ps03Service {

    @Autowired
    private Ps03Dao ps03Dao;


    @Override
    public Ps03DTO getPs03Info(Long ps0201) {
        Ps03DTO ps03DTO = ps03Dao.getPs0301ByPs0201(ps0201);
        if (!ObjectUtil.isEmpty(ps03DTO)) {
            List<Ot01DTO> list = ps03Dao.getOt01ByPs0201(ps03DTO.getPs0301());
            ps03DTO.setOt01DTO(list);
        }
        return ps03DTO;
    }

    @Override
    public QueryWrapper<Ps03Entity> getWrapper(Map<String, Object> params) {
        return null;
    }
}