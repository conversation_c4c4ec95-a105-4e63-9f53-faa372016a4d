package io.renren.modules.enterprise.controller;

import io.renren.annotation.Login;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.dto.Tm01DTO;
import io.renren.modules.enterprise.dto.Tm01PersonDTO;
import io.renren.modules.enterprise.service.Tm01Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;


/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@RestController
@RequestMapping("/supervise/tm01")
@Api(tags = "班组基础信息")
public class Tm01Controller {
    @Autowired
    private Tm01Service tm01Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name ="pj0101", value = "班组id", paramType = "query", dataType = "String",required = true),
            @ApiImplicitParam(name ="teamname", value = "班组名称", paramType = "query", dataType = "String")
    })
    public Result<PageData<Tm01DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Tm01DTO> page = tm01Service.pageList(params);
        return new Result<PageData<Tm01DTO>>().ok(page);
    }

    @Login
    @GetMapping("getTm01Info")
    @ApiOperation("班组详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name ="tm0101", value = "班组id)", paramType = "query", dataType = "String",required = true)
    })
    public Result<Tm01DTO> getTm01Info(@RequestParam Map<String, Object> params) {

        String tm0101 = (String) params.get("tm0101");

        Tm01DTO tm01Info = tm01Service.getTm01Info(Long.valueOf(tm0101));

        return new Result<Tm01DTO>().ok(tm01Info);
    }

    @Login
    @GetMapping("getTm01PersonInfo")
    @ApiOperation("班组下人员情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name ="tm0101", value = "班组id)", paramType = "query", dataType = "String",required = true),
            @ApiImplicitParam(name ="name", value = "名字", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name ="inorout", value = "进退场状态 1 进场 2退场 空值所有", paramType = "query", dataType = "String"),
    })
    public Result<PageData<Tm01PersonDTO>> getTm01PersonInfo(@RequestParam Map<String, Object> params) {

        PageData<Tm01PersonDTO> tm01Info = tm01Service.getTm01PersonInfo(params);

        return new Result<PageData<Tm01PersonDTO>>().ok(tm01Info);
    }

}