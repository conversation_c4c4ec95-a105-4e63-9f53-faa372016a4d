package io.renren.modules.enterprise.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工人管理人员信息
 *
 * <AUTHOR> yanghao
 * @Date : 2021-04-26
 **/
@Data
@ApiModel(value = "在建项目列表")
public class PersonDTO {

    /**
     * pj0101
     */
    @ApiModelProperty(value = "pj0101")
    private String pj0101;

    /**
     * 所属区域
     */
    @ApiModelProperty(value = "所属区域")
    private String areaname;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String name;

    /**
     * 工人数量
     */
    @ApiModelProperty(value = "工人数量")
    private String ps02num;


    /**
     * 管理人员数量
     */
    @ApiModelProperty(value = "管理人员数量")
    private String ps04num;

}
