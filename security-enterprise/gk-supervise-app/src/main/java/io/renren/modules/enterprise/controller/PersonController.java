package io.renren.modules.enterprise.controller;

import io.renren.annotation.Login;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.common.dao.Pj01Dao;
import io.renren.modules.enterprise.dto.BuildDTO;
import io.renren.modules.enterprise.dto.HomeDTO;
import io.renren.modules.enterprise.dto.ProjectPersonDTO;
import io.renren.modules.enterprise.dto.Ps02PageDTO;
import io.renren.modules.enterprise.service.Ps02Service;
import io.renren.modules.enterprise.service.Ps04Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * 工人 和管理人员 信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-03-30
 */
@RestController
@RequestMapping("supervise/person")
@Api(tags = "今日在场工人和管理人员信息")
public class PersonController {

    @Autowired
    private Ps02Service ps02Service;

    @Autowired
    private Ps04Service ps04Service;


    @Login
    @GetMapping("getPersonInfoByPj0101")
    @ApiOperation("项目下工人和管理人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "pj0101", value = "项目id", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "1工人 2 管理人员  空值 所有人员", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "人员名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "date", value = "日期 yyyyMMdd", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "inorout", value = "进退场状态 1 进场 2 退场 空值 所有", paramType = "query", required = true, dataType = "String"),
    })
    public Result<PageData<ProjectPersonDTO>> getPersonInfoByPj0101(@RequestParam Map<String, Object> params) {
        PageData<ProjectPersonDTO> list;
        if (StringUtils.isEmpty(params.get("type").toString())) {
            //所有人员信息
            list = ps02Service.getAllPersonByPj0101(params);
            return new Result<PageData<ProjectPersonDTO>>().ok(list);
        }
        Integer type = Integer.parseInt(params.get("type").toString());

        if (1 == type) {//工人信息
            list = ps02Service.getPs02ByPj0101(params);
        } else {//管理人员信息
            list = ps04Service.getPs04ByPj0101(params);
        }
        return new Result<PageData<ProjectPersonDTO>>().ok(list);
    }

    @Login
    @GetMapping("getPersonByAreaCode")
    @ApiOperation("区县下工人和管理人员信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "areacode", value = "区县编码", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "1工人 2 管理人员 空值 所有人员", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "人员名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "inorout", value = "进退场状态 1 进场 2 退场 空值 所有", paramType = "query", required = true, dataType = "String"),
    })
    public Result<PageData<ProjectPersonDTO>> getPersonByAreaCode(@RequestParam Map<String, Object> params) {
        PageData<ProjectPersonDTO> list = ps02Service.getPersonByAreaCode(params);
        return new Result<PageData<ProjectPersonDTO>>().ok(list);
    }


}