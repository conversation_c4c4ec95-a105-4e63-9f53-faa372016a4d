package io.renren.modules.enterprise.controller;

import io.renren.annotation.Login;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.dto.Ps03DTO;
import io.renren.modules.enterprise.service.Ps03Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-06-01
 */
@RestController
@RequestMapping("/supervise/ps03")
@Api(tags = "合同信息")
public class Ps03Controller {
    @Autowired
    private Ps03Service ps03Service;

    @Login
    @GetMapping("getPs03Info")
    @ApiOperation("工人合同信息")
    public Result<Ps03DTO> getPs03Info(@RequestParam("ps0201") String ps0201) {
        Ps03DTO data = ps03Service.getPs03Info(Long.valueOf(ps0201));
        return new Result<Ps03DTO>().ok(data);
    }


}