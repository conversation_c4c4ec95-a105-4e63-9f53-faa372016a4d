package io.renren.modules.enterprise.controller;

import io.renren.common.exception.ErrorCode;
import io.renren.common.exception.RenException;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.dto.Ot01DTO;
import io.renren.modules.enterprise.service.Ot01Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
 * 附件数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-05-21
 */
@RestController
@RequestMapping("/supervise/ot01")
@Api(tags = "附件数据表")
public class Ot01Controller {
    @Autowired
    private Ot01Service ot01Service;


    @PostMapping("upload")
    @ApiOperation("文件上传")
    public Result<Ot01DTO> upload(@RequestParam("file") MultipartFile file, @RequestParam("busiType") String busiType) {
        if (file.isEmpty()) {
            throw new RenException(ErrorCode.UPLOAD_FILE_EMPTY);
        }
        Ot01DTO ot01dto = ot01Service.upload(file, busiType);
        return new Result<Ot01DTO>().ok(ot01dto);
    }
}