package io.renren.modules.enterprise.service;

import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.dto.Ot01DTO;
import io.renren.modules.enterprise.entity.Ot01Entity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 附件数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-10
 */
public interface Ot01Service extends CrudService<Ot01Entity, Ot01DTO> {

    /**
     * 根据附件id  加载图片
     *
     * @param id
     * @param response
     */
    void loadFileByid(String id, HttpServletResponse response) throws IOException;

    /**
     * 文件上传
     *
     * @param file
     * @return
     */
    Ot01DTO upload(MultipartFile file, String busiType);


}