package io.renren.modules.enterprise.controller;

import io.renren.annotation.Login;
import io.renren.common.utils.Result;
import io.renren.modules.common.dao.Pj01Dao;
import io.renren.modules.enterprise.dto.HomeDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 实名制首页
 *
 * <AUTHOR>
 * @since 1.0.0 2021-03-30
 */
@RestController
@RequestMapping("supervise/home")
@Api(tags = "实名制首页")
public class HomeController {

    @Autowired
    private Pj01Dao pj01Dao;


    @Login
    @GetMapping("getTotal")
    @ApiOperation("实名制首页统计信息")
    public Result<List<HomeDTO>> getTotal(@RequestParam("deptId") String deptId) {
        Long id = Long.valueOf(deptId);
        List<HomeDTO> total = pj01Dao.getTotal(id);
        return new Result<List<HomeDTO>>().ok(total);
    }


}