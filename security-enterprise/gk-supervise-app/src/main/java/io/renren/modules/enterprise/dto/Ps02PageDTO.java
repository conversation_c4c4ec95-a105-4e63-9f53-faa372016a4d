package io.renren.modules.enterprise.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020-7-8 08:37:20
 * 工人列表
 */
@Data
@ApiModel(value = "建筑工人分页列表信息")
public class Ps02PageDTO implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long userid;

    @ApiModelProperty(value = "人员姓名")
    private String name;

    @ApiModelProperty(value = "头像采集照片")
    private String url;

    @ApiModelProperty(value = "项目名称")
    private String proname;

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "人员类型 1工人 2 管理人员")
    private String type;
}
