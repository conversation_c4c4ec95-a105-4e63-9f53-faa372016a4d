package io.renren.modules.enterprise.controller;

import io.renren.annotation.Login;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.dto.ProjectPersonDTO;
import io.renren.modules.enterprise.dto.Ps02DTO;
import io.renren.modules.enterprise.dto.Ps04DTO;
import io.renren.modules.enterprise.service.Ps02Service;
import io.renren.modules.enterprise.service.Ps04Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@RestController
@RequestMapping("supervise/ps04")
@Api(tags = "管理人员信息")
public class Ps04Controller {

    @Autowired
    private Ps04Service ps04Service;


    @GetMapping("page")
    @ApiOperation("项目详情下管理人员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pj0101", value = "项目id", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "管理人员名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "cp0201", value = "所属企业", paramType = "query", dataType = "String"),
    })
    public Result<PageData<ProjectPersonDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<ProjectPersonDTO> page = ps04Service.pageList(params);
        return new Result<PageData<ProjectPersonDTO>>().ok(page);
    }

    @Login
    @GetMapping("getPs04Detail")
    @ApiOperation("管理人员信息详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ps0401", value = "人员id", paramType = "query", required = true, dataType = "String"),
    })
    public Result<Ps04DTO> getPs04Detail(@RequestParam("ps0401") String ps0401) {
        Ps04DTO ps04Detail = ps04Service.getPs04Detail(Long.valueOf(ps0401));
        return new Result<Ps04DTO>().ok(ps04Detail);
    }


}