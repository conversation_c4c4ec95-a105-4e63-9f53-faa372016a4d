package io.renren.modules.enterprise.controller;

import com.alibaba.fastjson.JSONArray;
import io.renren.annotation.Login;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.dto.ProjectPersonDTO;
import io.renren.modules.enterprise.service.Kq02Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;


/**
 * 考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-03-30
 */
@RestController
@RequestMapping("supervise/kq02")
@Api(tags = "考勤信息")
public class Kq02Controller {

    @Autowired
    private Kq02Service kq02Service;


    @Login
    @GetMapping("getPersonKQInfoByPj0101")
    @ApiOperation("项目下工人和管理人员考勤信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "pj0101", value = "项目id", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "1工人 2 管理人员", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "name", value = "人员名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "dayDate", value = "时间(yyyy-MM-dd)", paramType = "query", required = true, dataType = "String"),
    })
    public Result<PageData<ProjectPersonDTO>> getPersonKQInfoByPj0101(@RequestParam Map<String, Object> params) {
        PageData<ProjectPersonDTO> list = kq02Service.getAllPersonKQByPj0101(params);
        return new Result<PageData<ProjectPersonDTO>>().ok(list);
    }

    @Login
    @GetMapping("getPersonKQByAreaCode")
    @ApiOperation("区县下工人和管理人员考勤信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "areacode", value = "区县编码", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "type", value = "1工人 2 管理人员  空值 所有人员", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "人员名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "startdate", value = "日期 yyyyMMdd", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "enddate", value = "日期 yyyyMMdd", paramType = "query", required = true, dataType = "String"),
    })
    public Result<PageData<ProjectPersonDTO>> getPersonKQByAreaCode(@RequestParam Map<String, Object> params) {
        PageData<ProjectPersonDTO> list = kq02Service.getPersonKQByAreaCode(params);
        return new Result<PageData<ProjectPersonDTO>>().ok(list);
    }


    @Login
    @GetMapping("getPersonKQInfo")
    @ApiOperation("获取人员考勤记录信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "checkdate", value = "考勤时间,格式为yyyymm", paramType = "query", required = true, dataType = "string"),
            @ApiImplicitParam(name = "userId", value = "工人(管理人员)Id", paramType = "query", required = true, dataType = "string"),
            @ApiImplicitParam(name = "pj0101", value = "项目id", paramType = "query", required = true, dataType = "string"),
    })
    public Result<JSONArray> getPersonKQInfo(@ApiIgnore @RequestParam Map<String, Object> params) {

        JSONArray list = kq02Service.getPersonKQInfo(params);

        return new Result<JSONArray>().ok(list);
    }


    @Login
    @GetMapping("getPersonKQInfoDetail")
    @ApiOperation("获取工人考勤记录信息详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "checkdate", value = "考勤时间,格式为yyyymmdd", paramType = "query", required = true, dataType = "string"),
            @ApiImplicitParam(name = "userId", value = "工人(管理人员)Id", paramType = "query", required = true, dataType = "string"),
            @ApiImplicitParam(name = "pj0101", value = "项目id", paramType = "query", required = true, dataType = "string"),
    })
    public Result<List<ProjectPersonDTO>> getPersonKQInfoDetail(@ApiIgnore @RequestParam Map<String, Object> params) {

        List<ProjectPersonDTO> list = kq02Service.getPersonKQInfoDetail(params);

        return new Result<List<ProjectPersonDTO>>().ok(list);
    }


}