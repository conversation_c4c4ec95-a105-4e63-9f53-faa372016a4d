package io.renren.modules.qualityAndSafety.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.qualityAndSafety.dto.Zj02DTO;
import io.renren.modules.qualityAndSafety.entity.Zj02Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 质量监督信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-03
 */
@Mapper
public interface Zj02Dao extends BaseDao<Zj02Entity> {
    List<Zj02DTO> pageList(Map<String, Object> params);

    Zj02DTO selectInfoById(Long zj0201);
}