package io.renren.modules.enterprise.controller;

import io.renren.annotation.Login;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.dto.ProjectPersonDTO;
import io.renren.modules.enterprise.dto.Ps02DTO;
import io.renren.modules.enterprise.service.Ps02Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@RestController
@RequestMapping("supervise/ps02")
@Api(tags = "建筑工人信息")
public class Ps02Controller {

    @Autowired
    private Ps02Service ps02Service;

    @GetMapping("page")
    @ApiOperation("项目详情下工人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pj0101", value = "项目id", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "工人名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "cp0201", value = "所属企业", paramType = "query", dataType = "String"),
    })
    public Result<PageData<ProjectPersonDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<ProjectPersonDTO> page = ps02Service.pageList(params);
        return new Result<PageData<ProjectPersonDTO>>().ok(page);
    }


    @Login
    @GetMapping("getPs02Detail")
    @ApiOperation("工人信息详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ps0201", value = "人员id", paramType = "query", required = true, dataType = "String"),
    })
    public Result<Ps02DTO> getPs02Detail(@RequestParam("ps0201") String ps0201) {
        Ps02DTO ps02Detail = ps02Service.getPs02Detail(Long.valueOf(ps0201));
        return new Result<Ps02DTO>().ok(ps02Detail);
    }


}