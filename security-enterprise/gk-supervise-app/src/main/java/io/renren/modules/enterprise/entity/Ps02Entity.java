package io.renren.modules.enterprise.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 建筑工人数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PS02")
public class Ps02Entity {
    private static final long serialVersionUID = 1L;
    @TableId

    /**
     * 主键ID
     */
    private Long ps0201;
    /**
     * 项目主键ID
     */
    private Long pj0101;

    /**
     * 班组主键ID
     */
    private Long tm0101;
    /**
     * 人员主键ID
     */
    private Long ps0101;
    /**
     * 是否班组长(dic)
     */
    private String isteamleader;
    /**
     * 工种(dic)
     */
    private String worktypecode;
    /**
     * 进场时间
     */
    private Date entrytime;
    /**
     * 退场时间
     */
    private Date exittime;

    /**
     * 制卡采集照片
     */
    //更新的时候忽略该字段的空值判断
//    @TableField(strategy = FieldStrategy.IGNORED)
    private String issuecardpicurl;

    /**
     * 发放工资银行卡号
     */
    private String payrollbankcardnumber;
    /**
     * 发放工资银行名称
     */
    private String payrollbankname;

    /**
     * 工资卡银行代码(dic)
     */
    private String payrolltopbankcode;

    /**
     * 备注
     */
    private String memo;

    /**
     * 进退场状态
     */
    private String inOrOut;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}