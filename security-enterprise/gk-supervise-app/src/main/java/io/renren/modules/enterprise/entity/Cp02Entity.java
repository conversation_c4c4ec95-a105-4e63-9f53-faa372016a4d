package io.renren.modules.enterprise.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目参建单位信息数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_CP02")

public class Cp02Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long cp0201;
    /**
     * 企业ID
     */
    private Long cp0101;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 参建类型
     */
    private String corptype;
    /**
     * 进场时间
     */
    private Date entrytime;
    /**
     * 退场时间
     */
    private Date exittime;
    /**
     * 进退场状态
     */
    private String inOrOut;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}