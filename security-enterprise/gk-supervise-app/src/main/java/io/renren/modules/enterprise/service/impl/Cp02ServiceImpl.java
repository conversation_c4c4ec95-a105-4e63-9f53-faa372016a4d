package io.renren.modules.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.common.dto.SysDictDTO;
import io.renren.modules.enterprise.dao.Cp02Dao;
import io.renren.modules.enterprise.dto.Cp01DTO;
import io.renren.modules.enterprise.dto.Cp02DTO;
import io.renren.modules.enterprise.entity.Cp02Entity;
import io.renren.modules.enterprise.service.Cp02Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 参建单位信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Cp02ServiceImpl extends CrudServiceImpl<Cp02Dao, Cp02Entity, Cp02DTO> implements Cp02Service {

    @Autowired
    private Cp02Dao bCp02Dao;

    @Override
    public QueryWrapper<Cp02Entity> getWrapper(Map<String, Object> params) {
        return null;
    }

    @Override
    public PageData<Cp02DTO> pageList(Map<String, Object> params) {
        IPage<Cp02Entity> page = getPage(params, "", false);
        List<Cp02DTO> kqPageData = bCp02Dao.getList(params);
        return getPageData(kqPageData, page.getTotal(), Cp02DTO.class);
    }

    @Override
    public Cp01DTO getDetail(Long cp0201) {
        Cp01DTO bCp01DTO = bCp02Dao.getDetail(cp0201);
        return bCp01DTO;
    }

    @Override
    public Result getCompanySelect(String pj0101) {
        Result<Object> result = new Result<>();
        List<SysDictDTO> list = baseDao.getCompanySelect(pj0101);
        return result.ok(list);
    }
}