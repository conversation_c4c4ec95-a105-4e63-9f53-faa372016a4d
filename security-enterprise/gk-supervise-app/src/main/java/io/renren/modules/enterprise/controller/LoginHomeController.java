package io.renren.modules.enterprise.controller;

import io.renren.annotation.Login;
import io.renren.common.utils.Result;
import io.renren.modules.common.dao.Pj01Dao;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 实名制登陆首页
 *
 * <AUTHOR>
 * @since 1.0.0 2021-03-30
 */
@RestController
@RequestMapping("supervise/loginHome")
@Api(tags = "实名制登陆首页")
public class LoginHomeController {

    @Autowired
    private Pj01Dao pj01Dao;


    @Login
    @GetMapping("getTotal")
    @ApiOperation("登陆首页实名制在建项目信息")
    public Result<Integer> getTotal(@RequestParam("deptId") String deptId) {
        Long id = Long.valueOf(deptId);
        Integer total = pj01Dao.getTotalPj01ByDeptId(id);
        return new Result<Integer>().ok(total);
    }

    @Login
    @GetMapping("addInfo")
    @ApiOperation("登陆首页实名制本月新增项目信息")
    public Result<Integer> addInfo(@RequestParam("deptId") String deptId) {
        Long id = Long.valueOf(deptId);
        Integer addPj01 = pj01Dao.addInfo(id);
        return new Result<Integer>().ok(addPj01);
    }

    @Login
    @GetMapping("getTodayNumber")
    @ApiOperation("登陆首页实名制今日在场人数信息")
    public Result<Integer> getTodayNumber(@RequestParam("deptId") String deptId) {
        Long id = Long.valueOf(deptId);
        Integer total = pj01Dao.getTodayNumber(id);
        return new Result<Integer>().ok(total);
    }

    @Login
    @GetMapping("getTodayKQNumber")
    @ApiOperation("登陆首页实名制今日考勤人数信息")
    public Result<Integer> getTodayKQNumber(@RequestParam("deptId") String deptId) {
        Long id = Long.valueOf(deptId);
        Integer todayNumber = pj01Dao.getTodayKQNumber(id);
        return new Result<Integer>().ok(todayNumber);
    }

    @Login
    @GetMapping("getProjectNum")
    @ApiOperation("登陆首页实名制无关键岗位项目信息")
    public Result<Integer> getProjectNum(@RequestParam("deptId") String deptId) {
        Long id = Long.valueOf(deptId);
        Integer noJobType = pj01Dao.getProjectNum(id);
        return new Result<Integer>().ok(noJobType);
    }

    @Login
    @GetMapping("getNotKQProjectNum")
    @ApiOperation("登陆首页实名制今日无考勤项目信息")
    public Result<Integer> getNotKQProjectNum(@RequestParam("deptId") String deptId) {
        Long id = Long.valueOf(deptId);
        Integer notKQ = pj01Dao.getNotKQProjectNum(id);
        return new Result<Integer>().ok(notKQ);
    }


}