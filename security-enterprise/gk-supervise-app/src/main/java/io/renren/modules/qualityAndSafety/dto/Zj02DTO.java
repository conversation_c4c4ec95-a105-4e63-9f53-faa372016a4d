package io.renren.modules.qualityAndSafety.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.iscreditcodevalidator.IsCreditCodeValidator;
import io.renren.modules.enterprise.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 质量监督信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-03
 */
@Data
@ApiModel(value = "质量监督信息")
public class Zj02DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long zj0201;

    @ApiModelProperty(value = "项目ID", hidden = true)
    private Long pj0101;

    @ApiModelProperty(value = "质量报监受理部门", required = true)
    @NotBlank(message = "质量报监受理部门不能为空")
    private String superDepartment;

    @ApiModelProperty(value = "质量报监受理人员", required = true)
    @NotBlank(message = "质量报监受理人员不能为空")
    private String receptionist;

    @ApiModelProperty(value = "质量报监受理意见")
    private String acceptComment;

    @ApiModelProperty(value = "监督开始日期",example = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    @ApiModelProperty(value = "监督终止日期",example = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    @ApiModelProperty(value = "监督机构", required = true)
    @NotBlank(message = "监督机构不能为空")
    private String oversightDept;

    @ApiModelProperty(value = "监督机构统一社会信用代码", required = true)
    @NotBlank(message = "监督机构统一社会信用代码不能为空")
    @IsCreditCodeValidator
    private String corpCode;

    @ApiModelProperty(value = "企业ID", hidden = true)
    private Long cp0101;

    @ApiModelProperty(value = "问题类型")
    private String troubletype;

    @ApiModelProperty(value = "问题附件")
    private List<Ot01DTO> troubleFiles;

    @ApiModelProperty(value = "问题描述")
    private String troubleinfo;

    @ApiModelProperty(value = "整改反馈")
    private String feedback;

    @ApiModelProperty(value = "反馈附件")
    private List<Ot01DTO> feedbackFiles;

    @ApiModelProperty(value = "问题状态")
    private String status;

    @ApiModelProperty(value = "审核操作")
    private String audit;

    @ApiModelProperty(value = "项目名称")
    private String projectname;

}