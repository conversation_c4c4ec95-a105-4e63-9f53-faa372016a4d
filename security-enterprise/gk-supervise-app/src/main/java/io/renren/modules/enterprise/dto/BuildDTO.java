package io.renren.modules.enterprise.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 在建项目列表
 *
 * <AUTHOR> yang<PERSON>
 * @Date : 2021-04-26
 **/
@Data
@ApiModel(value = "在建项目列表")
public class BuildDTO {

    /**
     * pj0101
     */
    @ApiModelProperty(value = "pj0101")
    private String pj0101;

    /**
     * 所属区域
     */
    @ApiModelProperty(value = "所属区域")
    private String areaname;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String name;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String linkman;


    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String linkphone;


    /**
     * 最新考勤时间
     */
    @ApiModelProperty(value = "最新考勤时间")
    private Date lastkqdate;

}
