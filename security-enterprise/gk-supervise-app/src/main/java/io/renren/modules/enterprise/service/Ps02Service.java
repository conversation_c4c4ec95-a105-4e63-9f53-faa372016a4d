package io.renren.modules.enterprise.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.dto.ProjectPersonDTO;
import io.renren.modules.enterprise.dto.Ps02DTO;
import io.renren.modules.enterprise.dto.Ps02PageDTO;
import io.renren.modules.enterprise.entity.Ps02Entity;

import java.util.Map;

/**
 * 建筑工人数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-05
 */
public interface Ps02Service extends CrudService<Ps02Entity, Ps02PageDTO> {
    /**
     * 列表查询数据
     *
     * @param params
     * @return
     */
    PageData<ProjectPersonDTO> pageList(Map<String, Object> params);

    PageData<ProjectPersonDTO> getPs02ByPj0101(Map<String, Object> params);

    PageData<ProjectPersonDTO> getAllPersonByPj0101(Map<String, Object> params);

    PageData<ProjectPersonDTO> getPersonByAreaCode(Map<String, Object> params);

    Ps02DTO getPs02Detail(Long ps0201);


}