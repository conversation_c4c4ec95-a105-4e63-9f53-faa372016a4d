package io.renren.modules.enterprise.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.file.FilesUploadUtil;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.dao.Ot01Dao;
import io.renren.modules.enterprise.dto.Ot01DTO;
import io.renren.modules.enterprise.entity.Ot01Entity;
import io.renren.modules.enterprise.service.Ot01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Map;

/**
 * 附件数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-10
 */
@Service
@Transactional
public class Ot01ServiceImpl extends CrudServiceImpl<Ot01Dao, Ot01Entity, Ot01DTO> implements Ot01Service {

    @Override
    public QueryWrapper<Ot01Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Ot01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    /**
     * 根据附件id  加载图片
     *
     * @param id
     * @param response
     * @throws IOException
     */
    @Override
    public void loadFileByid(String id, HttpServletResponse response) throws IOException {
        if (id == null || "undefined".equals(id) || "".equals(id)) {
            return;
        }
        Ot01DTO data = this.get(Long.valueOf(id));
        if (data.getUrl() == null) {
            return;
        }
        File imageFile = new File(data.getUrl());
        if (!imageFile.exists()) { //判断图片是否存在，不存在就结束加载
            return;
        }
        FileInputStream inputStream = new FileInputStream(imageFile);
        int i = inputStream.available();
        //byte数组用于存放图片字节数据
        byte[] buff = new byte[i];
        inputStream.read(buff);
        //记得关闭输入流
        inputStream.close();
        //设置发送到客户端的响应内容类型
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html");
        response.setHeader("Content-Disposition", "attachment;filename=\"" + URLEncoder.encode(data.getName(), "UTF-8"));
        OutputStream out = response.getOutputStream();
        out.write(buff);
        //关闭响应输出流
        out.close();
    }

    @Override
    public Ot01DTO upload(MultipartFile file, String busiType) {
        String saveFileName = FilesUploadUtil.uploadFile(file, "");
        Ot01Entity ot01Entity = new Ot01Entity();
        ot01Entity.setName(FileUtil.getName(saveFileName));
        ot01Entity.setBusitype(busiType);
        ot01Entity.setUrl(saveFileName);
        ot01Entity.setOriginalName(file.getOriginalFilename());
        ot01Entity.setWhether("1");
        baseDao.insert(ot01Entity);
        Ot01DTO dto = new Ot01DTO();
        BeanUtil.copyProperties(ot01Entity, dto, CopyOptions.create().setIgnoreNullValue(true));
        return dto;
    }

}