package io.renren.modules.enterprise.service;

import com.alibaba.fastjson.JSONArray;
import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.dto.Kq02DTO;
import io.renren.modules.enterprise.dto.ProjectPersonDTO;
import io.renren.modules.enterprise.entity.Kq02Entity;

import java.util.List;
import java.util.Map;


/**
 * 考勤数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-09
 */
public interface Kq02Service extends CrudService<Kq02Entity, Kq02DTO> {


    PageData<ProjectPersonDTO> getAllPersonKQByPj0101(Map<String, Object> params);

    PageData<ProjectPersonDTO> getPs02KQByPj0101(Map<String, Object> params);

    PageData<ProjectPersonDTO> getPs04KQByPj0101(Map<String, Object> params);

    PageData<ProjectPersonDTO> getPersonKQByAreaCode(Map<String, Object> params);

    JSONArray getPersonKQInfo(Map<String, Object> params);

    List<ProjectPersonDTO> getPersonKQInfoDetail(Map<String, Object> params);


}