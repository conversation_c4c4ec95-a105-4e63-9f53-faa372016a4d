package io.renren.modules.enterprise.service;


import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.dto.ProjectPersonDTO;
import io.renren.modules.enterprise.dto.Ps04DTO;
import io.renren.modules.enterprise.dto.Ps04PageDTO;
import io.renren.modules.enterprise.entity.Ps04Entity;

import java.util.Map;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-06-01
 */
public interface Ps04Service extends CrudService<Ps04Entity, Ps04PageDTO> {


    PageData<ProjectPersonDTO> getPs04ByPj0101(Map<String, Object> params);

    Ps04DTO getPs04Detail(Long ps0401);

    PageData<ProjectPersonDTO> pageList(Map<String, Object> params);

}