package io.renren.modules.enterprise.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 建筑工人合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "建筑工人合同信息")
public class Ps03DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long ps0301;

    @ApiModelProperty(value = "合同编号", required = true)
    private String contractcode;

    @ApiModelProperty(value = "合同期限类型", required = true)
    private String contractperiodtype;

    @ApiModelProperty(value = "签订日期", required = true)
    private Date signdate;

    @ApiModelProperty(value = "开始日期", required = true)
    private Date startdate;

    @ApiModelProperty(value = "结束时期", required = true)
    private Date enddate;

    @ApiModelProperty(value = "合同信息")
    private List<Ot01DTO> Ot01DTO;

}