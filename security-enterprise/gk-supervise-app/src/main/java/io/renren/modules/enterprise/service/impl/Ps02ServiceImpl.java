package io.renren.modules.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.dao.Ps02Dao;
import io.renren.modules.enterprise.dto.ProjectPersonDTO;
import io.renren.modules.enterprise.dto.Ps02DTO;
import io.renren.modules.enterprise.dto.Ps02PageDTO;
import io.renren.modules.enterprise.entity.Ps02Entity;
import io.renren.modules.enterprise.service.Ps02Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;


/**
 * 建筑工人数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-05
 */
@Service
@Transactional
public class Ps02ServiceImpl extends CrudServiceImpl<Ps02Dao, Ps02Entity, Ps02PageDTO> implements Ps02Service {

    @Autowired
    private Ps02Dao ps02Dao;

    @Override
    public QueryWrapper<Ps02Entity> getWrapper(Map<String, Object> params) {
        return null;
    }

    @Override
    public PageData<ProjectPersonDTO> pageList(Map<String, Object> params) {
        paramsToLike(params, "name");
        IPage<Ps02Entity> page = getPage(params, "", false);
        List<ProjectPersonDTO> list = ps02Dao.getListData(params);
        return getPageData(list, page.getTotal(), ProjectPersonDTO.class);
    }

    @Override
    public PageData<ProjectPersonDTO> getPs02ByPj0101(Map<String, Object> params) {
        IPage<Ps02Entity> page = getPage(params, "", false);
        List<ProjectPersonDTO> ps02PageDTO = ps02Dao.getPs02ByPj0101(params);
        return getPageData(ps02PageDTO, page.getTotal(), ProjectPersonDTO.class);
    }

    @Override
    public PageData<ProjectPersonDTO> getAllPersonByPj0101(Map<String, Object> params) {
        IPage<Ps02Entity> page = getPage(params, "", false);
        List<ProjectPersonDTO> ps02PageDTO = ps02Dao.getAllPersonByPj0101(params);
        return getPageData(ps02PageDTO, page.getTotal(), ProjectPersonDTO.class);
    }

    @Override
    public PageData<ProjectPersonDTO> getPersonByAreaCode(Map<String, Object> params) {
        IPage<Ps02Entity> page = getPage(params, "", false);
        List<ProjectPersonDTO> ps02PageDTO = ps02Dao.getPersonByAreaCode(params);
        return getPageData(ps02PageDTO, page.getTotal(), ProjectPersonDTO.class);
    }

    @Override
    public Ps02DTO getPs02Detail(Long ps0201) {
        return ps02Dao.getPs02Detail(ps0201);
    }
}