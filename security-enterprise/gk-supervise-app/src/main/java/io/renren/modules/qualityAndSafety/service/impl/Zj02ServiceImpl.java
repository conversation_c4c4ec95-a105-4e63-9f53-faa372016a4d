package io.renren.modules.qualityAndSafety.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.entity.SysUserEntity;
import io.renren.entity.UserEntity;
import io.renren.modules.enterprise.dao.Cp01Dao;
import io.renren.modules.enterprise.dao.Ot01Dao;
import io.renren.modules.enterprise.dto.Ot01DTO;
import io.renren.modules.enterprise.entity.Cp01Entity;
import io.renren.modules.enterprise.entity.Ot01Entity;
import io.renren.modules.qualityAndSafety.dao.Zj02Dao;
import io.renren.modules.qualityAndSafety.dto.Zj02DTO;
import io.renren.modules.qualityAndSafety.entity.Zj02Entity;
import io.renren.modules.qualityAndSafety.service.Zj02Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 质量监督信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-03
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Zj02ServiceImpl extends CrudServiceImpl<Zj02Dao, Zj02Entity, Zj02DTO> implements Zj02Service {
    @Autowired
    private Ot01Dao ot01Dao;
    @Autowired
    private Cp01Dao cp01Dao;

    @Override
    public QueryWrapper<Zj02Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<Zj02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }

    @Override
    public PageData<Zj02DTO> pageList(Map<String, Object> params, UserEntity user) {
        IPage<Zj02Entity> page = getPage(params, null, false);
        params.put("userId", user.getId());
        List<Zj02DTO> list = baseDao.pageList(params);
        return getPageData(list, page.getTotal(), Zj02DTO.class);
    }

    @Override
    public Result info(Long zj0201) {
        Result<Object> result = new Result<>();
        Zj02DTO zj02dto = baseDao.selectInfoById(zj0201);
        //查询问题附件
        List<Ot01DTO> troubleFiles = ot01Dao.getBusinessData(zj0201, "33");
        if (troubleFiles.size() != 0) {
            zj02dto.setTroubleFiles(troubleFiles);
        }
        //查询反馈附件
        List<Ot01DTO> feedbackFiles = ot01Dao.getBusinessData(zj0201, "34");
        if (feedbackFiles.size() != 0) {
            zj02dto.setFeedbackFiles(feedbackFiles);
        }
        return result.ok(zj02dto);
    }

    @Override
    public Result saveInfo(Zj02DTO dto, UserEntity user) {
        Result<Object> result = new Result<>();
        Zj02Entity zj02 = new Zj02Entity();
        BeanUtil.copyProperties(dto, zj02, CopyOptions.create().setIgnoreNullValue(true));
        Cp01Entity cp01 = cp01Dao.selectById(dto.getCp0101());
        zj02.setCorpCode(cp01.getCorpcode());
        zj02.setOversightDept(cp01.getCorpname());
        zj02.setStartDate(new Date());
        zj02.setCreateDate(new Date());
        zj02.setCreator(user.getId());
        baseDao.insert(zj02);
        List<Ot01DTO> troubleFiles = dto.getTroubleFiles();
        for (Ot01DTO file : troubleFiles) {
            Ot01Entity ot01 = ot01Dao.selectById(file.getOt0101());
            ot01.setBusisysno(zj02.getZj0201());
            ot01Dao.updateById(ot01);
        }
        return result;
    }

    @Override
    public Result audit(Zj02DTO dto, UserEntity user) {
        Result<Object> result = new Result<>();
        String audit = dto.getAudit();
        Zj02Entity zj02 = baseDao.selectById(dto.getZj0201());
        zj02.setAcceptComment(dto.getAcceptComment());
        //驳回
        if ("2".equals(audit)) {
            zj02.setStatus("3");
        } else {
            zj02.setStatus("4");
            zj02.setEndDate(new Date());
        }
        baseDao.updateById(zj02);
        return result;
    }
}