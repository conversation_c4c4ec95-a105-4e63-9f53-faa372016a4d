package io.renren.modules.qualityAndSafety.controller;

import io.renren.annotation.Login;
import io.renren.annotation.LoginUser;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.entity.UserEntity;
import io.renren.modules.qualityAndSafety.dto.Aj13DTO;
import io.renren.modules.qualityAndSafety.service.Aj13Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 安全监督信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-04
 */
@RestController
@RequestMapping("supervise/aj13")
@Api(tags = "安全监督信息")
public class Aj13Controller {
    @Autowired
    private Aj13Service aj13Service;

    @Login
    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码j，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int")
    })
    public Result<PageData<Aj13DTO>> page8(@ApiIgnore @RequestParam Map<String, Object> params, @ApiIgnore @LoginUser UserEntity user) {
        PageData<Aj13DTO> page = aj13Service.pageList(params, user);

        return new Result<PageData<Aj13DTO>>().ok(page);
    }

    @Login
    @GetMapping("info")
    @ApiOperation("整改详情")
    public Result info(@RequestParam Long aj1301) {

        Result result = aj13Service.info(aj1301);

        return result;
    }

    @Login
    @PostMapping("save")
    @ApiOperation("整改发布")
    public Result save(@RequestBody Aj13DTO dto, @ApiIgnore @LoginUser UserEntity user) {

        Result result = aj13Service.saveInfo(dto, user);

        return result;
    }

    @Login
    @PostMapping("audit")
    @ApiOperation("整改审核")
    public Result audit(@RequestBody Aj13DTO dto, @ApiIgnore @LoginUser UserEntity user) {

        Result result = aj13Service.audit(dto, user);

        return result;
    }
}