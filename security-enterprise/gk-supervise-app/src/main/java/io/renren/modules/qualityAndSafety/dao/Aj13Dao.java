package io.renren.modules.qualityAndSafety.dao;


import io.renren.common.dao.BaseDao;
import io.renren.modules.qualityAndSafety.dto.Aj13DTO;
import io.renren.modules.qualityAndSafety.entity.Aj13Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 安全监督信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-04
 */
@Mapper
public interface Aj13Dao extends BaseDao<Aj13Entity> {
    List<Aj13DTO> pageList(Map<String, Object> params);

    Aj13DTO selectInfoById(Long aj1301);
}