package io.renren.modules.qualityAndSafety.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.entity.SysUserEntity;
import io.renren.entity.UserEntity;
import io.renren.modules.qualityAndSafety.dto.Zj02DTO;
import io.renren.modules.qualityAndSafety.entity.Zj02Entity;

import java.util.Map;

/**
 * 质量监督信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-03
 */
public interface Zj02Service extends CrudService<Zj02Entity, Zj02DTO> {
    PageData<Zj02DTO> pageList(Map<String, Object> params, UserEntity user);

    Result info(Long zj0201);

    Result saveInfo(Zj02DTO dto, UserEntity user);

    Result audit(Zj02D<PERSON> dto, UserEntity user);
}