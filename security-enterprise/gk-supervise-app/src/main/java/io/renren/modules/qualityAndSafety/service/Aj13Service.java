package io.renren.modules.qualityAndSafety.service;


import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.entity.SysUserEntity;
import io.renren.entity.UserEntity;
import io.renren.modules.qualityAndSafety.dto.Aj13DTO;
import io.renren.modules.qualityAndSafety.entity.Aj13Entity;

import java.util.Map;

/**
 * 安全监督信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-04
 */
public interface Aj13Service extends CrudService<Aj13Entity, Aj13DTO> {
    /**
     * 列表查询
     * @param params
     * @param user
     * @return
     */
    PageData<Aj13DTO> pageList(Map<String, Object> params, UserEntity user);

    /**
     * 整改详情
     * @return
     */
    Result info(Long aj1301);

    /**
     * 整改发布
     * @param dto
     * @param user
     * @return
     */
    Result saveInfo(Aj13DTO dto, UserEntity user);

    /**
     * 整改审核
     * @param dto
     * @param user
     * @return
     */
    Result audit(Aj13DTO dto, UserEntity user);
}