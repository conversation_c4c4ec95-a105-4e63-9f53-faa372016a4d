package io.renren.modules.enterprise.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 班组下人员情况
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-05
 */
@Data
@ApiModel(value = "班组下人员情况")
public class Tm01PersonDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "人员id")
    private Long userid;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "电话")
    private String phone;

    //兼容其他页面
    @ApiModelProperty(value = "头像")
    private String url;

    @ApiModelProperty(value = "人员类型")
    private String type;

}