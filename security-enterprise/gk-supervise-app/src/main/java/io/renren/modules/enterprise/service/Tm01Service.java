package io.renren.modules.enterprise.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.dto.Tm01DTO;
import io.renren.modules.enterprise.dto.Tm01PersonDTO;
import io.renren.modules.enterprise.entity.Tm01Entity;

import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
public interface Tm01Service extends CrudService<Tm01Entity, Tm01DTO> {

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    PageData<Tm01DTO> pageList(Map<String, Object> params);


    /**
     * 班组详情
     *
     * @param tm0101
     * @return
     */
    Tm01DTO getTm01Info(Long tm0101);


    /**
     * 班组下人员情况
     *
     * @param
     * @return
     */
    PageData<Tm01PersonDTO> getTm01PersonInfo(Map<String, Object> params);

}