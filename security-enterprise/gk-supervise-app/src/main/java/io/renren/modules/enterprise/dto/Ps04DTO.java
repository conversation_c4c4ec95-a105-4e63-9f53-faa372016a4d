package io.renren.modules.enterprise.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "管理人员信息")
public class Ps04DTO implements Serializable {
    private static final long serialVersionUID = 1L;
    //ps01基础信息


    @ApiModelProperty(value = "项目id")
    private String pj0101 ;

    @ApiModelProperty(value = "人员姓名")
    private String name ;

    @ApiModelProperty(value = "证件类型")
    private String idcardtype ;

    @ApiModelProperty(value = "证件号码【身份证】")
    private String idcardnumber ;

    @ApiModelProperty(value = "性别")
    private String gender ;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "政治面貌")
    private String politicstype;

    @ApiModelProperty(value = "手机号码")
    private String cellphone;

    @ApiModelProperty(value = "文化程度")
    private String edulevel;

    @ApiModelProperty(value = "紧急联系人姓名")
    private String urgentlinkman;

    @ApiModelProperty(value = "紧急联系电话")
    private String urgentlinkmanphone;

    @ApiModelProperty(value = "地址")
    private String address;

    //ps04 基本数据

    @ApiModelProperty(value = "工人id")
    private String ps0401;

    @ApiModelProperty(value = "所属企业")
    private String corpname;

    @ApiModelProperty(value = "所属企业")
    private String jobtype;

    @ApiModelProperty(value = "是否购买工伤或意外伤害保险")
    private String hasbuyinsurance;

    @ApiModelProperty(value = "头像地址")
    private String url;

    @ApiModelProperty(value = "证书信息")
    private List<Ot01DTO> ot01DTOList;

}