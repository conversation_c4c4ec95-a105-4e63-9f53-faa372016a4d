package io.renren.modules.enterprise.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.dao.Ot01Dao;
import io.renren.modules.enterprise.dao.Ps04Dao;
import io.renren.modules.enterprise.dto.Ot01DTO;
import io.renren.modules.enterprise.dto.ProjectPersonDTO;
import io.renren.modules.enterprise.dto.Ps04DTO;
import io.renren.modules.enterprise.dto.Ps04PageDTO;
import io.renren.modules.enterprise.entity.Ps04Entity;
import io.renren.modules.enterprise.service.Ps04Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
@Transactional
public class Ps04ServiceImpl extends CrudServiceImpl<Ps04Dao, Ps04Entity, Ps04PageDTO> implements Ps04Service {

    @Autowired
    private Ps04Dao ps04Dao;
    @Autowired
    private Ot01Dao ot01Dao;

    @Override
    public QueryWrapper<Ps04Entity> getWrapper(Map<String, Object> params) {
        return null;
    }

    @Override
    public PageData<ProjectPersonDTO> getPs04ByPj0101(Map<String, Object> params) {
        IPage<Ps04Entity> page = getPage(params, "", false);
        List<ProjectPersonDTO> ps04PageDTO = ps04Dao.getPs04ByPj0101(params);
        return getPageData(ps04PageDTO, page.getTotal(), ProjectPersonDTO.class);
    }

    @Override
    public Ps04DTO getPs04Detail(Long ps0401) {
        Ps04DTO ps04DTO =  ps04Dao.getPs04Detail(ps0401);
        //附件信息
        List<Ot01DTO> businessData = ot01Dao.getBusinessData(ps0401, "21");
        ps04DTO.setOt01DTOList(businessData);
        return ps04DTO;
    }

    @Override
    public PageData<ProjectPersonDTO> pageList(Map<String, Object> params) {
        IPage<Ps04Entity> page = getPage(params, "", false);
        List<ProjectPersonDTO> ps04PageDTO = ps04Dao.getListData(params);
        return getPageData(ps04PageDTO, page.getTotal(), ProjectPersonDTO.class);
    }


}