package io.renren.modules.enterprise.controller;

import io.renren.annotation.Login;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.dto.Cp01DTO;
import io.renren.modules.enterprise.dto.Cp02DTO;
import io.renren.modules.enterprise.service.Cp02Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 企业信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-26
 */
@RestController
@RequestMapping("/supervise/cp02")
@Api(tags = "企业信息")
public class Cp02Controller {

    @Autowired
    private Cp02Service cp02Service;

    @Login
    @GetMapping("page")
    @ApiOperation("企业端分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pj0101", value = "项目id", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "name", value = "企业名称", paramType = "query", required = false, dataType = "int"),
    })
    public Result<PageData<Cp02DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Cp02DTO> page = cp02Service.pageList(params);

        return new Result<PageData<Cp02DTO>>().ok(page);
    }

    @Login
    @GetMapping("getDetail")
    @ApiOperation("企业详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cp0201", value = "企业id", paramType = "query", required = true, dataType = "String"),
    })
    public Result<Cp01DTO> getDetail(@ApiIgnore @RequestParam Map<String, Object> params) {
        String cp0201 = params.get("cp0201").toString();

        Cp01DTO bCp01DTO = cp02Service.getDetail(Long.valueOf(cp0201));

        return new Result<Cp01DTO>().ok(bCp01DTO);
    }

    @Login
    @GetMapping("getCompanySelect")
    @ApiOperation("参建单位下拉列表")
    public Result getCompanySelect(@RequestParam("pj0101") String pj0101) {
        Result result = cp02Service.getCompanySelect(pj0101);
        return result;
    }
}