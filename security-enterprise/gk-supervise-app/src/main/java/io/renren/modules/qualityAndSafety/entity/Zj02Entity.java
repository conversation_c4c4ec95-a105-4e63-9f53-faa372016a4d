package io.renren.modules.qualityAndSafety.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 质量监督信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ZJ_ZJ02")
public class Zj02Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long zj0201;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 质量报监受理部门
     */
    private String superDepartment;
    /**
     * 质量报监受理人员
     */
    private String receptionist;
    /**
     * 质量报监受理意见
     */
    private String acceptComment;
    /**
     * 监督开始日期
     */
    private Date startDate;
    /**
     * 监督终止日期
     */
    private Date endDate;
    /**
     * 监督机构
     */
    private String oversightDept;
    /**
     * 监督机构统一社会信用代码
     */
    private String corpCode;
    /**
     * 问题类型
     */
    private String troubletype;
    /**
     * 问题描述
     */
    private String troubleinfo;
    /**
     * 整改反馈
     */
    private String feedback;
    /**
     * 问题状态
     */
    private String status;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}