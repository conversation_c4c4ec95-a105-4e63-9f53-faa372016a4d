/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package io.renren.controller;


import io.renren.annotation.Login;
import io.renren.common.utils.Result;
import io.renren.service.SysDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公共方法接口
 *
 * @<NAME_EMAIL>
 */
@RestController
@RequestMapping("/supervise/worker/common")
@Api(tags = "公共方法接口")
public class ApiCommonController {
    @Autowired
    private SysDictService dictService;

    @Login
    @PostMapping("/getDict")
    @ApiOperation("获取码表")
    public Result getFullDict() {
        Result result = dictService.getFullDict();
        return result;
    }

}