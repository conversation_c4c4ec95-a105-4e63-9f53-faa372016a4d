/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package io.renren.controller;


import io.renren.annotation.Login;
import io.renren.common.exception.RenException;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.dto.LoginDTO;
import io.renren.dto.SysUserDTO;
import io.renren.service.TokenService;
import io.renren.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/supervise")
@Api(tags = "登录接口")
public class ApiLoginController {
    @Autowired
    private UserService userService;
    @Autowired
    private TokenService tokenService;

    @PostMapping("login")
    @ApiOperation("登录")
    public Result<Map<String, Object>> login(SysUserDTO sysUserDTO) {
        //表单校验
        ValidatorUtils.validateEntity(sysUserDTO);

        //用户登录
        Map<String, Object> map = userService.login(sysUserDTO);

        return new Result().ok(map);
    }

    @Login
    @PostMapping("checkDictVersion")
    @ApiOperation("检查码表是否需要更新")
    public Result<Map<String, Object>> checkDictVersion(LoginDTO dto) {
        Long dictVersion = tokenService.getDictVersion();
        HashMap<String, Object> map = new HashMap<>();
        map.put("dictVersion", dictVersion);
        return new Result().ok(map);
    }

    @Login
    @PostMapping("logout")
    @ApiOperation("退出")
    public Result logout(@RequestParam(value = "userId", required = false) Long userId) {
        if (userId == null || "".equals(userId)) {
            throw new RenException("用户id不能为空");
        }
        //tokenService.expireToken(userId);
        return new Result();
    }


    @PostMapping("updatePwd")
    @ApiOperation("修改密码")
    public Result updatePwd(@RequestParam Map<String, String> map) {


        Integer resule = userService.updatePwd(map);

        if (resule == 0) {
            throw new RenException("修改失败，请重试！");
        }

        return new Result().ok("1");
    }

    @Login
    @PostMapping("checkForUpdate")
    @ApiOperation("检查更新")
    public Result checkForUpdate(String version) {
        int compare = userService.checkForUpdate(version);
        return new Result().ok(compare);
    }

    @GetMapping("download")
    @ApiOperation("安装包下载")
    public void download(HttpServletResponse response) {
        userService.download(response);
    }
}