<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.crane.dao.Dv01Dao">
    <select id="devicePage" resultType="io.renren.modules.crane.dto.Dv01DTO">
        select t.*, a.name from CN_DV01 t, b_pj01 a where t.pj0101 = a.pj0101
        <if test="params.pj0101 != null and params.pj0101 != ''">
            and t.pj0101 like '%' || #{params.pj0101} || '%'
        </if>
        <if test="params.name != null and params.name != ''">
            and a.name like '%' || #{params.name} || '%'
        </if>
        <if test="params.terminaltype != null and params.terminaltype != ''">
            and t.terminaltype = #{params.terminaltype}
        </if>
        <if test="params.towernumber != null and params.towernumber != ''">
            and t.towernumber = #{params.towernumber}
        </if>
        <if test="params.status != null and params.status != ''">
            and t.status = #{params.status}
        </if>
    </select>
    <select id="infoDevice" resultType="io.renren.modules.crane.dto.Dv01DTO">
        select t.*, a.name from CN_DV01 t, b_pj01 a where t.pj0101 = a.pj0101 and t.dv0101 = #{dv0101}
    </select>
</mapper>