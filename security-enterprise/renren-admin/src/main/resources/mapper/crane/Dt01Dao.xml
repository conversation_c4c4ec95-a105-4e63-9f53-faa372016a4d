<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.crane.dao.Dt01Dao">

    <select id="currentDataPage" resultType="io.renren.modules.crane.dto.Dt01DTO">
        select t.*, a.name, b.device_no, b.devicename
          from CN_DT01 t, b_pj01 a, cn_dv01 b
         where t.pj0101 = a.pj0101
           and t.dv0101 = b.dv0101
        <if test="params.pj0101 != null and params.pj0101 != ''">
            and t.pj0101 like '%' || #{params.pj0101} || '%'
        </if>
        <if test="params.name != null and params.name != ''">
            and a.name like '%' || #{params.name} || '%'
        </if>
        <if test="params.towernumber != null and params.towernumber != ''">
            and b.towernumber = #{params.towernumber}
        </if>
    </select>
</mapper>