<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.quality.zj01.dao.Zj01Dao">
    <!--分页查询-->
    <select id="getListData" resultType="io.renren.modules.quality.zj01.dto.Zj01PageDTO">
        select b.NAME             as projectName,
               t.zj0101,
               t.SUPER_DEPARTMENT as superDepartment,
               t.RECEPTIONIS<PERSON>     as receptionist,
               t.RECEPTION_TIME   as receptionTime,
               t.OVERSIGHT_DEPT   as oversightDept,
               t.CORP_CODE        as corpCode,
               t.RECORD_NUMBER    as recordNumber
        from zj_zj01 t,
             r_pj01_dept a,
             b_pj01 b
        where t.pj0101 = a.pj0101
          and b.PJ0101 = a.PJ0101
          and a.dept_id = #{deptId}
    </select>
</mapper>