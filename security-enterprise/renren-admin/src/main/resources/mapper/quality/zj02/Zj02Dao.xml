<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.quality.zj02.dao.Zj02Dao">
    <select id="pageList" resultType="io.renren.modules.quality.zj02.dto.Zj02DTO">
        select t.*, a.name as pjname
          from ZJ_ZJ02 t, b_pj01 a
         where t.pj0101 = a.pj0101
        <if test="troubletype != null and troubletype != ''">
            and t.troubletype=#{troubletype}
        </if>
        <if test="pjname != null and pjname != ''">
            and a.name like '%'||#{pjname}||'%'
        </if>
        <if test="status != null and status != ''">
            and t.status=#{status}
        </if>
    </select>

    <select id="statistics" resultType="io.renren.modules.quality.zj02.dto.Zj02TJDTO">
        select a1.dict_label troubletype,nvl(b1.ct,0) ct
        from (select b.dict_value, b.dict_label
              from Sys_Dict_Type a, SYS_DICT_DATA b
              where a.id = b.dict_type_id
                and a.dict_type = 'ZJ_TROUBLETYPE') a1,
             (select aa.troubletype, count(aa.zj0201) ct
              from zj_zj02 aa
              group by aa.troubletype) b1
        where a1.dict_value = b1.troubletype(+)
    </select>

    <select id="getInfo" resultType="io.renren.modules.quality.zj02.dto.Zj02DTO">
        select a.*, b.name pjname
        from zj_zj02 a, b_pj01 b
        where a.pj0101 = b.pj0101
          and a.zj0201=#{id}
    </select>
</mapper>