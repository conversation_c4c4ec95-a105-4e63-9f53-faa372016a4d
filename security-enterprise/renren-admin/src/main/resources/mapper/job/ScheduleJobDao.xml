<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.job.dao.ScheduleJobDao">
    <!-- 批量更新状态 -->
    <update id="updateBatch">
        update schedule_job
        set status = #{status} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <!--扬尘数据上报-->
    <update id="callYcUploadData">
        {call PC_STUPLOAD_YC}
    </update>
    <!--扬尘设备状态-->
    <update id="callYcDeviceState">
        {call PC_DEAL_DEVICESTATE}
    </update>
    <!--扬尘预警项目-->
    <select id="callYcWarProject" statementType="CALLABLE">
        {call PC_YJ_YC}
    </select>
    <!--扬尘超标数据-->
    <select id="callExceedStandard" statementType="CALLABLE">
        {call PC_YJ_YC_TJ}
    </select>
    <!--扬尘重置国控值-->
    <update id="callYcResetMonitor">
        {call PC_RESET_MONITOR}
    </update>

    <select id="doYcDt01RealData" statementType="CALLABLE">

        {call PC_REAL_DATA}

    </select>
</mapper>