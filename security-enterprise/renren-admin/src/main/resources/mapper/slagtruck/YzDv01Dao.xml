<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.slagtruck.dao.YzDv01Dao">

    <resultMap type="io.renren.modules.slagtruck.entity.YzDv01Entity" id="yzDv01Map">
        <result property="dv0101" column="DV0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="cp0201" column="CP0201"/>
        <result property="st0101" column="ST0101"/>
        <result property="terminaltype" column="TERMINALTYPE"/>
        <result property="deviceserialno" column="DEVICESERIALNO"/>
        <result property="devicename" column="DEVICENAME"/>
        <result property="whether" column="WHETHER"/>
        <result property="networkStatus" column="NETWORK_STATUS"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getList" resultType="io.renren.modules.slagtruck.dto.YzDv01DTO">
        select a.dv0101,
               a.pj0101,
               b.name pjname,
               a.cp0201,
               d.corpname cpname,
               a.st0101,
               e.number_plate numberPlate,
               a.terminaltype,
               a.deviceserialno,
               a.devicename ,
               a.whether,
               a.network_status
        from YZ_DV01 a, b_pj01 b, b_cp02 c, b_cp01 d, yz_st01 e,R_PJ01_DEPT r
        where a.pj0101 = b.pj0101
          and a.cp0201 = c.cp0201
          and c.cp0101 = d.cp0101
          and a.st0101 = e.st0101
          and a.pj0101 = r.pj0101
          and r.dept_id = #{deptId}
    </select>

    <select id="info" resultType="io.renren.modules.slagtruck.dto.YzDv01DTO">
        select a.dv0101,
               a.pj0101,
               b.name pjname,
               a.cp0201,
               d.corpname cpname,
               a.st0101,
               e.number_plate numberPlate,
               a.terminaltype,
               a.deviceserialno,
               a.name,
               a.whether,
               a.network_status
        from YZ_DV01 a, b_pj01 b, b_cp02 c, b_cp01 d, yz_st01 e
        where a.pj0101 = b.pj0101
          and a.cp0201 = c.cp0201
          and c.cp0101 = d.cp0101
          and a.st0101 = e.st0101
          and a.dv0101=#{dv0101}
    </select>

    <select id="st01list" resultType="io.renren.common.common.dto.CommonDto">
        select a.st0101 value, a.number_plate label
        from yz_st01 a
        where a.cp0201 = #{cp0201}
    </select>

</mapper>