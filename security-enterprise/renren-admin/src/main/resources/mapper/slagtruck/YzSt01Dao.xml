<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.slagtruck.dao.YzSt01Dao">

    <resultMap type="io.renren.modules.slagtruck.entity.YzSt01Entity" id="yzSt01Map">
        <result property="st0101" column="ST0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="cp0201" column="CP0201"/>
        <result property="type" column="TYPE"/>
        <result property="numberPlate" column="NUMBER_PLATE"/>
        <result property="contactPerson" column="CONTACT_PERSON"/>
        <result property="contactNumber" column="CONTACT_NUMBER"/>
        <result property="state" column="STATE"/>
        <result property="vehiclePhotos" column="VEHICLE_PHOTOS"/>
        <result property="remark" column="REMARK"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getPage" resultType="io.renren.modules.slagtruck.dto.YzSt01DTO">
        select
        (SELECT p.name FROM b_pj01 p WHERE p.pj0101 = t.pj0101)  as pjName,
        (SELECT d.corpname FROM b_cp02 c,b_cp01 d WHERE c.cp0101=d.cp0101 and c.cp0201 = t.cp0201 ) as cpName,
        t.* from YZ_ST01 t,R_PJ01_DEPT r where r.pj0101 = t.pj0101
        and r.DEPT_ID = #{deptId}
        <if test="numberPlate !=null and numberPlate !=''">
            and t.st0101 = #{numberPlate}
        </if>
        <if test="type !=null and type !=''">
            and t.type =  #{type}
        </if>
        <if test="state !=null and state !=''">
            and t.state = #{state}
        </if>
    </select>

    <select id="selectCommon" parameterType="java.lang.Long" resultType="io.renren.common.common.dto.CommonDto">
        select t.cp0201 as value, a.corpname||'('||b.dict_label||')' as label
        from B_CP02 t, b_cp01 a,SYS_DICT_DATA b
        where a.cp0101 = t.cp0101
          and t.corptype=b.dict_value
          and b.dict_type_id='14'
          and t.pj0101 = #{pj0101}
    </select>

    <select id="getSt01List" parameterType="java.lang.Long" resultType="io.renren.common.common.dto.CommonDto">
        select t.st0101 as value,t.NUMBER_PLATE as label
        from YZ_ST01 t,R_PJ01_DEPT r
        where r.pj0101 = t.pj0101
        and r.dept_id=#{deptId}
    </select>

    <select id="getImages" resultType="java.lang.String">
        SELECT t.url FROM b_ot01 t WHERE t.busitype = '24' and t.busisysno = #{st0101}
    </select>
</mapper>