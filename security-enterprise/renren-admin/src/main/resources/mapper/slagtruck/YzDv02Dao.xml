<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.slagtruck.dao.YzDv02Dao">

    <resultMap type="io.renren.modules.slagtruck.entity.YzDv02Entity" id="yzDv02Map">
        <result property="dv0201" column="DV0201"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="deviceModel" column="DEVICE_MODEL"/>
        <result property="deviceNum" column="DEVICE_NUM"/>
        <result property="deviceName" column="DEVICE_NAME"/>
        <result property="manufacturer" column="MANUFACTURER"/>
        <result property="batch" column="BATCH"/>
        <result property="sn" column="SN"/>
        <result property="sim" column="SIM"/>
        <result property="state" column="STATE"/>
        <result property="unit" column="UNIT"/>
        <result property="contactName" column="CONTACT_NAME"/>
        <result property="contactPerson" column="CONTACT_PERSON"/>
        <result property="remark" column="REMARK"/>
        <result property="longitude" column="LONGITUDE"/>
        <result property="latitude" column="LATITUDE"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getList" resultType="io.renren.modules.slagtruck.dto.YzDv02DTO">
        select a.dv0201,
        a.pj0101,
        a.device_model,
        a.device_num,
        a.device_name,
        a.manufacturer,
        a.batch,
        a.sn,
        a.sim,
        a.state,
        a.unit,
        a.contact_name,
        a.contact_person,
        a.remark,
        a.longitude,
        a.latitude,
        b.name
        from YZ_DV02 a, B_pj01 b
        where a.pj0101 = b.pj0101
        <if test="pj0101!=null and pj0101!=''">
            and a.pj0101=#{pj0101}
        </if>
        <if test="deviceName!=null and deviceName!=''">
            and a.deviceName=#{deviceName}
        </if>
        <if test="dv0201!=null and dv0201!=''">
            and a.dv0201=#{dv0201}
        </if>
    </select>

</mapper>