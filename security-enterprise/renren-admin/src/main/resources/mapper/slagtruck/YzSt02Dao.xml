<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.slagtruck.dao.YzSt02Dao">

    <resultMap type="io.renren.modules.slagtruck.entity.YzSt02Entity" id="yzSt02Map">
        <result property="st0201" column="ST0201"/>
        <result property="st0101" column="ST0101"/>
        <result property="direction" column="DIRECTION"/>
        <result property="passtime" column="PASSTIME"/>
        <result property="imageUrl" column="IMAGE_URL"/>
    </resultMap>

    <select id="getPage" resultType="io.renren.modules.slagtruck.dto.YzSt02DTO">
        select
        (SELECT p.name FROM b_pj01 p WHERE p.pj0101 = t.pj0101)  as pjName,
        (SELECT d.corpname FROM b_cp02 c,b_cp01 d WHERE c.cp0101=d.cp0101 and c.cp0201 = t.cp0201 ) as cpName,
        t.NUMBER_PLATE as numberPlate,
        t.type as type,
        s.* from YZ_ST01 t, YZ_ST02 s,R_PJ01_DEPT r where t.st0101 = s.st0101
        and r.pj0101 = t.pj0101 and  r.DEPT_ID = #{deptId}
        <if test="numberPlate !=null and numberPlate !=''">
            and t.st0101 = #{numberPlate}
        </if>
        <if test="type !=null and type !=''">
            and t.type =  #{type}
        </if>
        <if test="direction !=null and direction !=''">
            and s.DIRECTION = #{direction}
        </if>
        <if test="passtime !=null and passtime !=''">
            and to_char(s.PASSTIME ,'yyyy-mm-dd')= #{passtime}
        </if>
        <if test="isViolation !=null and isViolation !=''">
            and s.IS_VIOLATION = #{isViolation}
        </if>

    </select>


    <select id="vehicleAccessStatistics" resultType="io.renren.modules.slagtruck.dto.YzSt01StatisticsDTO">

    </select>

    <select id="getCount" resultType="io.renren.modules.slagtruck.dto.YzSt01CountDTO">

    </select>
</mapper>