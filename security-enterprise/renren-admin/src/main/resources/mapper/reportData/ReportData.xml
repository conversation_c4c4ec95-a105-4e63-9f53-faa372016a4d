<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.reportData.dao.ReportDataDao">
    <!--查询泸州数据上报情况-->
    <select id="selectLuZhouReportData" resultType="io.renren.modules.reportData.dto.ReportData">
        select *
        from (
                 select a.reportstatus,
                        count(0)                                                                    as actualTotal,
                        round(COUNT(*) / SUM(COUNT(*)) OVER (), 4) * 100                            as perRate,
                        round(COUNT(*) / SUM(COUNT(*)) OVER (), 4) * 100 || '%'                     as perTotal,
                        '泸州市'                                                                       as areaCode,
                        (select count(0)
                         from gk_report_zjj.attendance b
                         where to_char(b.checkdate, 'yyyy-MM-dd') = to_char(sysdate, 'yyyy-MM-dd')) as dataTotal
                 from gk_report_zjj.attendance a
                 where to_char(a.checkdate, 'yyyy-MM-dd') = to_char(sysdate, 'yyyy-MM-dd')
                 group by a.reportstatus) c
        where c.reportstatus = '1'
    </select>
    <!--住建厅数据上报查询-->
    <select id="selectDourReportData" resultType="io.renren.modules.reportData.dto.ReportData">
        select *
        from (
                 select a.is_send,
                        count(0)                                                as actualTotal,
                        round(COUNT(*) / SUM(COUNT(*)) OVER (), 4) * 100        as perRate,
                        round(COUNT(*) / SUM(COUNT(*)) OVER (), 4) * 100 || '%' as perTotal,
                        '住建厅'                                                   as areaCode,
                        (select count(0)
                         from gk_nmg_smz_zjsb.request_log b
                         where to_char(b.creation_time, 'yyyy-MM-dd') =
                               to_char(sysdate, 'yyyy-MM-dd'))                  as dataTotal
                 from gk_nmg_smz_zjsb.request_log a
                 where to_char(a.creation_time, 'yyyy-MM-dd') =
                       to_char(sysdate, 'yyyy-MM-dd')
                 group by a.is_send) c
        where c.is_send = '1'
    </select>

    <select id="selectReportWriteData" resultType="io.renren.modules.enterprise.tj01.dto.ReportWrite">
        select t.business_type as type, count(0) as sumCount
        from gk_nmg_smz_zjsb.request_log t
        where to_char(t.creation_time, 'yyyyMMdd') =
              to_char(sysdate - 2, 'yyyyMMdd')
        group by t.business_type
    </select>
</mapper>