<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj01info.dao.Pj01InfoDao">
    <select id="getListData" resultType="io.renren.modules.enterprise.pj01info.dto.Pj01InfoDTO">
        select t.NAME,
               t.PJ0101,
               t.AREACODE,
               t.INDUSTRY,
               t.PRJSTATUS,
               t.LINKMAN,
               t.LINKPHONE,
               t.INVESTTYPE,
               t.CONSTRUCTTYPE,
               t.STARTDATE,
               t.INVEST,
               t.CATEGORY,
               t.ADDRESS
        from B_PJ01 t,
             R_PJ01_DEPT a,
             sys_dept b
        where t.PJ0101 = a.PJ0101
          and a.DEPT_ID = #{deptId}
          and b.id = a.dept_id
        <if test="pj0101 != null and pj0101 != ''">
            and t.PJ0101 = #{pj0101}
        </if>
        <if test="areacode != null and areacode != ''">
            and t.areacode like #{areacode} || '%'
        </if>
        <if test="prjstatus != null and prjstatus != ''">
            and t.PRJSTATUS = #{prjstatus}
        </if>
        order by t.AREACODE, t.PRJSTATUS
    </select>

    <!--    参建单位列表分页查询SQL-->
    <select id="getCp02PageList" resultType="io.renren.modules.enterprise.pj01info.dto.Cp02PageDTO">
        select t.cp0201,
               (select c.NAME from b_pj01 c where c.PJ0101 = t.PJ0101) name,
               b.corpname,
               b.corpcode,
               b.linkman,
               b.linkcellphone,
               t.corptype
        from b_cp02 t,
             b_cp01 b
        where t.cp0101 = b.cp0101
          and t.pj0101 = #{params.pj0101}
        order by t.CREATE_DATE desc
    </select>

    <select id="getTeamPageList" resultType="io.renren.modules.enterprise.pj01info.dto.Tm01PageDTO">
        select a.name                      projectName,
               t.responsiblepersonname,
               t.responsiblepersonphone,
               (select b.CORPNAME
                from B_CP01 b,
                     b_cp02 c
                where c.CP0101 = b.CP0101
                  and t.CP0201 = c.CP0201) corpname,
               t.tm0101,
               t.cp0201,
               t.pj0101,
               t.teamname,
               t.entrytime,
               t.exittime,
               t.IN_OR_OUT
        from b_tm01 t,
             b_pj01 a
        where t.pj0101 = a.pj0101
          and a.pj0101 = #{params.pj0101}
        order by t.CREATE_DATE desc
    </select>

    <select id="getWorkerPageList" resultType="io.renren.modules.enterprise.pj01info.dto.Ps02PageDTO">
        select t.ps0201,
               (select c.name
                from b_pj01 c
                where c.pj0101 = t.pj0101)                    projectName,
               t.TM0101,
               (select h.corpname
                from b_cp01 h
                where h.cp0101 = (select f.cp0101
                                  from b_tm01 e,
                                       b_cp02 f
                                  where e.cp0201 = f.cp0201
                                    and e.tm0101 = t.tm0101)) corpName,
               a.name                                         personName,
               a.gender,
               a.idcardnumber                                 idCardNumber,
               t.isteamleader                                 isTeamLeader,
               a.cellphone                                    cellPhone,
               t.worktypecode                                 workTypeCode,
               t.in_or_out                                    inOrOut
        from b_ps02 t,
             b_PS01 a,
             b_tm01 c
        where t.ps0101 = a.ps0101
          and t.tm0101 = c.tm0101
          and t.IN_OR_OUT = '1'
          and t.pj0101 = #{params.pj0101}
        order by t.CREATE_DATE desc
    </select>

    <select id="getAttendancePageList" resultType="io.renren.modules.enterprise.pj01info.dto.Kq02PageDTO">
        select a.KQ0201,
               a.USER_ID     as userId,
               a.PJ0101,
               a.PERSON_NAME as personName,
               a.PERSON_TYPE as personType,
               a.DIRECTION,
               a.ATTENDTYPE  as attendType,
               a.CHECKDATE   as checkDate,
               a.IMAGE_URL   as imageUrl
        from B_KQ02 a
        where a.pj0101 = #{params.pj0101}
          and to_char(a.checkdate, 'yyyy-MM-dd') &gt;=
              to_char(sysdate - 30, 'yyyy-MM-dd')
        order by a.kq0201 desc
    </select>

    <select id="getManagerPageList" resultType="io.renren.modules.enterprise.pj01info.dto.Ps04PageDTO">
        select t.PS0101,
               t.PS0401,
               a.NAME,
               a.idcardnumber,
               a.cellphone,
               (select d.NAME from B_PJ01 d where d.PJ0101 = t.PJ0101) projectName,
               t.CP0201,
               a.GENDER,
               t.JOBTYPE                                               jobtype,
               t.MANAGETYPE                                            manageType
        from B_PS04 t,
             b_ps01 a
        where t.PS0101 = a.PS0101
          and t.IN_OR_OUT = '1'
          and t.pj0101 = #{params.pj0101}
        order by t.CREATE_DATE desc
    </select>
</mapper>