<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.kq02.dao.Kq02Dao">
    <resultMap id="noAttendanceProject" type="io.renren.modules.enterprise.kq02.dto.NoAttendanceAreaCode">
        <result property="areaCode" column="areaCode"/>
        <result property="email" column="email"/>
        <result property="areaName" column="areaName"/>
        <collection property="list" ofType="io.renren.modules.enterprise.kq02.dto.NoAttendanceProject">
            <result property="pj0101" column="pj0101"/>
            <result property="projectName" column="projectName"/>
            <result property="linkPhone" column="linkPhone"/>
            <result property="linkMan" column="linkMan"/>
            <result property="reportPlace" column="reportPlace"/>
            <result property="workerPresent" column="workerPresent"/>
            <result property="managerPresent" column="managerPresent"/>
            <result property="projectAreaName" column="projectAreaName"/>
        </collection>
    </resultMap>
    <select id="getListData" resultType="io.renren.modules.enterprise.kq02.dto.Kq02PageDTO">
        SELECT *
        FROM (SELECT TMP.*, ROWNUM ROW_ID
        FROM (
        select a.KQ0201,
               count(*) over ()    total,
               c.NAME           as projectName,
               a.USER_ID        as userId,
               a.PJ0101,
               a.PERSON_NAME    as personName,
               a.PERSON_TYPE    as personType,
               a.DIRECTION,
               a.ATTENDTYPE     as attendType,
               a.CHECKDATE      as checkDate,
               a.IMAGE_URL      as imageUrl,
               a.DEVICESERIALNO as deviceKey
        <!--               (select e.risk_assessment_grade-->
        <!--                from (select d.risk_assessment_grade, d.user_id-->
        <!--                      from b_person_health_code d-->
        <!--                      order by d.report_date desc) e-->
        <!--                where e.user_id = a.user_id-->
        <!--                  and rownum = 1) riskAssessmentGrade-->
        from b_pj01 c
            inner join R_PJ01_DEPT b
                       on c.PJ0101 = b.PJ0101 and b.DEPT_ID = #{deptId}
            inner join B_KQ02 a
        on a.PJ0101 = c.PJ0101
        <if test="personName != null and personName != ''">
            and a.PERSON_NAME = #{personName}
        </if>
        <if test="startTime != null and startTime != ''">
            and to_Char(a.checkDate, 'yyyy-MM-dd') &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and to_Char(a.checkDate, 'yyyy-MM-dd') &lt;= #{endTime}
        </if>
        <if test="personType != null and personType != ''">
            and a.PERSON_TYPE = #{personType}
        </if>
        <if test="pj0101 != null and pj0101 != ''">
            and a.PJ0101 = #{pj0101}
        </if>
        <if test="deviceKey != null and deviceKey != ''">
            and a.DEVICESERIALNO = #{deviceKey}
        </if>
        order by a.CHECKDATE desc
        ) TMP
        WHERE ROWNUM &lt;= #{rowNum})
        WHERE ROW_ID > #{rowId}
    </select>

    <select id="selectHasKq" resultType="java.lang.Long">
        select count(1)
        from b_kq02 t
        where t.user_id = #{ps0401}
          and to_date(t.checkdate, 'yyyymmddhhmiss') &gt;=
              to_date(#{createDate}, 'yyyymmddhhmiss')
          and to_date(t.checkdate, 'yyyymmddhhmiss') &lt;=
              to_date(#{latestKqtime}, 'yyyymmddhhmiss')
    </select>
    <!--查询无考勤项目-->
    <select id="selectNoAttendanceProjects" resultMap="noAttendanceProject">
        select a1.*
        from (
        select a.pj0101,
               a.areacode                                                                         as areaCode,
               (select m.NAME from SYS_REGION m where m.VALUE = substr(a.AREACODE, 0, 4) || '00') as areaName,
               a.name                                                                             as projectName,
               a.linkman                                                                          as linkMan,
               a.linkphone                                                                        as linkPhone,
               (select p.NAME from SYS_REGION p where p.VALUE = a.AREACODE)                       as projectAreaName,
               nvl(b1.attTotal, '0')                                                              as attTotal,
               (select c.email
                from sys_user c,
                     sys_dept d
                where c.dept_id = d.id
                  and d.AREACODE = substr(a.areacode, 0, 4) || '00'
                  and ROWNUM = 1)                                                                 as email,
               fn_report_params_pj01((select n.report_type
                                      from b_pj01_report n
                                      where n.pj0101 = a.pj0101))                                 as reportPlace,

               nvl(m1.workerTotal, '0')                                                           as workerPresent,
               nvl(m3.managerTotal, '0')                                                          as managerPresent
        from b_pj01 a
                 left join (select b.pj0101, count(0) as attTotal
                            from b_kq02 b
                            where to_char(b.checkdate, 'yyyy-MM-dd') &gt;=
                                  to_char(sysdate - #{day}, 'yyyy-MM-dd')
                              and to_char(b.checkdate, 'yyyy-MM-dd') &lt;
                                  to_char(sysdate, 'yyyy-MM-dd')
                            group by b.pj0101) b1
                           on a.pj0101 = b1.pj0101
                 left join (select m.pj0101, count(0) as workerTotal
                            from b_ps02 m
                            where m.in_or_out = '1'
                            group by m.pj0101) m1
                           on a.pj0101 = m1.pj0101
                 left join (select m2.pj0101, count(0) as managerTotal
                            from b_ps04 m2
                            where m2.in_or_out = '1'
                            group by m2.pj0101) m3
                           on a.pj0101 = m3.pj0101
        where a.prjstatus = '3'
          and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>)
    order by a.areacode) a1
    where a1.attTotal = '0'
    </select>

    <select id="getListDataCount" resultType="long">
        select count(a.KQ0201)
        from b_pj01 c
            inner join R_PJ01_DEPT b
                       on c.PJ0101 = b.PJ0101 and b.DEPT_ID = #{deptId}
            inner join B_KQ02 a
        on a.PJ0101 = c.PJ0101
        <if test="personName != null and personName != ''">
            and a.PERSON_NAME = #{personName}
        </if>
        <if test="startTime != null and startTime != ''">
            and to_Char(a.checkDate, 'yyyy-MM-dd') &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and to_Char(a.checkDate, 'yyyy-MM-dd') &lt;= #{endTime}
        </if>
        <if test="personType != null and personType != ''">
            and a.PERSON_TYPE = #{personType}
        </if>
        <if test="pj0101 != null and pj0101 != ''">
            and a.PJ0101 = #{pj0101}
        </if>
        <if test="deviceKey != null and deviceKey != ''">
            and a.DEVICESERIALNO = #{deviceKey}
        </if>
    </select>
</mapper>