<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pa02.dao.Pa02Dao">

    <resultMap type="io.renren.modules.enterprise.pa02.entity.Pa02Entity" id="pa02Map">
        <result property="pa0201" column="PA0201"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="detialid" column="DETIALID"/>
        <result property="comaccount" column="COMACCOUNT"/>
        <result property="comaccountname" column="COMACCOUNTNAME"/>
        <result property="accounttype" column="ACCOUNTTYPE"/>
        <result property="accountdate" column="ACCOUNTDATE"/>
        <result property="accountnum" column="ACCOUNTNUM"/>
        <result property="capitalcurrencytype" column="CAPITALCURRENCYTYPE"/>
        <result property="blance" column="BLANCE"/>
        <result property="purpose" column="PURPOSE"/>
        <result property="partaccount" column="PARTACCOUNT"/>
        <result property="partname" column="PARTNAME"/>
        <result property="description" column="DESCRIPTION"/>
    </resultMap>

    <select id="getList" resultType="io.renren.modules.enterprise.pa02.dto.Pa02DTO">
        select t.*, a.name
        from B_PA02 t, B_pj01 a, r_pj01_dept b
        where t.pj0101 = a.pj0101
        and t.pj0101 = b.pj0101
        and b.dept_id = #{deptId}
        <if test="name != null and name != ''">
            and a.NAME like '%' || #{name} || '%'
        </if>
    </select>
</mapper>