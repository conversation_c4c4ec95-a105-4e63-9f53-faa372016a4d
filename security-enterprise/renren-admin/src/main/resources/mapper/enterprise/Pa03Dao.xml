<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pa03.dao.Pa03Dao">

    <resultMap type="io.renren.modules.enterprise.pa03.entity.Pa03Entity" id="pa03Map">
        <result property="pa0301" column="PA0301"/>
        <result property="pa0201" column="PA0201"/>
        <result property="accountnum" column="ACCOUNTNUM"/>
        <result property="partaccount" column="PARTACCOUNT"/>
        <result property="partname" column="PARTNAME"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="createDate" column="CREATE_DATE"/>
    </resultMap>
    <select id="getList" resultType="io.renren.modules.enterprise.pa03.dto.Pa03DTO">
        select t.*, a.comaccount, a.comaccountname, a.accountdate
        from B_PA03 t, b_pa02 a, r_pj01_dept b
        where t.pa0201 = a.pa0201
        and a.pj0101 = b.pj0101
        and b.dept_id = #{deptId}
        <if test="partname != null and partname != ''">
            and t.partname like '%' || #{partname} || '%'
        </if>
        <if test="comaccount != null and comaccount != ''">
            and a.comaccount = #{comaccount}
        </if>
    </select>

</mapper>