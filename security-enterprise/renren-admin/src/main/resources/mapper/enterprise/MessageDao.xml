<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.message.dao.MessageDao">
    <!--分页查询-->
    <select id="getList" resultType="io.renren.modules.enterprise.message.dto.MessagePage">
        select a.PJ0101,
               a.NAME      as projectName,
               a.AREACODE  as areaCode,
               a.LINKMAN   as linkMan,
               a.LINKPHON<PERSON> as linkPhone,
               a.PRJSTATUS as projectStatus
        from b_pj01 a
        <where>
            <if test="projectName != '' and projectName != null">
                a.NAME like '%' || #{projectName} || '%'
            </if>
            <if test="areaCode != '' and areaCode != null">
                and a.AREACODE like '' || #{areaCode} || '%'
            </if>
            <if test="projectStatus != '' and projectStatus != null">
                and a.PRJSTATUS = #{projectStatus}
            </if>
        </where>
    </select>
    <!--查询项目下边的参建单位-->
    <select id="selectPartUnits" resultType="java.lang.Long">
        select a.CP0201
        from b_cp02 a
        where a.IN_OR_OUT = '1'
          and a.PJ0101 = #{pj0101}
    </select>
    <!--查询项目下边的班组信息-->
    <select id="selectTeam" resultType="java.lang.Long">
        select a.TM0101
        from B_TM01 a
        where a.PJ0101 = #{pj0101}
          and a.IN_OR_OUT = '1'
    </select>
    <!--查询项目下边的专户信息-->
    <select id="selectPa01" resultType="java.lang.Long">
        select a.PA0101
        from B_PA01 a
        where a.PJ0101 = #{pj0101}
          and ROWNUM = 1
    </select>
    <!--查询项目下边的人员信息-->
    <select id="selectPersonIds" resultType="java.lang.Long">
        select a.PS0201
        from B_PS02 a
        where a.PJ0101 = #{pj0101}
          and a.IN_OR_OUT = '1'
    </select>
    <!--查询项目下边的管理人员信息-->
    <select id="selectManagerIds" resultType="java.lang.Long">
        select a.PS0401
        from b_ps04 a
        where a.PJ0101 = #{pj0101}
          and a.IN_OR_OUT = '1'
    </select>
    <!--查询考勤信息-->
    <select id="selectAttInfo" resultType="io.renren.modules.enterprise.kq02.dto.Kq02DTO">
        select *
        from b_kq02 a
        where to_char(a.CREATE_DATE, 'yyyy-MM-dd') = to_char(sysdate, 'yyyy-MM-dd')
          and a.PJ0101 = #{pj0101}
    </select>
</mapper>