<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pa01.dao.Pa01Dao">
    <select id="pageList" resultType="io.renren.modules.enterprise.pa01.dto.Pa01DTO">
        select t.PA0101, t.PJ0101, t.SPECIAL_ACCOUNT, t.BANK_NAME, t.SPECIAL_ACCOUNT_NO, t.PAY_BANK_CODE
        from b_pa01 t,
             R_PJ01_DEPT a
        where t.PJ0101 = a.PJ0101
          and a.DEPT_ID = #{deptId}
        <if test="specialAccount != null and specialAccount != ''">
            and t.SPECIAL_ACCOUNT like #{specialAccount}
        </if>
    </select>
    <!--查询项目的专户信息-->
    <select id="selectByPj0101" resultType="io.renren.modules.enterprise.pa01.dto.Pa01DTO">
        select *
        from B_PA01 t
        where t.PJ0101 = #{pj0101} and rownum=1
    </select>
</mapper>