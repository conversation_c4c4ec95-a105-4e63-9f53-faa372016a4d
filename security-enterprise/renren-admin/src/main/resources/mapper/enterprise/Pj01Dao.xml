<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj01.dao.Pj01Dao">
    <select id="getListData" resultType="io.renren.modules.enterprise.pj01.dto.Pj01DTO">
        select t.NAME,
               t.PJ0101,
               t.AREACODE,
               t.INDUSTRY,
               t.PRJSTATUS,
               t.LINKMAN,
               t.LINKPHONE,
               t.INVESTTYPE,
               t.CONSTRUCTTYPE,
               t.STARTDATE,
               t.INVEST
        from B_PJ01 t,
             R_PJ01_DEPT a
        where t.PJ0101 = a.PJ0101
          and a.DEPT_ID = #{deptId}
        <if test="pj0101 != null and pj0101 != ''">
            and t.PJ0101 = #{pj0101}
        </if>
    </select>
    <select id="loadUserProjectInfo" resultType="io.renren.modules.enterprise.pj01.dto.Pj01DTO">
        select *
        from B_PJ01 t
        where t.DEPT_ID = #{deptId}
    </select>
    <select id="selectRegionByPj0101" resultType="io.renren.modules.enterprise.pj01.dto.Pj01DTO">
        select t.lat, t.lng, t.region
        from b_pj01 t
        where t.pj0101 = #{pj0101}
    </select>


    <update id="updateRegion">
        update b_pj01
        set region = #{region}
        where pj0101 = #{pj0101}
    </update>

    <select id="selectProjectList" resultType="io.renren.common.common.dto.CommonDto">
        select a.NAME as label, a.PJ0101 as value
        from b_pj01 a,
             R_PJ01_DEPT b
        where a.PJ0101 = b.PJ0101
          and b.DEPT_ID = #{deptId}
        order by a.PJ0101 desc
    </select>
</mapper>