<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj07.dao.Pj07Dao">
    <!--分页查询-->
    <select id="getPageList" resultType="io.renren.modules.enterprise.pj07.dto.Pj07Page">
        select a.pj0101,
               a.name                                            as projectName,
               a.areacode                                        as areaCode,
               a.linkman                                         as linkMan,
               a.linkphone                                       as linkPhone,
               a.prjstatus                                       as projectStatus,
               nvl(b.whether, '0')                               as whether,
               b.remark,
               decode(c1.turnkeyUnitTotal, '', 'x', '√')         as turnkeyUnit,
               decode(c2.supervisionUnitTotal, '', 'x', '√')     as supervisionUnit,
               decode(c3.constructionTotal, '', 'x', '√')        as construction,
               decode(c4.otherUnitsTotal, '', 'x', '√')          as otherUnits,
               decode(p1.supervisingEngineerTotal, '', 'x', '√') as supervisingEngineer,
               decode(p2.projectManagerTotal, '', 'x', '√')      as projectManager
        from b_pj01 a
                 left join b_pj07 b
                           on a.pj0101 = b.pj0101
                 left join (select c.pj0101, c.corptype, count(0) as turnkeyUnitTotal
                            from b_cp02 c
                            where c.in_or_out = '1'
                              and c.corptype = '9'
                            group by c.pj0101, c.corptype) c1
                           on a.pj0101 = c1.pj0101
                 left join (select c.pj0101, c.corptype, count(0) as supervisionUnitTotal
                            from b_cp02 c
                            where c.in_or_out = '1'
                              and c.corptype = '7'
                            group by c.pj0101, c.corptype) c2
                           on a.pj0101 = c2.pj0101
                 left join (select c.pj0101, c.corptype, count(0) as constructionTotal
                            from b_cp02 c
                            where c.in_or_out = '1'
                              and c.corptype = '8'
                            group by c.pj0101, c.corptype) c3
                           on a.pj0101 = c3.pj0101
                 left join (select c.pj0101, count(0) as otherUnitsTotal
                            from b_cp02 c
                            where c.in_or_out = '1'
                              and c.corptype not in ('7', '8', '9')
                            group by c.pj0101) c4
                           on a.pj0101 = c4.pj0101
                 left join (select p.pj0101,
                                   p.jobtype,
                                   m.corptype,
                                   count(0) as supervisingEngineerTotal
                            from b_cp02 m,
                                 sys_jobtype n,
                                 b_ps04 p
                            where m.corptype = n.corptype
                              and p.jobtype = n.jobtype
                              and p.in_or_out = '1'
                              and m.in_or_out = '1'
                              and m.corptype = '7'
                              and p.jobtype = '1001'
                            group by p.pj0101, p.jobtype, m.corptype) p1
                           on a.pj0101 = p1.pj0101
                 left join (select p.pj0101,
                                   p.jobtype,
                                   m.corptype,
                                   count(0) as projectManagerTotal
                            from b_cp02 m,
                                 sys_jobtype n,
                                 b_ps04 p
                            where m.corptype = n.corptype
                              and p.jobtype = n.jobtype
                              and p.in_or_out = '1'
                              and m.in_or_out = '1'
                              and m.corptype = '9'
                              and p.jobtype = '1009'
                            group by p.pj0101, p.jobtype, m.corptype) p2
                           on a.pj0101 = p2.pj0101
        where a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>)
        <if test="projectStatus != '' and projectStatus != null">
            and a.PRJSTATUS = #{projectStatus}
        </if>
        <if test="areaCode != '' and areaCode != null">
            and a.AREACODE like '' || #{areaCode} || '%'
        </if>
        <if test="pj0101 != '' and pj0101 != null">
            and a.PJ0101 = #{pj0101}
        </if>
        <if test="whether != '' and whether != null">
            and nvl(b.WHETHER, '0') = #{whether}
        </if>
        order by a.areacode, nvl(b.WHETHER, '0')
    </select>
</mapper>