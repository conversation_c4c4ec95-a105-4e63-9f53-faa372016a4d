<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps01.dao.Ps01Dao">
    <select id="loadPs01" resultType="io.renren.modules.enterprise.ps01.dto.Ps01DTO">
        select *
        from B_PS01 t
        where t.IDCARDNUMBER = #{idCardNumber}
    </select>
    <select id="getList" resultType="io.renren.modules.enterprise.ps01.dto.Ps01DTO">
        select t.PS0101, t.NAME, t.IDCARDTYPE, t.IDCARDNUMBER, t.GENDER, t.NATION, t.CELLPHONE
        from B_PS01 t
        where 1 = 1
        <if test="params.name != null and params.name != ''">
            and t.NAME like '%' || #{params.name} || '%'
        </if>
        <if test="params.idcardnumber != null and params.idcardnumber != ''">
            and t.IDCARDNUMBER like '%' || #{params.idcardnumber} || '%'
        </if>
    </select>
    <!--查询人员基础信息-->
    <select id="selectPersonByBusinessId" resultType="io.renren.modules.enterprise.ps01.dto.Ps01DTO">
        select *
        from (
                 select a.*, b.ps0401 as userId
                 from b_ps01 a,
                      b_ps04 b
                 where a.ps0101 = b.ps0101
                 union all
                 select c.*, d.ps0201 as userId
                 from b_ps01 c,
                      b_ps02 d
                 where c.ps0101 = d.ps0101) p
        where p.userId = #{id}
    </select>
</mapper>