<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps04.dao.Ps04Dao">
    <select id="getListData" resultType="io.renren.modules.enterprise.ps04.dto.Ps04PageDTO">
        select t.PS0101,
               t.PJ0101,
               t.PS0401,
               a.NAME,
               a.idcardnumber,
               a.cellphone,
               (select d.NAME from B_PJ01 d where d.PJ0101 = t.PJ0101) as projectName,
               t.CP0201,
               a.GENDER,
               t.JOBTYPE                                               as jobtype,
               t.MANAGETYPE                                            as manageType,
               t.ENTRYTIME                                             as entryTime,
               t.EXITTIME                                              as exitTime,
               b.corptype                                              as corpType,
               t.IN_OR_OUT                                             as inOrOut,
               decode((select count(0)
                       from keyjob_config p
                       where p.corptype = b.corptype
                         and p.jobtype = t.jobtype),
                      '0',
                      '0',
                      '',
                      '0',
                      '1')                                             as hasKeyPositions
        from B_PS04 t
            inner join
        b_ps01 a on t.PS0101 = a.PS0101
        <if test="name != null and name != ''">
            and a.NAME like #{name}
        </if>
        inner join
        R_PJ01_DEPT c on t.PJ0101 = c.PJ0101 and c.DEPT_ID = #{deptId}
        inner join
        b_cp02 b on b.CP0201 = t.CP0201
        <where>
            <if test="jobtype != null and jobtype != ''">
                t.jobtype like #{jobtype}
            </if>
            <if test="inOrOut != null and inOrOut != ''">
                and t.IN_OR_OUT = #{inOrOut}
            </if>
            <if test="pj0101 != null and pj0101 != ''">
                and t.PJ0101 = #{pj0101}
            </if>
        </where>
        order by t.PS0401 desc, t.ENTRYTIME desc
    </select>

    <update id="updateInOrOutByIds">
        update B_PS04 t
        <set>
            t.IN_OR_OUT = #{type},
            <choose>
                <when test="type == '2'.toString()">
                    t.EXITTIME = to_date(to_char(sysdate, 'yyyy-MM-dd'), 'yyyy-MM-dd'),
                </when>
                <otherwise>
                    t.ENTRYTIME = to_date(to_char(sysdate, 'yyyy-MM-dd'), 'yyyy-MM-dd'),
                    t.EXITTIME=''
                </otherwise>
            </choose>
        </set>
        where t.PS0401 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item.ps0401}
        </foreach>
    </update>

    <select id="selectPersonByIds" resultType="io.renren.modules.enterprise.kq05.dto.PersonDTO">
        select a.NAME, b.PS0401 userId, b.PHOTO imageUrl, b.PJ0101
        from b_ps01 a,
             b_ps04 b where b.PS0101 = a.PS0101
                        and b.PS0401 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="getKeyJobData" resultType="io.renren.modules.enterprise.ps04.dto.KeyJobDTO">
        select *
        from (select t.projectname,
                     t.corptype,
                     t.jobtype,
                     b.name,
                     b.idcardnumber,
                     b.companyname,
                     b.pj0101,
                     b.in_or_out,
                     b.entertime,
                     m.CP0201
        from (select b.pj0101,
                     b.name as projectname,
                     d.corptype,
                     d.jobtype
              from (select t.pj0101, t.name, '1' type
                    from b_pj01 t,
                         r_pj01_dept a
                    where t.pj0101 = a.pj0101
                      and a.dept_id = #{deptId}) b,
                   (select c.corptype, c.jobtype, c.create_date, '1' type
                    from KEYJOB_CONFIG c) d
        where b.type = d.type
        <if test="jobtype != null and jobtype != ''">
            and d.jobtype = #{jobtype}
        </if>
        ) t
            left join b_cp02 m
                      on m.pj0101 = t.pj0101
                          and t.corptype = m.corptype
            left join (select s.NAME,
                              s.idcardnumber,
                              p.CORPNAME                         COMPANYNAME,
                              c.CORPTYPE,
                              b.pj0101,
                              b.jobtype,
                              b.in_or_out,
                              TO_CHAR(b.entrytime, 'yyyy-MM-dd') entertime
                       from B_PS04 b,
                            B_PS01 s,
                            B_CP02 c,
                            B_CP01 p
                       WHERE s.ps0101 = b.ps0101
                         and b.pj0101 = c.pj0101
                         AND b.cp0201 = c.cp0201
                         AND c.cp0101 = p.cp0101
        ) b
                      on t.pj0101 = b.pj0101
                          and t.jobtype = b.jobtype
                          and t.corptype = b.corptype) z
        <where>
            <if test="name != null and name != ''">
                z.name like '%' || #{name} || '%'
            </if>
            <if test="pj0101 != null and pj0101 != ''">
                and z.pj0101=#{pj0101}
            </if>
        </where>
        order by z.CORPTYPE, z.JOBTYPE
    </select>
    <select id="getKeyStation" resultType="java.util.Map">
        SELECT d.dict_label label,
               t.JOBTYPE    value
        FROM KEYJOB_CONFIG t,
             SYS_DICT_TYPE s,
             SYS_DICT_DATA d
        WHERE s.ID = d.DICT_TYPE_ID
          AND t.JOBTYPE = d.dict_value
          AND s.DICT_TYPE = 'JOBTYPE'
    </select>
    <!--查询关键岗位的数量-->
    <select id="selectByCount" resultType="java.lang.Integer">
        select count(0)
        from B_PS04 t
        where t.PJ0101 = #{pj0101}
          and t.CP0201 in (select a.CP0201 from B_CP02 a where a.CORPTYPE = #{partType})
          and t.JOBTYPE = #{jobType}
          and t.IN_OR_OUT = '1'
    </select>
    <!--查询参建单位类型-->
    <select id="selectTypeById" resultType="java.lang.String">
        select a.CORPTYPE
        from B_CP02 a
        where a.CP0201 = #{cp0201}
    </select>

    <select id="getAttachments" resultType="io.renren.modules.enterprise.ot01.dto.Ot01DTO">
        select ot0101, busitype, busisysno, name, url, view_type, original_name
        from b_ot01
        where busisysno = #{ps0401,jdbcType=BIGINT}
          and busitype = '21'
          and whether = '1'
    </select>
    <select id="selectKeys" resultType="io.renren.modules.enterprise.ps04.entity.Ps04Entity">
        select *
        from b_ps04 t
        where t.jobtype in (select a.dict_value
                            from SYS_DICT_DATA a,
                                 SYS_DICT_TYPE b
                            where a.dict_type_id = b.id
                              and b.dict_type = 'KEYJOB')
          and t.pj0101 = #{pj0101}
    </select>
    <!--查询监管岗位信息-->
    <select id="getKeyPositions" resultType="java.lang.Integer">
        select count(0)
        from KEYJOB_CONFIG a
        where a.CORPTYPE = (select b.CORPTYPE from b_cp02 b where b.CP0201 = #{cp0201})
          and a.JOBTYPE = #{jobType}
    </select>

    <select id="selectKeyPositionsById" resultType="int">
        select count(0)
        from b_ps04 a,
             keyjob_config b,
             b_cp02 c
        where a.ps0401 = #{ps0401}
          and a.cp0201 = c.cp0201
          and a.jobtype = b.jobtype
          and b.corptype = c.corptype
    </select>
    <!--更新管理人员头像-->
    <update id="updateManagerPhoto">
        update b_ps04 a
        set a.PHOTO=#{photo}
        where a.PS0401 = #{ps0401}
    </update>
    <!--管理人员信息导出-->
    <select id="selectManagerExcelList" resultType="io.renren.modules.enterprise.ps04.excel.Ps04Excel">
        select b.NAME,
               b.IDCARDNUMBER                     as idCardNumber,
               d.CORPNAME                         as businessName,
               (select m.DICT_LABEL
                from SYS_DICT_DATA m
                where m.DICT_TYPE_ID = '1160061077912858625'
                  and m.DICT_VALUE = b.GENDER)    as gender,
               (select m.DICT_LABEL
                from SYS_DICT_DATA m
                where m.DICT_TYPE_ID = '1'
                  and m.DICT_VALUE = b.NATION)    as nation,
               (select n.JOBTYPENAME
                from SYS_JOBTYPE n
                where n.CORPTYPE = c.CORPTYPE
                  and n.JOBTYPE = a.JOBTYPE)      as workType,
               b.ADDRESS,
               b.CELLPHONE,
               to_char(a.entrytime, 'yyyy-MM-dd') as entryTime,
               to_char(a.exittime, 'yyyy-MM-dd')  as exitTime
        from b_ps04 a,
             b_ps01 b,
             b_cp02 c,
             b_cp01 d,
             b_pj01 e,
             R_PJ01_DEPT f
        where a.PS0101 = b.PS0101
          and a.CP0201 = c.CP0201
          and c.CP0101 = d.CP0101
          and a.PJ0101 = e.PJ0101
          and e.PJ0101 = f.PJ0101
          and f.DEPT_ID = #{deptId}
        <if test="name != null and name != ''">
            and b.NAME like #{name}
        </if>
        <if test="jobtype != null and jobtype != ''">
            and a.jobtype like #{jobtype}
        </if>
        order by a.IN_OR_OUT, a.PS0401 desc
    </select>
</mapper>