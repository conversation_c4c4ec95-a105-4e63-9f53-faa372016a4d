<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj03.dao.Pj03Dao">
    <select id="getListData" resultType="io.renren.modules.enterprise.pj03.dto.Pj03DTO">
        select t.pj0301,
               t.PJ0101,
               t.TRAININGDATE,
               t.TRAININGNAME,
               t.TRAININGTYPECODE,
               t.TRAININGDURATION,
               t.TRAINER
        from B_PJ03 t,
             R_PJ01_DEPT a
        where t.PJ0101 = a.PJ0101
          and a.DEPT_ID = #{deptId}
    </select>
</mapper>