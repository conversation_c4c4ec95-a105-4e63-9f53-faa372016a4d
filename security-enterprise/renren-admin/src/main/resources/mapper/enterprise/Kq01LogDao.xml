<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.kq01log.dao.Kq01LogDao">
    <select id="getListData" resultType="io.renren.modules.enterprise.kq01log.dto.Kq01LogPageDTO">
        select b.NAME           projectName,
               t.PJ0101,
               t.DEVICEKEY,
               t.DEVICESERIALNO,
               t.KQ0101,
               t.NOTE,
               t.TERMINALTYPE,
               t.NETWORK_STATUS networkStatus
        from B_KQ01 t,
             R_PJ01_DEPT a,
             B_PJ01 b
        where a.PJ0101 = t.PJ0101
          and t.PJ0101 = b.PJ0101
          and a.DEPT_ID = #{deptId}
          and t.whether = '1'
        <if test="deviceserialno != null and deviceserialno != ''">
            and t.DEVICESERIALNO = #{deviceserialno}
        </if>
        <if test="pj0101 != null and pj0101 != ''">
            and b.PJ0101 = #{pj0101}
        </if>
    </select>

    <select id="getListInfo" resultType="io.renren.modules.enterprise.kq04.dto.Kq04DTO">
        select *
        from b_kq04 t
        where t.DEVICESERIALNO = #{deviceserialno}
        order by t.create_date desc
    </select>
</mapper>