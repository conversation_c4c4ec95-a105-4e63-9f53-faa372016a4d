<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.kq07.dao.Kq07Dao">

    <select id="getListData" resultType="io.renren.modules.enterprise.kq07.dto.Kq07DTO">
        select kq0701,
               name,
               code,
               business_license,
               legal_name,
               legal_id_card_number,
               linkman,
               linkman_phone,
               creator,
               create_date,
               updater,
               update_date,
               secrets,
               vendorname
        from b_kq07
        <where>
            <if test="name != null and name != ''">
                name like '%' || #{name} || '%'
            </if>
            <if test="linkman != null and linkman != ''">
                and linkman like '%' || #{linkman} || '%'
            </if>
            <if test="code != null and code != ''">
                and code like '%' || #{code} || '%'
            </if>
        </where>
        order by kq0701 desc
    </select>
</mapper>