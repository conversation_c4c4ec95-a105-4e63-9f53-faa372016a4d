<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.person.dao.PersonTempDao">
    <resultMap type="io.renren.modules.enterprise.person.dto.PersonTempDTO" id="personTempMap">
        <result property="id" column="ID"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="name" column="NAME"/>
        <result property="idCardNumber" column="ID_CARD_NUMBER"/>
        <result property="cellphone" column="CELLPHONE"/>
        <result property="headImageUrl" column="HEAD_IMAGE_URL"/>
        <result property="whether" column="WHETHER"/>
    </resultMap>

    <update id="updateStateByIds">
        update b_person_temp t
        <set>
            t.whether= #{state}
        </set>
        where t.id in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>

    <select id="getListData" resultType="io.renren.modules.enterprise.person.dto.PersonTempDTO">
        select a.*
        from b_person_temp a,
             R_PJ01_DEPT c
        where a.PJ0101 = c.PJ0101
          and c.DEPT_ID = #{deptId}
          and a.WHETHER = '1'
        <if test="name != '' and name != null">
            and a.NAME like '%' || #{name} || '%'
        </if>
    </select>

    <select id="selectBatchByIds" resultMap="personTempMap">
        select *
        from B_PERSON_TEMP a where a.ID in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>