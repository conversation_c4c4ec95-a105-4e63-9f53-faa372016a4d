<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.dataShare.facilitatorOperation.dao.FacilitatorOperationDao">
    <resultMap type="io.renren.modules.enterprise.dataShare.facilitatorOperation.dto.FacilitatorOperationDTO"
               id="facilitatorOperationMap">
        <result property="id" column="ID"/>
        <result property="operation" column="OPERATION"/>
        <result property="requestUri" column="REQUEST_URI"/>
        <result property="requestMethod" column="REQUEST_METHOD"/>
        <result property="requestParams" column="REQUEST_PARAMS"/>
        <result property="requestTime" column="REQUEST_TIME"/>
        <result property="userAgent" column="USER_AGENT"/>
        <result property="ip" column="IP"/>
        <result property="status" column="STATUS"/>
        <result property="appKey" column="APP_KEY"/>
        <result property="createDate" column="CREATE_DATE"/>
    </resultMap>

    <select id="getListData" resultMap="facilitatorOperationMap">
        select *
        from B_FACILITATOR_OPERATION a
        <where>
            <if test="appKey != '' and appKey != null">
                a.APP_KEY = #{appKey}
            </if>
            <if test="status != '' and status != null">
                and a.STATUS = #{status}
            </if>
        </where>
        order by a.CREATE_DATE desc
    </select>
</mapper>