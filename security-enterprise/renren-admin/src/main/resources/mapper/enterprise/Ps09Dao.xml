<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps09.dao.Ps09Dao">
    <!--分页列表查询-->
    <select id="selectPageList" resultType="io.renren.modules.enterprise.ps09.dto.Ps09Page">
        select a.PS0901,
               a.PJ0101,
               a.CP0201,
               a.PS0101,
               b.NAME             as projectName,
               b.AREACODE         as areaCode,
               d.NAME             as personName,
               d.IDCARDNUMBER     as idCardNumber,
               d.CELLPHONE        as cellPhone,
               a.JOBTYPE          as jobType,
               a.ENTRYTIME        as entryTime,
               a.state,
               a.NOT_PASS_REASONS as notPassReasons,
               a.CREATE_DATE      as createDate,
               a.UPDATE_DATE      as updateDate,
               a.AUDIT_DATE       as auditDate
        from b_ps09 a,
             b_pj01 b,
             R_PJ01_DEPT c,
             B_PS01 d
        where a.PJ0101 = b.PJ0101
          and b.PJ0101 = c.PJ0101
          and a.PS0101 = d.PS0101
          and c.DEPT_ID = #{deptId}
        <if test="pj0101 != '' and pj0101 != null">
            and b.PJ0101 = #{pj0101}
        </if>
        <if test="personName != '' and personName != null">
            and d.NAME like '%' || #{personName} || '%'
        </if>
        <if test="jobType != '' and jobType != null">
            and a.JOBTYPE = #{jobType}
        </if>
        <if test="areaCode != '' and areaCode != null">
            and b.AREACODE like '' || #{areaCode} || '%'
        </if>
        order by a.STATE, a.AUDIT_DATE desc
    </select>

    <select id="selectPersonIsExist" resultType="int">
        select count(0)
        from B_PS09 a
        where a.PJ0101 = #{pj0101}
          and a.CP0201 = #{cp0201}
          and a.JOBTYPE = #{jobType}
          and a.STATE = '0'
    </select>
</mapper>