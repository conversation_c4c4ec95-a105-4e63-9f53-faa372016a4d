<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.file.dao.FileDao">
    <select id="selectEmpRecordFormList" resultType="io.renren.modules.enterprise.file.dto.EmpRecordFormDTO">
        select a.name,
               (select f.dict_label
                from sys_dict_data f,
                     sys_dict_type e
                where f.dict_type_id = e.id
                  and e.dict_type = 'GENDER'
                  and f.dict_value = a.gender)       gender,
               a.cellphone,
               a.IDCARDNUMBER                        idCardNumber,
               (select f.dict_label
                from sys_dict_data f,
                     sys_dict_type e
                where f.dict_type_id = e.id
                  and e.dict_type = 'WORKTYPECODE'
                  and f.dict_value = t.worktypecode) workTypeCode,
               b.UNITPRICE                           unitPrice,
               to_char(b.STARTDATE, 'yyyy-MM-dd')    startDate,
               to_char(b.ENDDATE, 'yyyy-MM-dd')      endDate
        from b_ps02 t
                     left join b_ps03 b
                on t.ps0201 = b.ps0201,
             b_ps01 a,
             R_PJ01_DEPT c
                where t.ps0101 = a.ps0101
                  and t.PJ0101 = c.PJ0101
                  and c.DEPT_ID = #{deptId}
        <if test="years != null and years != ''">
            and to_char(t.ENTRYTIME, 'yyyy-MM') = #{years}
        </if>
        <if test="tm0101 != null and tm0101 != ''">
            and t.TM0101 = #{tm0101}
        </if>
    </select>
</mapper>