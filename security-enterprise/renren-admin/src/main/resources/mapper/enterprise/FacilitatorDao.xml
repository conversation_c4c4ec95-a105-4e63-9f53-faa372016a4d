<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.dataShare.facilitator.dao.FacilitatorDao">
    <resultMap type="io.renren.modules.enterprise.dataShare.facilitator.dto.FacilitatorDTO" id="facilitatorMap">
        <result property="id" column="ID"/>
        <result property="appKey" column="APP_KEY"/>
        <result property="appSecret" column="APP_SECRET"/>
        <result property="name" column="NAME"/>
        <result property="code" column="CODE"/>
        <result property="linkMan" column="LINK_MAN"/>
        <result property="linkPhone" column="LINK_PHONE"/>
        <result property="businessLicense" column="BUSINESS_LICENSE"/>
        <result property="whether" column="WHETHER"/>
    </resultMap>

    <select id="getListData" resultMap="facilitatorMap">
        select *
        from B_FACILITATOR a
        <where>
            <if test="name != '' and name != null">
                a.NAME like '%' || #{name} || '%'
            </if>
            <if test="whether != '' and whether != null">
                and a.WHETHER = #{whether}
            </if>
        </where>
        order by a.WHETHER desc
    </select>

    <update id="updateWhether">
        update B_FACILITATOR a
        set a.WHETHER =#{state} where a.ID in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="selectListAll" resultType="io.renren.common.common.dto.CommonDto">
        select a.APP_KEY as value, a.NAME as label
        from B_FACILITATOR a
    </select>

    <select id="selectCountByCode" resultType="java.lang.Integer">
        select count(0)
        from B_FACILITATOR a
        where a.CODE = #{code}
    </select>
</mapper>