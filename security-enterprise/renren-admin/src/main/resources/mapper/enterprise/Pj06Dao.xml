<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj06.dao.Pj06Dao">
    <resultMap type="io.renren.modules.enterprise.pj06.entity.Pj06Entity" id="pj06Map">
        <result property="pj0601" column="PJ0601"/>
        <result property="name" column="NAME"/>
        <result property="industry" column="INDUSTRY"/>
        <result property="areacode" column="AREACODE"/>
        <result property="linkman" column="LINKMAN"/>
        <result property="linkphone" column="LINKPHONE"/>
        <result property="constructionname" column="CONSTRUCTIONNAME"/>
        <result property="constructionnumber" column="CONSTRUCTIONNUMBER"/>
        <result property="contractname" column="CONTRACTNAME"/>
        <result property="contractnumber" column="CONTRACTNUMBER"/>
        <result property="memo" column="MEMO"/>
        <result property="status" column="STATUS"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <!-- 查询未审核的项目数量   -->
    <select id="getProjectNumber" resultType="java.lang.Long">
        select count(0)
        from B_PJ06 t
        where t.NAME = #{name}
          and t.STATUS = '0'
          and t.AREACODE = #{areaCode}
    </select>
    <!--  查询已存在的项目数量  -->
    <select id="getPj01Number" resultType="java.lang.Long">
        select count(0)
        from B_PJ01 t
        where t.NAME = #{name}
          and t.AREACODE = #{areaCode}
    </select>
    <select id="selectUserNameMax" resultType="java.lang.String">
        select max(t.username)
        from sys_user t
        where t.USER_TYPE = '1'
          and t.username like '' || #{areaCode} || '%'
    </select>

    <select id="getPageData" resultMap="pj06Map">
        select t.pj0601,
               t.name,
               t.industry,
               t.areacode,
               t.linkman,
               t.linkphone,
               t.constructionname,
               t.constructionnumber,
               t.contractname,
               t.contractnumber,
               t.memo,
               t.status,
               t.pay_bank_code,
               t.report_type,
               t.create_date,
               decode(t.status, '1', t.update_date, null) as update_date,
               t.role_id_list,
               t.report
        from b_pj06 t where 1 = 1
        <if test="name != null and name != ''">
            and t.name like #{name}
        </if>
        order by t.status, t.update_date desc
    </select>

    <insert id="saveProjectBankRelation">
        insert into b_pj01_bank t (t.pj0101, t.bank_code)
        values (#{pj0101}, #{payBankCode})
    </insert>

    <update id="callProjectRelation">
        {call PC_R_PJ01_DEPT}
    </update>
</mapper>