<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps08.dao.Ps08Dao">
    <!--分页数据查询-->
    <select id="getListData" resultType="io.renren.modules.enterprise.ps08.dto.Ps08PageDTO">
        select c.PS0801,
               c.PS0201,
               c.HAPPEN_DATE,
               c.CONTENT,
               a.<PERSON>AME         as projectName,
               e.<PERSON>AM<PERSON>         as workerName,
               e.IDCARDNUMBER as idCardNumber
        from b_pj01 a,
             R_PJ01_DEPT b,
             b_ps08 c,
             b_ps02 d,
             b_ps01 e
        where a.PJ0101 = b.PJ0101
          and c.PJ0101 = a.PJ0101
          and c.PS0201 = d.PS0201
          and d.PS0101 = e.PS0101
          and b.DEPT_ID = #{deptId}
    </select>
</mapper>