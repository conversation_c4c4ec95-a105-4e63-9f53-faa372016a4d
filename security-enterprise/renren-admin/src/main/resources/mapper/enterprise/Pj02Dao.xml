<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj02.dao.Pj02Dao">

    <select id="getPj02List" resultType="io.renren.modules.enterprise.pj02.dto.Pj02DTO">
        select t.PJ0101, t.PJ0201, t.PRJNAME, t.BUILDERLICENSENUM, t.ORGANNAME, t.ORGANDATE
        from B_PJ02 t
        where t.PJ0101 = #{id}
    </select>


</mapper>