<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.yj01.dao.Yj01Dao">
    <!--查询在建项目信息-->
    <select id="selectProjectInfo" resultType="java.lang.Long">
        select t.PJ0101
        from b_pj01 t
        where t.PRJSTATUS = '3'
    </select>
    <!--查询管理人员信息-->
    <select id="selectManager" resultType="io.renren.modules.enterprise.yj01.dto.ManagerDTO">
        select (listagg(b.ps0401, ',') within group (order by b.ps0401)) as managerId, count(0) as managerNum
        from b_ps04 b,
             b_ps01 c,
             b_cp02 a
        where b.ps0101 = c.ps0101
          and b.cp0201 = a.cp0201
          and b.IN_OR_OUT = '1'
          and b.pj0101 = #{id}
          and b.jobtype = #{jobType}
          and a.corptype = #{partType}
    </select>
    <!--查询人员工时-->
    <select id="selectByWorkHoursById" resultType="java.lang.String">
        select ROUND(nvl(ROUND(TO_NUMBER(a.endtime - a.starttime) * 24 * 60), '0') / 60, 2)
        from (select (select max(t.checkdate)
                      from b_kq02 t
                      where to_char(t.checkdate, 'yyyy-MM-dd') =
                            to_char(sysdate - 1, 'yyyy-MM-dd')
                        and t.user_id = #{id}) endTime,
                     (select min(t.checkdate)
                      from b_kq02 t
                      where to_char(t.checkdate, 'yyyy-MM-dd') =
                            to_char(sysdate - 1, 'yyyy-MM-dd')
                        and t.user_id = #{id}) startTime
              from dual) a
    </select>
    <!--查询数据-->
    <select id="selectInfo" resultType="io.renren.modules.enterprise.yj01.entity.Yj01Entity">
        select *
        from b_yj01 t
        where t.pj0101 = #{id}
          and t.TYPE = '1'
    </select>
    <!--分页查询-->
    <select id="getList" resultType="io.renren.modules.enterprise.yj01.dto.ProjectKeyPositInfo">
        select p.PJ0101,
               p.name                                as projectName,
               p.areacode                            as areaCode,
               p.linkman                             as linkMan,
               p.linkphone                           as linkPhone,
               fun_manager(p.pj0101, '1009', '9')    as pmName,
               fun_staff_time(p.pj0101, '1009', '9') as pmNameUserHours,
               fun_manager(p.pj0101, '1001', '7')    as generalManager,
               fun_staff_time(p.pj0101, '1001', '7') as generalManagerHours,
               b.content
        from b_pj01 p,
             R_PJ01_DEPT t,
             b_yj01 b
        where p.prjstatus = '3'
          and p.PJ0101 = t.PJ0101
          and t.DEPT_ID = #{deptId}
          and p.pj0101 = b.pj0101
        <if test="projectName != '' and projectName != null">
            and p.NAME like '%' || #{projectName} || '%'
        </if>
        order by p.areacode, p.PJ0101
    </select>
    <!--查询预警内容-->
    <select id="selectWarningContent" resultType="java.lang.String">
        select t.CONTENT
        from B_YJ01 t
        where t.PJ0101 = #{pj0101}
    </select>
</mapper>