<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pd02.dao.Pd02Dao">

    <select id="getList" resultType="io.renren.modules.enterprise.pd02.dto.Pd02DTO">
        select t.PD0201,t.CP0201,t.USETYPE,t.OCCURTIME,t.PD0101,
        t.OPERATIONPERSON,t.REGISTDATE,t.MEMO
        from B_PD02 t,b_pd01 a,R_PJ01_DEPT b
        where a.PJ0101=b.PJ0101 and t.PD0101=a.PD0101 and b.DEPT_ID=#{deptId}
        <if test="devicename != null and devicename.trim() != ''">
            and DEVICENAME like #{devicename}
        </if>
    </select>


</mapper>