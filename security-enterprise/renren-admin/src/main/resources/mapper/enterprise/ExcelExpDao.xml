<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.excelexp.dao.ExcelExpDao">
    <select id="getPayrollRegisterDTOList"
            resultType="io.renren.modules.enterprise.excelexp.dto.PayrollRegisterDTO">
        <choose>
            <when test="personType != null and personType != ''and personType == 1">
                SELECT (SELECT m.teamname FROM b_tm01 m WHERE m.pj0101 = #{pj0101} and m.tm0101 = s2.tm0101) teamname,
                       s1.name,
                       decode(S1.Gender, '2', '女', '1', '男') as                                              gender,
                       s1.idcardnumber,
                       s1.cellphone,
                       s2.payrollbankcardnumber,
                       s2.PAYROLLBANKNAME                    as                                              payRollBankName,
                       s3.unitprice,
                       nvl(e.attcount, 0)                    as                                              count
                FROM b_ps02 s2,
                     b_ps01 s1,
                     b_ps03 s3,
                     (select d.user_id, count(0) attcount
                      from (select to_char(c.checkdate, 'yyyy-MM-dd') attday, c.user_id
                            from b_kq02 c
                            where to_char(c.checkdate, 'yyyy-MM') = #{month}
                              and c.pj0101 = #{pj0101}
                            group by to_char(c.checkdate, 'yyyy-MM-dd'), c.user_id) d
                      group by d.user_id) e
                where s2.pj0101 = #{pj0101}
                  and s1.ps0101 = s2.ps0101
                  and s2.ps0201 = s3.ps0201
                  and s2.ps0201 = e.user_id(+)
                <if test="tm0101 != null and tm0101 != ''">
                    and s2.tm0101 = #{tm0101}
                </if>
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
            </when>
            <when test="personType != null and personType != ''and personType == 2">
                SELECT '管理人员'                                as teamname,
                       s1.name,
                       decode(S1.Gender, '2', '女', '1', '男') as gender,
                       s1.idcardnumber,
                       s1.cellphone,
                       ''                                    as payrollbankcardnumber,
                       ''                                    as payRollBankName,
                       null                                  as unitprice,
                       nvl(e.attcount, 0)                    as count
                FROM b_ps04 s2,
                     b_ps01 s1,
                     (select d.user_id, count(0) attcount
                      from (select to_char(c.checkdate, 'yyyy-MM-dd') attday, c.user_id
                            from b_kq02 c
                            where to_char(c.checkdate, 'yyyy-MM') = #{month}
                              and c.pj0101 = #{pj0101}
                            group by to_char(c.checkdate, 'yyyy-MM-dd'), c.user_id) d
                      group by d.user_id) e
                where s2.pj0101 = #{pj0101}
                  and s1.ps0101 = s2.ps0101
                  and s2.ps0401 = e.user_id(+)
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
            </when>
            <otherwise>
                (

                SELECT (SELECT m.teamname
                        FROM b_tm01 m
                        WHERE m.pj0101 = #{pj0101}
                          and m.tm0101 = s2.tm0101)          as teamname,
                       s1.name,
                       decode(S1.Gender, '2', '女', '1', '男') as gender,
                       s1.idcardnumber,
                       s1.cellphone,
                       s2.payrollbankcardnumber,
                       s2.PAYROLLBANKNAME                    as payRollBankName,
                       s3.unitprice,
                       nvl(e.attcount, 0)                    as count
                FROM b_ps02 s2,
                     b_ps01 s1,
                     b_ps03 s3,
                     (select d.user_id, count(0) attcount
                      from (select to_char(c.checkdate, 'yyyy-MM-dd') attday, c.user_id
                            from b_kq02 c
                            where to_char(c.checkdate, 'yyyy-MM') = #{month}
                              and c.pj0101 = #{pj0101}
                            group by to_char(c.checkdate, 'yyyy-MM-dd'), c.user_id) d
                      group by d.user_id) e
                where s2.pj0101 = #{pj0101}
                  and s1.ps0101 = s2.ps0101
                  and s2.ps0201 = s3.ps0201
                  and s2.PS0201 = e.USER_ID(+)
                <if test="tm0101 != null and tm0101 != ''">
                    and s2.tm0101 = #{tm0101}
                </if>
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
                )
                union all
                (
                SELECT '管理人员'                                as teamname,
                       s1.name,
                       decode(S1.Gender, '2', '女', '1', '男') as gender,
                       s1.idcardnumber,
                       s1.cellphone,
                       ''                                    as payrollbankcardnumber,
                       ''                                    as payRollBankName,
                       null                                  as unitprice,
                       nvl(e.attcount, 0)                    as count
                FROM b_ps04 s2,
                     b_ps01 s1,
                     (select d.user_id, count(0) attcount
                      from (select to_char(c.checkdate, 'yyyy-MM-dd') attday, c.user_id
                            from b_kq02 c
                            where to_char(c.checkdate, 'yyyy-MM') = #{month}
                              and c.pj0101 = #{pj0101}
                            group by to_char(c.checkdate, 'yyyy-MM-dd'), c.user_id) d
                      group by d.user_id) e
                where s2.pj0101 = #{pj0101}
                  and s1.ps0101 = s2.ps0101
                  and s2.PS0401 = e.USER_ID(+)
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
                )
            </otherwise>
        </choose>
    </select>

    <select id="getWorkerRecordDTOList" resultType="io.renren.modules.enterprise.excelexp.dto.WorkerRecordDTO">
        <choose>
            <when test="personType != null and personType != ''and personType == 1">
                select (SELECT m.teamname FROM b_tm01 m WHERE m.pj0101 = t.pj0101 and m.tm0101 = s2.tm0101) teamname,
                       s1.name,
                       decode(S1.Gender, '2', '女', '1', '男')                                                gender,
                       s1.idcardnumber,
                       s1.cellphone,
                       s3.unitprice,
                       (SELECT a.dict_label
                        FROM sys_dict_data a,
                             sys_dict_type c
                        where a.dict_type_id = c.id
                          and c.dict_type = 'WORKTYPECODE'
                          and a.dict_value = s2.worktypecode)                                               worktypecode,
                       to_char(s2.entrytime, 'yyyy-MM-dd')                                                  starttime,
                       to_char(s2.exittime, 'yyyy-MM-dd')                                                   endtime
                FROM r_pj01_dept t,
                     b_ps02 s2,
                     b_ps01 s1,
                     b_ps03 s3
                where s2.pj0101 = t.pj0101
                  and s1.ps0101 = s2.ps0101
                  and s2.ps0201 = s3.ps0201
                <if test="tm0101 != null and tm0101 != ''">
                    and s2.tm0101 = #{tm0101}
                </if>
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
                <!--and #{month} >= to_char(s2.entrytime, 'yyyymm')
                and nvl(to_char(s2.exittime, 'yyyymm'), #{month}) >= #{month}-->
                and t.dept_id = #{deptId}
                order by starttime
            </when>
            <when test="personType != null and personType != ''and personType == 2">
                select '管理人员'                                teamname,
                       s1.name,
                       decode(S1.Gender, '2', '女', '1', '男') gender,
                       s1.idcardnumber,
                       s1.cellphone,
                       null                                  unitprice,
                       (SELECT a.dict_label
                        FROM sys_dict_data a,
                             sys_dict_type c
                        where a.dict_type_id = c.id
                          and c.dict_type = 'MANAGETYPE'
                          and a.dict_value = s2.managetype)  worktypecode,
                       to_char(s2.entrytime, 'yyyy-MM-dd')   starttime,
                       to_char(s2.exittime, 'yyyy-MM-dd')    endtime
                FROM r_pj01_dept t,
                     b_ps04 s2,
                     b_ps01 s1
                where s2.pj0101 = t.pj0101
                  and s1.ps0101 = s2.ps0101
                <!-- and #{month} >= to_char(s2.entrytime, 'yyyymm')
                 and nvl(to_char(s2.exittime, 'yyyymm'), #{month}) >= #{month}-->
                and t.dept_id = #{deptId}
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
                order by starttime
            </when>
            <otherwise>
                select * from (
                ( select (SELECT m.teamname
                          FROM b_tm01 m
                          WHERE m.pj0101 = t.pj0101
                            and m.tm0101 = s2.tm0101)           teamname,
                         s1.name,
                         decode(S1.Gender, '2', '女', '1', '男')  gender,
                         s1.idcardnumber,
                         s1.cellphone,
                         s3.unitprice,
                         (SELECT a.dict_label
                          FROM sys_dict_data a,
                               sys_dict_type c
                          where a.dict_type_id = c.id
                            and c.dict_type = 'WORKTYPECODE'
                            and a.dict_value = s2.worktypecode) worktypecode,
                         to_char(s2.entrytime, 'yyyy-MM-dd')    starttime,
                         to_char(s2.exittime, 'yyyy-MM-dd')     endtime
                  FROM r_pj01_dept t,
                       b_ps02 s2,
                       b_ps01 s1,
                       b_ps03 s3
                where s2.pj0101 = t.pj0101
                  and s1.ps0101 = s2.ps0101
                  and s2.ps0201 = s3.ps0201
                <if test="tm0101 != null and tm0101 != ''">
                    and s2.tm0101 = #{tm0101}
                </if>
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
                <!-- and #{month} >= to_char(s2.entrytime, 'yyyymm')
                 and nvl(to_char(s2.exittime, 'yyyymm'), #{month}) >= #{month}-->
                and t.dept_id = #{deptId})
                union all
                (
                select '管理人员'                                teamname,
                       s1.name,
                       decode(S1.Gender, '2', '女', '1', '男') gender,
                       s1.idcardnumber,
                       s1.cellphone,
                       null                                  unitprice,
                       (SELECT a.dict_label
                        FROM sys_dict_data a,
                             sys_dict_type c
                        where a.dict_type_id = c.id
                          and c.dict_type = 'MANAGETYPE'
                          and a.dict_value = s2.managetype)  worktypecode,
                       to_char(s2.entrytime, 'yyyy-MM-dd')   starttime,
                       to_char(s2.exittime, 'yyyy-MM-dd')    endtime
                FROM r_pj01_dept t,
                     b_ps04 s2,
                     b_ps01 s1
                where s2.pj0101 = t.pj0101
                  and s1.ps0101 = s2.ps0101
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
                <!-- and #{month} >= to_char(s2.entrytime, 'yyyymm')
                 and nvl(to_char(s2.exittime, 'yyyymm'), #{month}) >= #{month}-->
                and t.dept_id = #{deptId})
                ) s
                order by starttime
            </otherwise>
        </choose>
    </select>

    <select id="getAttendanceRecordDTOList"
            resultType="io.renren.modules.enterprise.excelexp.dto.AttendanceRecordDTO">
        select decode(day1, null, 0, 1) +
               decode(day2, null, 0, 1) +
               decode(day3, null, 0, 1) +
               decode(day4, null, 0, 1) +
               decode(day5, null, 0, 1) +
               decode(day6, null, 0, 1) +
               decode(day7, null, 0, 1) +
               decode(day8, null, 0, 1) +
               decode(day9, null, 0, 1) +
               decode(day10, null, 0, 1) +
               decode(day11, null, 0, 1) +
               decode(day12, null, 0, 1) +
               decode(day13, null, 0, 1) +
               decode(day14, null, 0, 1) +
               decode(day15, null, 0, 1) +
               decode(day16, null, 0, 1) +
               decode(day17, null, 0, 1) +
               decode(day18, null, 0, 1) +
               decode(day19, null, 0, 1) +
               decode(day20, null, 0, 1) +
               decode(day21, null, 0, 1) +
               decode(day22, null, 0, 1) +
               decode(day23, null, 0, 1) +
               decode(day24, null, 0, 1) +
               decode(day25, null, 0, 1) +
               decode(day26, null, 0, 1) +
               decode(day27, null, 0, 1) +
               decode(day28, null, 0, 1) +
               decode(day29, null, 0, 1) +
               decode(day30, null, 0, 1) +
               decode(day31, null, 0, 1)
                                            counts,
               teamname,
               name,
               idcardnumber,
               decode(day1, null, '', '√')  day1,
               decode(day2, null, '', '√')  day2,
               decode(day3, null, '', '√')  day3,
               decode(day4, null, '', '√')  day4,
               decode(day5, null, '', '√')  day5,
               decode(day6, null, '', '√')  day6,
               decode(day7, null, '', '√')  day7,
               decode(day8, null, '', '√')  day8,
               decode(day9, null, '', '√')  day9,
               decode(day10, null, '', '√') day10,
               decode(day11, null, '', '√') day11,
               decode(day12, null, '', '√') day12,
               decode(day13, null, '', '√') day13,
               decode(day14, null, '', '√') day14,
               decode(day15, null, '', '√') day15,
               decode(day16, null, '', '√') day16,
               decode(day17, null, '', '√') day17,
               decode(day18, null, '', '√') day18,
               decode(day19, null, '', '√') day19,
               decode(day20, null, '', '√') day20,
               decode(day21, null, '', '√') day21,
               decode(day22, null, '', '√') day22,
               decode(day23, null, '', '√') day23,
               decode(day24, null, '', '√') day24,
               decode(day25, null, '', '√') day25,
               decode(day26, null, '', '√') day26,
               decode(day27, null, '', '√') day27,
               decode(day28, null, '', '√') day28,
               decode(day29, null, '', '√') day29,
               decode(day30, null, '', '√') day30,
               decode(day31, null, '', '√') day31
        from (
        with temp as
        (
        <choose>
            <when test="personType != null and personType != ''and personType == 1">
                SELECT (SELECT m.teamname FROM b_tm01 m WHERE m.pj0101 = p.pj0101 and m.tm0101 = s2.tm0101) teamname,
                       s1.name,
                       s1.idcardnumber,
                       substr(to_char(k.checkdate, 'yyyymmdd'), 7)                                          checkdate
                FROM b_kq02 k,
                     r_pj01_dept p,
                     b_ps02 s2,
                     b_ps01 s1
                WHERE k.pj0101 = p.pj0101
                  and s2.pj0101 = p.pj0101
                  and s1.ps0101 = s2.ps0101
                  and k.user_id = s2.ps0201
                <if test="tm0101 != null and tm0101 != ''">
                    and s2.tm0101 = #{tm0101}
                </if>
                and p.dept_id = #{deptId}
                and to_char(k.checkdate, 'yyyy-MM') = #{month}
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
                <!-- and #{month} >= to_char(s2.entrytime,'yyyymm')
                and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month} -->
            </when>
            <when test="personType != null and personType != ''and personType == 2">
                SELECT
                '管理人员' teamname,
                s1.name,
                s1.idcardnumber,
                substr(to_char(k.checkdate,'yyyymmdd'),7) checkdate
                FROM b_kq02 k, r_pj01_dept p, b_ps04 s2, b_ps01 s1
                WHERE k.pj0101 = p.pj0101
                  and s2.pj0101 = p.pj0101
                  and s1.ps0101 = s2.ps0101
                  and k.user_id = s2.ps0401
                  and p.dept_id = #{deptId}
                  and to_char( k.checkdate
                    , 'yyyy-MM') = #{month}
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
                <!-- and #{month} >= to_char(s2.entrytime,'yyyymm')
                 and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month}-->
            </when>
            <otherwise>
                SELECT
                    (SELECT m.teamname FROM b_tm01 m WHERE m.pj0101 = p.pj0101 and m.tm0101 = s2.tm0101) teamname,
                    s1.name,
                    s1.idcardnumber,
                    substr(to_char(k.checkdate, 'yyyymmdd'), 7) checkdate
                FROM b_kq02 k, r_pj01_dept p, b_ps02 s2, b_ps01 s1
                WHERE k.pj0101 = p.pj0101
                  and s2.pj0101 = p.pj0101
                  and s1.ps0101 = s2.ps0101
                  and k.user_id = s2.ps0201
                <if test="tm0101 != null and tm0101 != ''">
                    and s2.tm0101 = #{tm0101}
                </if>
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
                and p.dept_id = #{deptId}
                and to_char( k.checkdate
                  , 'yyyy-MM') = #{month}
                <!-- and #{month} >= to_char(s2.entrytime,'yyyymm')
                and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month} -->
                union all
                SELECT
                    '管理人员' teamname,
                    s1.name,
                    s1.idcardnumber,
                    substr(to_char(k.checkdate, 'yyyymmdd'), 7) checkdate
                FROM b_kq02 k, r_pj01_dept p, b_ps04 s2, b_ps01 s1
                WHERE k.pj0101 = p.pj0101
                  and s2.pj0101 = p.pj0101
                  and s1.ps0101 = s2.ps0101
                  and k.user_id = s2.ps0401
                  and p.dept_id = #{deptId}
                  and to_char( k.checkdate
                    , 'yyyy-MM') = #{month}
                <if test="inOrOut != null and inOrOut != ''">
                    and s2.IN_OR_OUT = #{inOrOut}
                </if>
                <!-- and #{month} >= to_char(s2.entrytime,'yyyymm')
                and nvl(to_char(s2.exittime,'yyyymm'),#{month}) >= #{month} -->
            </otherwise>
        </choose>
        )
        select *
        from (select teamname, name, idcardnumber, checkdate
              from temp) pivot ( max(checkdate) for checkdate in
            ( '01' as day1,
            '02' as day2,
            '03' as day3,
            '04' as day4,
            '05' as day5,
            '06' as day6,
            '07' as day7,
            '08' as day8,
            '09' as day9,
            '10' as day10,
            '11' as day11,
            '12' as day12,
            '13' as day13,
            '14' as day14,
            '15' as day15,
            '16' as day16,
            '17' as day17,
            '18' as day18,
            '19' as day19,
            '20' as day20,
            '21' as day21,
            '22' as day22,
            '23' as day23,
            '24' as day24,
            '25' as day25,
            '26' as day26,
            '27' as day27,
            '28' as day28,
            '29' as day29,
            '30' as day30,
            '31' as day31 ))

        )
    </select>

    <select id="getMonthDay" resultType="io.renren.modules.enterprise.excelexp.dto.MonthDayDTO">
        SELECT to_char(TO_DATE(#{month}, 'yyyy-MM') + (ROWNUM - 1), 'yyyy-MM-dd') as dayId,
               '-1'                                                               as unId,
               'day' || rownum                                                    as dayName,
               'hours' || rownum                                                  as hours
        FROM DUAL
        CONNECT BY ROWNUM &lt;=
                   TO_NUMBER(TO_CHAR(LAST_DAY(TO_DATE(#{month}, 'yyyy-MM')), 'dd'))
    </select>

    <select id="getExportAttendanceList" resultType="io.renren.modules.enterprise.excelexp.dto.AttendanceRecordDTO">
        select t1.*,(
        <foreach collection="monthDayList" item="item" index="index" open="" separator="+" close="">
            nvl(t1.${item.hours}, 0)
        </foreach>
        ) as hoursTotal,
        (
        <foreach collection="monthDayList" item="item" index="index" open="" separator="+" close="">
            decode(t1.${item.dayName}, '', 0, 1)
        </foreach>
        ) as attTotal
        from ( select *
        from (select t.pj0101,
                     a.personId,
                     a.TEAMNAME,
                     a.in_or_out,
                     b.name,
                     b.idcardnumber,
                     a.userType,
                     a.TM0101,
                     '出勤' as isAtt,
                     '工时' as workingHours
              from b_pj01 t,
                   (select f.ps0201 as personId, f.pj0101, f.PS0101, k.teamname, f.IN_OR_OUT, '1' as userType, f.TM0101
                    from b_ps02 f,
                         b_tm01 k
                    where f.tm0101 = k.tm0101
                    union all
                    select h.ps0401 as personId,
                           h.pj0101,
                           h.PS0101,
                           '管理人员'   as teamname,
                           h.IN_OR_OUT,
                           '2'      as userType,
                           -2       as tm0101
                    from b_ps04 h) a,
                   b_ps01 b,
                   r_pj01_dept c
        where t.pj0101 = c.PJ0101
          and t.pj0101 = a.pj0101
          and a.PS0101 = b.ps0101
          and c.DEPT_ID = #{params.deptId}
        <if test="params.tm0101 != null and params.tm0101 != ''">
            and a.tm0101 = #{params.tm0101}
        </if>
        <if test="params.inOrOut != null and params.inOrOut != ''">
            and a.IN_OR_OUT = #{params.inOrOut}
        </if>
        <if test="params.personType != null and params.personType != ''">
            and a.userType = #{params.personType}
        </if>
        order by a.TM0101, a.in_or_out) m
        <foreach collection="monthDayList" item="item" index="index" open="" separator="" close="">
            left join (select p.user_id,
                              decode(sign(count(0)), 1, '√') as ${item.dayName}
                       from b_kq02 p
                       where to_char(p.checkdate, 'yyyy-MM-dd') = #{item.dayId}
                       group by p.user_id) n
                      on n.user_id = m.personId
        </foreach>
        <foreach collection="monthDayList" item="item" index="index" open="" separator="" close="">
            left join (select f.user_id, fun_attendance_hours(f.user_id, #{item.dayId}) as ${item.hours}
                       from b_kq02 f
                       where to_char(f.checkdate, 'yyyy-MM-dd') = #{item.dayId}
                       group by f.user_id) e on e.user_id = m.personId
        </foreach>
        ) t1
    </select>

    <select id="selectAttExcel" resultType="io.renren.modules.enterprise.excelexp.dto.AttendanceExcel">
        select * from(
        select b.userid,
               b.teamname,
               a.name,
               a.idcardnumber,
               b.in_or_out,
               b.userType,
               '出勤'                                                  as isAtt,
               '工时'                                                  as workingHours,
               b.tm0101,
               FUN_ATTENDANCE_HOURS_TOTAL(b.userid, #{params.month}) as hoursTotal,
               FUN_PERSON_ATTENDANCE_DAY(b.userid, #{params.month})  as attTotal,
        <foreach collection="monthDayList" item="item" index="index" open="" separator="," close=",">
            FUN_PERSON_HASATT(b.userId, #{item.dayId}) as ${item.dayName}
        </foreach>
        <foreach collection="monthDayList" item="item" index="index" open="" separator="," close="">
            FUN_ATTENDANCE_HOURS(b.userId,
                #{item.dayId}) as
                ${item.hours}
        </foreach>
        from b_ps01 a,
            (select a1.ps0101,
            a1.pj0101,
            a1.ps0201 as userId,
            a1.in_or_out,
            a3.teamname,
            '1' as userType,
            a3.tm0101
            from b_ps02 a1,
            b_tm01 a3
            where a1.tm0101 = a3.tm0101
            union all
            select a2.ps0101,
            a2.pj0101,
            a2.ps0401 as userId,
            a2.in_or_out,
            '管理人员' as teamname,
            '2' as userType,
            -1
            from b_ps04 a2) b,
            b_pj01 d
        where a.ps0101 = b.ps0101
          and b.pj0101 = d.pj0101
          and d.dept_id = #{params.deptId}) p
        <where>
            <if test="params.tm0101 != null and params.tm0101 != ''">
                p.tm0101 = #{params.tm0101}
            </if>
            <if test="params.inOrOut != null and params.inOrOut != ''">
                and p.IN_OR_OUT = #{params.inOrOut}
            </if>
            <if test="params.personType != null and params.personType != ''">
                and p.userType = #{params.personType}
            </if>
        </where>
    </select>

    <select id="selectPersonInfo" resultType="io.renren.modules.enterprise.excelexp.dto.AttendanceExcel">
        select b.userid,
               a.name,
               a.idcardnumber,
               b.in_or_out,
               b.userType,
               '出勤' as isAtt,
               '工时' as workingHours,
               b.tm0101,
               b.teamname,
               b.payRollBankCardNumber,
               b.payRollBankName
        from b_ps01 a
            inner join (select a1.ps0101,
                               a1.pj0101,
                               a1.ps0201                as userId,
                               a1.in_or_out,
                               a3.teamname,
                               '1'                      as userType,
                               a1.PAYROLLBANKNAME       as payRollBankName,
                               a1.PAYROLLBANKCARDNUMBER as payRollBankCardNumber,
                               a3.tm0101
                        from b_ps02 a1,
                             b_tm01 a3
                        where a1.tm0101 = a3.tm0101
                          and a1.PJ0101 = #{pj0101}
                        union all
                        select a2.ps0101,
                               a2.pj0101,
                               a2.ps0401 as userId,
                               a2.in_or_out,
                               '管理人员'    as teamname,
                               '2'       as userType,
                               ''        as payRollBankName,
                               ''        as payRollBankCardNumber,
                               -1
                        from b_ps04 a2
                        where a2.PJ0101 = #{pj0101}) b
        on a.ps0101 = b.ps0101
        <if test="tm0101 != null and tm0101 != ''">
            and b.tm0101 = #{tm0101}
        </if>
        <if test="inOrOut != null and inOrOut != ''">
            and b.IN_OR_OUT = #{inOrOut}
        </if>
        <if test="personType != null and personType != ''">
            and b.userType = #{personType}
        </if>
    </select>

    <select id="selectPersonAttendance" resultType="io.renren.modules.enterprise.excelexp.dto.PersonAttendance">
        select to_char(b.checkdate, 'yyyy-MM-dd') as attDay,
               b.user_id                          as userId,
               count(0)                           as attCount,
               max(b.checkdate)                   as endDate,
               min(b.checkdate)                   as startDate
        from b_kq02 b
        where to_char(b.checkdate, 'yyyy-MM') = #{month}
          and b.PJ0101 = #{pj0101}
        group by to_char(b.checkdate, 'yyyy-MM-dd'), b.user_id
    </select>
</mapper>