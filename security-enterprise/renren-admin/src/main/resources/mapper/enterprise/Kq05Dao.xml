<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.kq05.dao.Kq05Dao">
    <select id="getKq05Data" resultType="io.renren.modules.enterprise.kq05.dto.Kq05ListDTO">
        SELECT *
        FROM (
                 SELECT s1.name   username,
                        s2.ps0201 userId,
                        p.name    projectname,
                        s1.idcardnumber,
                        '1'       personType,
                        s1.cellphone,
                        s2.PJ0101
                 FROM b_ps02 s2,
                      b_ps01 s1,
                      b_pj01 p,
                      R_PJ01_DEPT d
                 WHERE s1.PS0101 = s2.PS0101
                   AND s2.PJ0101 = p.PJ0101
                   AND p.PJ0101 = d.PJ0101
                   AND s2.pj0101 = d.pj0101
                   and d.DEPT_ID = #{deptId}
                 UNION ALL
                 SELECT s1.name   username,
                        s2.ps0401 userId,
                        p.name    projectname,
                        s1.idcardnumber,
                        '2'       personType,
                        s1.cellphone,
                        s2.PJ0101
                 FROM b_ps04 s2,
                      b_ps01 s1,
                      b_pj01 p,
                      R_PJ01_DEPT d
                 WHERE s1.PS0101 = s2.PS0101
                   AND s2.PJ0101 = p.PJ0101
                   AND p.PJ0101 = d.PJ0101
                   AND s2.pj0101 = d.pj0101
                   and d.DEPT_ID = #{deptId}
                 union all
                 select a1.name           as username,
                        a1.id             as userId,
                        a2.name           as projectname,
                        a1.id_card_number as idcardnumber,
                        '3'               as personType,
                        a1.cellphone,
                        a1.pj0101
                 from b_person_temp a1,
                      b_pj01 a2,
                      r_pj01_dept a3
                 where a1.pj0101 = a2.pj0101
                   and a1.pj0101 = a3.pj0101
                   and a3.dept_id = #{deptId}
             ) temp
        <where>
            <if test="pj0101 != null and pj0101 != ''">
                temp.PJ0101 = #{pj0101}
            </if>
            <if test="username != null and username != ''">
                and temp.USERNAME like '%' || #{username} || '%'
            </if>
            <if test="cmdCode != null and cmdCode != '' or status != null and status != ''">
                and temp.userid in
                (select t.user_id
                 from b_kq05 t
                <where>
                    <if test="status != null and status != ''">
                        t.status = #{status}
                    </if>
                    <if test="cmdCode != null and cmdCode != ''">
                        and t.cmd_code = #{cmdCode}
                    </if>
                    )
                </where>
            </if>
        </where>
    </select>
    <select id="getKq05Detail" resultType="io.renren.modules.enterprise.kq05.dto.Kq05DTO">
        SELECT *
        FROM B_KQ05 k
        WHERE k.USER_ID = #{userId}
        order by k.CREATE_DATE DESC
    </select>
</mapper>