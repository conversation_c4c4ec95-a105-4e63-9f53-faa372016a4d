<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.dataShare.facilitatorProject.dao.FacilitatorProjectDao">
    <resultMap type="io.renren.modules.enterprise.dataShare.facilitatorProject.entity.FacilitatorProjectEntity"
               id="bFacilitatorProjectMap">
        <result property="id" column="ID"/>
        <result property="facilitatorId" column="FACILITATOR_ID"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="whether" column="WHETHER"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <delete id="deleteByFacilitatorId">
        delete
        from B_FACILITATOR_PROJECT a
        where a.FACILITATOR_ID = #{id}
    </delete>

    <select id="selectByFacilitatorId"
            resultType="io.renren.modules.enterprise.dataShare.facilitatorProject.dto.FacilitatorProjectInfo">
        select b.PJ0101, b.NAME as projectName
        from B_FACILITATOR_PROJECT a
                 inner join b_pj01 b on a.PJ0101 = b.PJ0101
        where a.FACILITATOR_ID = #{facilitatorId}
        order by b.AREACODE
    </select>
</mapper>