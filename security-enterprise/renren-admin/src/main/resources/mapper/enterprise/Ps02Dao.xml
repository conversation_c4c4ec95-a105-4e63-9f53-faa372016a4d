<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.ps02.dao.Ps02Dao">
    <!-- 工人列表查询-->
    <select id="getListData" resultType="io.renren.modules.enterprise.ps02.dto.Ps02PageDTO">
        select a.ps0201,
               t.name         as projectName,
               a.tm0101,
               c.TEAMNAME     as teamName,
               f.corpname     as corpName,
               b.name         as personName,
               b.idcardnumber as idCardNumber,
               a.isteamleader as isTeamLeader,
               b.cellphone    as cellPhone,
               a.worktypecode as workTypeCode,
               a.entrytime    as entryTime,
               a.exittime     as exitTime,
               a.in_or_out    as inOrOut,
               a.PJ0101
        from b_ps02 a
            inner join b_pj01 t
                       on a.pj0101 = t.pj0101
            inner join R_PJ01_DEPT d
                       on t.pj0101 = d.pj0101 and d.DEPT_ID = #{deptId}
            inner join b_tm01 c
        on a.tm0101 = c.tm0101
        <if test="cp0201 != null and cp0201 != ''">
            and c.cp0201 = #{cp0201}
        </if>
        inner join b_cp02 e
                   on c.cp0201 = e.cp0201
        inner join b_cp01 f
                   on e.cp0101 = f.cp0101
        inner join b_ps01 b
        on a.ps0101 = b.ps0101
        <if test="personName != null and personName != ''">
            and b.NAME like '%' || #{personName} || '%'
        </if>
        <where>
            <if test="tm0101 != null and tm0101 != ''">
                a.TM0101 = #{tm0101}
            </if>
            <if test="inOrOut != null and inOrOut != ''">
                and a.IN_OR_OUT = #{inOrOut}
            </if>
            <if test="pj0101 != null and pj0101 != ''">
                and a.PJ0101 = #{pj0101}
            </if>
        </where>
        order by a.PS0201 desc, a.ENTRYTIME desc
    </select>
    <select id="selectAllCount" resultType="java.lang.Long">
        select count(a.PS0201)
        from b_ps02 a
        <if test="personName != null and personName != ''">
            inner join b_ps01 c
                       on a.ps0101 = c.ps0101 and c.NAME like '%' || #{personName} || '%'
        </if>
        <if test="cp0201 != null and cp0201 != ''">
            inner join b_tm01 d
                       on a.tm0101 = d.tm0101 and d.CP0201 = #{cp0201}
        </if>
        inner join
        R_PJ01_DEPT b on a.PJ0101 = b.PJ0101
            and b.DEPT_ID = #{deptId}
        <if test="inOrOut != null and inOrOut != ''">
            and a.IN_OR_OUT = #{inOrOut}
        </if>
        <if test="pj0101 != null and pj0101 != ''">
            and a.PJ0101 = #{pj0101}
        </if>
        <if test="tm0101 != null and tm0101 != ''">
            and a.TM0101 = #{tm0101}
        </if>
    </select>
    <update id="updateInOrOutByIds">
        update B_PS02 t
        <set>
            t.IN_OR_OUT = #{type},
            <choose>
                <when test="type == '2'.toString()">
                    t.EXITTIME = to_date(to_char(sysdate, 'yyyy-MM-dd'), 'yyyy-MM-dd'),
                </when>
                <otherwise>
                    t.ENTRYTIME = to_date(to_char(sysdate, 'yyyy-MM-dd'), 'yyyy-MM-dd'),
                    t.EXITTIME=''
                </otherwise>
            </choose>
        </set>
        where t.PS0201 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>

    <select id="selectPersonByIds" resultType="io.renren.modules.enterprise.kq05.dto.PersonDTO">
        select a.NAME, b.PS0201 userId, b.ISSUECARDPICURL imageUrl, b.PJ0101
        from b_ps01 a,
             b_ps02 b where b.PS0101 = a.PS0101
                        and b.PS0201 in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>
    <select id="selectCountPs0101" resultType="java.lang.Integer">
        select count(0)
        from (select t.ps0101, t.pj0101
              from b_ps02 t
              union all
              select a.ps0101, a.pj0101
              from b_ps04 a) c
        where c.pj0101 = #{pj0101}
          and c.ps0101 = #{ps0101}
    </select>
    <select id="exportContract" resultType="io.renren.modules.enterprise.ps02.dto.Ps02ContractDTO">
        select t.ps0201,
               e.name                                projectname,
               a.birthday,
               d.corpname,
               d.legalman,
               d.address                             corpaddress,
               a.name,
               decode(a.gender, 1, '男', 2, '女')      gender,
               a.idcardnumber,
               a.address,
               a.cellphone,
               d.corpcode,
               (select g.dict_label
                from sys_dict_type f,
                     sys_dict_data g
                where f.dict_type = 'WORKTYPECODE'
                  and f.id = g.dict_type_id
                  and g.dict_value = t.worktypecode) worktypecode,
               d.linkcellphone
        from b_ps02 t,
             b_ps01 a,
             b_tm01 b,
             b_cp02 c,
             b_cp01 d,
             b_pj01 e
        where t.tm0101 = b.tm0101
          and t.ps0101 = a.ps0101
          and t.pj0101 = e.pj0101
          and b.cp0201 = c.cp0201
          and c.cp0101 = d.cp0101
          and t.ps0201 = #{ps0201}
    </select>
    <select id="getEmpRecordListData" resultType="io.renren.modules.enterprise.ps02.dto.Ps02EmpRecordDTO">
        select (select b.name from b_pj01 b where b.pj0101 = t.pj0101)     projectName,
               a.name,
               a.idcardnumber                                              idCardNumber,
               t.worktypecode                                              workTypeCode,
               (select c.teamname from b_tm01 c where c.tm0101 = t.tm0101) teamName,
               t.entrytime                                                 entryTime,
               t.exittime                                                  exitTime,
               t.in_or_out                                                 inOrOut
        from b_ps02 t,
             b_ps01 a
        where t.ps0101 = a.ps0101
          and t.ps0101 = #{ps0101}
    </select>
    <!--人员信息导出-->
    <select id="selectPersonExcelList" resultType="io.renren.modules.enterprise.ps02.excel.Ps02Excel">
        select a.NAME,
               a.IDCARDNUMBER                        as idCardNumber,
               f.CORPNAME                            as businessName,
               d.TEAMNAME                            as teamName,
               (select m.DICT_LABEL
                from SYS_DICT_DATA m
                where m.DICT_TYPE_ID = '206'
                  and m.DICT_VALUE = b.WORKTYPECODE) as workType,
               (select m.DICT_LABEL
                from SYS_DICT_DATA m
                where m.DICT_TYPE_ID = '1160061077912858625'
                  and m.DICT_VALUE = a.GENDER)       as gender,
               (select m.DICT_LABEL
                from SYS_DICT_DATA m
                where m.DICT_TYPE_ID = '1'
                  and m.DICT_VALUE = a.NATION)       as nation,
               a.ADDRESS,
               a.CELLPHONE,
               to_char(b.ENTRYTIME, 'yyyy-MM-dd')    as entryTime,
               to_char(b.EXITTIME, 'yyyy-MM-dd')     as exitTime
        from b_ps01 a,
             b_ps02 b,
             R_PJ01_DEPT c,
             b_tm01 d,
             b_cp02 e,
             b_cp01 f
        where a.PS0101 = b.PS0101
          and b.PJ0101 = c.PJ0101
          and b.TM0101 = d.TM0101
          and d.CP0201 = e.CP0201
          and e.CP0101 = f.CP0101
          and c.DEPT_ID = #{deptId}
        <if test="tm0101 != null and tm0101 != ''">
            and d.TM0101 = #{tm0101}
        </if>
        <if test="cp0201 != null and cp0201 != ''">
            and e.cp0201 = #{cp0201}
        </if>
        <if test="personName != null and personName != ''">
            and a.NAME like '%' || #{personName} || '%'
        </if>
        order by b.IN_OR_OUT, b.ps0201 desc
    </select>
    <select id="getNoAttendanceWorkerByDay" resultType="io.renren.modules.enterprise.ps02.dto.Ps02DTO">
        select a.PS0201 as ps0201, a.PJ0101 as pj0101
        from b_ps02 a
        where a.pj0101 = #{pj0101}
            and a.ENTRYTIME &lt; trunc(sysdate) - #{exitdayparam}
            and a.EXITTIME is null
            and not exists (select 1
                        from b_kq02 k
                        where k.PJ0101 = #{pj0101}
                            and k.CHECKDATE >= trunc(sysdate) - #{exitdayparam}
                            and USER_ID = a.PS0201
                            and PERSON_TYPE = '1')
            and rownum &lt; 1000
    </select>
</mapper>