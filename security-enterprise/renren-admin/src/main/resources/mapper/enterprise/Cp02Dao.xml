<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.cp02.dao.Cp02Dao">
    <!--    通用的查询项目下边的参建单位信息SQL-->
    <select id="loadCp02Info" resultType="io.renren.common.common.dto.CommonDto">
        SELECT a.CP0201                                                       value,
               b.CORPNAME || ' (' || (SELECT c.DICT_LABEL
                                      FROM SYS_DICT_TYPE d,
                                           SYS_DICT_DATA c
                                      WHERE d.ID = c.DICT_TYPE_ID
                                        AND d.DICT_TYPE = 'CORPTYPE'
                                        AND c.DICT_VALUE = a.CORPTYPE) || ')' label
        FROM B_CP02 a,
             b_cp01 b,
             R_PJ01_DEPT t
        WHERE a.PJ0101 = t.PJ0101
          AND a.CP0101 = b.CP0101
          and t.DEPT_ID = #{deptId}
    </select>
    <!--    参建单位列表分页查询SQL-->
    <select id="pageList" resultType="io.renren.modules.enterprise.cp02.dto.Cp02PageDTO">
        select t.cp0201,
               (select c.NAME from b_pj01 c where c.PJ0101 = t.PJ0101) name,
               b.corpname,
               b.corpcode,
               b.linkman,
               b.linkcellphone,
               t.corptype
        from b_cp02 t,
             r_pj01_dept a,
             b_cp01 b
        where t.pj0101 = a.pj0101
          and t.cp0101 = b.cp0101
          and a.dept_id = #{deptId}
        <if test="corpname != null and corpname != ''">
            and b.CORPNAME like #{corpname}
        </if>
        <if test="pj0101 != null and pj0101 != ''">
            and t.PJ0101 = #{pj0101}
        </if>
    </select>
    <!--    查询参建单位的数量-->
    <select id="getParticipantsUnitsCount" resultType="java.lang.Integer">
        select count(0)
        from B_CP02 t
        where t.PJ0101 = #{pj0101}
          and t.CORPTYPE = #{corpType}
    </select>

    <select id="loadJobType" resultType="io.renren.common.common.dto.CommonDto">
        select t.jobtype value, t.jobtypename label
        from SYS_JOBTYPE t
        where t.corptype = #{corptype}
        order by t.jobtype
    </select>
    <select id="selectCompany" resultType="io.renren.modules.enterprise.cp02.entity.Cp02Entity">
        select *
        from b_cp02 t
        where t.pj0101 = #{pj0101}
          and (t.corptype = '9' or t.corptype = '8')
    </select>

    <select id="getCorpList" resultType="io.renren.modules.enterprise.cp02.dto.Cp02LabelAndValueDTO">
        select t.cp0201   as value,
               b.corpname as label
        from b_cp02 t,
             r_pj01_dept a,
             b_cp01 b
        where t.pj0101 = a.pj0101
          and t.cp0101 = b.cp0101
          and a.dept_id = #{deptId}
    </select>

    <select id="selectByCorpCode" resultType="int">
        select count(0)
        from b_cp01 a,
             b_cp02 b
        where a.cp0101 = b.cp0101
          and b.PJ0101 = #{pj0101}
          and a.CORPCODE = #{corpCode}
    </select>
</mapper>