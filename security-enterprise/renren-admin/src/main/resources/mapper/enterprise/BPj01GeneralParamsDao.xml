<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj01GeneralParams.dao.BPj01GeneralParamsDao">

    <resultMap type="io.renren.modules.enterprise.pj01GeneralParams.entity.BPj01GeneralParamsEntity" id="bPj01GeneralParamsMap">
        <result property="pj0101" column="PJ0101"/>
        <result property="exitdayparam" column="EXITDAYPARAM"/>
        <result property="exitdayswitch" column="EXITDAYSWITCH"/>
    </resultMap>

    <select id="getListData" resultType="io.renren.modules.enterprise.pj01GeneralParams.dto.BPj01GeneralParamsDTO">
        select
            b.NAME, a.PJ0101, a.EXITDAYPARAM, a.EXITDAYSWITCH
        from B_PJ01_GENERAL_PARAMS a
            left join R_PJ01_DEPT r on a.PJ0101 = r.PJ0101
            left join B_PJ01 b on b.PJ0101 = a.PJ0101
        where r.DEPT_ID = #{deptId}
            <if test="pj0101 != null and pj0101 != ''">
                and b.pj0101 = #{pj0101}
            </if>
        order by a.PJ0101 desc
    </select>

    <select id="getGeneralParamsInfo"
            resultType="io.renren.modules.enterprise.pj01GeneralParams.dto.BPj01GeneralParamsDTO">
        select
            b.NAME, a.PJ0101, a.EXITDAYPARAM, a.EXITDAYSWITCH
        from B_PJ01_GENERAL_PARAMS a
            left join R_PJ01_DEPT r on a.PJ0101 = r.PJ0101
            left join B_PJ01 b on b.PJ0101 = a.PJ0101
        where r.DEPT_ID = #{deptId}
            and a.PJ0101 = #{pj0101}
    </select>
    <select id="getWorkerExitProject"
            resultType="io.renren.modules.enterprise.pj01GeneralParams.dto.BPj01GeneralParamsDTO">
        select t.PJ0101, pg.EXITDAYSWITCH, pg.EXITDAYPARAM
        from B_PJ01 t,
             B_PJ01_GENERAL_PARAMS pg
        where t.PJ0101 = pg.PJ0101
          and pg.EXITDAYSWITCH = '1'
    </select>

</mapper>