<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.pj01report.dao.BPj01ReportParamsDao">
    <select id="selectParamList" resultType="io.renren.modules.enterprise.pj01report.entity.BPj01ReportParamsEntity">
        select *
        from B_PJ01_REPORT_PARAMS a
        where a.id in (SELECT REGEXP_SUBSTR((select b.report_type
                                             from b_pj01_report b
                                             where b.pj0101 = #{pj0101}),
                                            '[^,]+',
                                            1,
                                            LEVEL) VALUE
                       FROM DUAL
                       CONNECT BY LEVEL &lt;= REGEXP_COUNT((select b.report_type
                                                            from b_pj01_report b
                                                            where b.pj0101 =
                                                                  #{pj0101}),
                                                           '[^,]+'))
    </select>

    <select id="selectReportParamList" resultType="io.renren.common.common.dto.CommonDto">
        select a.ID  value, a.REPORT_AREA_CODE  label  from B_PJ01_REPORT_PARAMS a
    </select>
</mapper>