<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.kq01.dao.Kq01Dao">
    <select id="getListData" resultType="io.renren.modules.enterprise.kq01.dto.Kq01PageDTO">
        select t.*, b.NAME as supplierName from (
        select b.NAME           as projectName,
               t.PJ0101,
               t.DEVICEKEY,
               t.DEVICESERIALNO,
               t.KQ0101,
               t.NOTE,
               t.TERMINALTYPE,
               t.NETWORK_STATUS as networkStatus,
               t.COM_REC_RANK   as comRecRank,
               t.KQ0701,
               t.CREATE_DATE
        from B_KQ01 t,
             R_PJ01_DEPT a,
             B_PJ01 b
        where a.PJ0101 = t.PJ0101
          and t.PJ0101 = b.PJ0101
          and a.DEPT_ID = #{deptId}
          and t.whether = '1'
        <if test="deviceserialno != null and deviceserialno != ''">
            and t.DEVICESERIALNO = #{deviceserialno}
        </if>
        <if test="pj0101 != null and pj0101 != ''">
            and b.PJ0101 = #{pj0101}
        </if>
        <if test="areaCode != null and areaCode != ''">
            and b.AREACODE like '' || #{areaCode} || '%'
        </if>
        ) t left join b_kq07 b on t.kq0701 = b.kq0701
    </select>
    <select id="selectByDevice" resultType="java.lang.Integer">
        select count(0)
        from B_KQ01 t
        where t.DEVICESERIALNO = #{devicemotion}
          and t.whether = '1'
    </select>
    <select id="selectByPj0101" resultType="io.renren.modules.enterprise.kq01.dto.Kq01DTO">
        select *
        from B_KQ01 t
        where t.PJ0101 = #{pj0101}
          and t.whether = '1'
    </select>
    <select id="selectDeviceListByType" resultType="io.renren.modules.enterprise.kq01.dto.DeviceDTO">
        select b.name as projectName, t.deviceserialno as deviceKey, t.terminaltype
        from b_kq01 t,
             r_pj01_dept d,
             b_Pj01 b
        where t.whether = '1'
          and t.pj0101 = b.pj0101
          and t.terminaltype = #{terminaltype}
          and t.pj0101 = d.pj0101
          and d.dept_id = #{deptId}
          and t.DEVICESERIALNO &lt;&gt; #{deviceserialno}
        <if test="deviceKey != null and deviceKey != ''">
            and t.DEVICESERIALNO = #{deviceKey}
        </if>
        <if test="projectName != null and projectName != ''">
            and b.NAME like '%' || #{projectName} || '%'
        </if>
        order by t.KQ0101
    </select>
    <!--更新活体检测-->
    <update id="updateComRecRank">
        update B_KQ01 a
        set a.COM_REC_RANK=#{comRecRank}
        where a.KQ0101 = #{kq0101}
    </update>

    <select id="getDeviceSupplier" resultType="io.renren.modules.enterprise.kq01.dto.DeviceSupplierDictDTO">
        select kq0701 as value,
        name as label
        from b_kq07
    </select>

    <select id="getInfo" resultType="io.renren.modules.enterprise.kq01.dto.Kq01InfoDTO">
        select t.*, b.app_key, b.app_secret
        from b_kq01 t
                 left join b_kq06 b
                           on t.deviceserialno = b.deviceserialno
        where t.kq0101 = #{id}
    </select>
</mapper>