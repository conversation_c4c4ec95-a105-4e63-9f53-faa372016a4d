<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.projectDataCompare.dao.ProjectDataCompareDao">
    <select id="getListData" resultType="io.renren.modules.enterprise.projectDataCompare.dto.ProjectDataCompareDTO">
        select t.pj0101,
               t.name                          as projectName,
               t.areacode                      as areaCode,
               t.linkman                       as contact<PERSON><PERSON>,
               t.linkphone                     as contactNumber,
               t.prjstatus                     as projectStatus,
               nvl(e.partunitnum, '0')         as partUnitNum,
               nvl(r.reportpartunitnum, '0')   as reportPartUnitNum,
               nvl(b.teamnum, '0')             as teamNum,
               NVL(A.REPORTTEAMNUM, '0')       as reportTeam<PERSON>um,
               nvl(d.workernum, '0')           as worker<PERSON>um,
               nvl(c.reportworkernum, '0')     as reportWorkerNum,
               nvl(f.managernum, '0')          as managerNum,
               nvl(j.reportmanagernum, '0')    as reportManagerNum,
               nvl(h.attendancenum, '0')       as attendanceNum,
               nvl(m.reportattendancenum, '0') as reportAttendanceNum
        from b_pj01 t
                     left join (select pj0101, count(0) partUnitNum from b_cp02 group by pj0101) e
                on t.pj0101 = e.pj0101
                     left join (select pj0101, count(0) reportPartUnitNum
                                from ${datasourceName}.project_corp_info
                                group by pj0101) r
                on t.pj0101 = r.pj0101
                     left join (select pj0101, count(0) teamNum from b_tm01 group by pj0101) b
                on t.pj0101 = b.pj0101
                     left join (select pj0101, count(distinct (tm0101)) reportTeamNum
                                from ${datasourceName}.TEAM_MASTER_INFO
                                group by pj0101) a
                on t.pj0101 = a.pj0101
                     left join (select pj0101, count(0) workerNum from b_ps02 group by pj0101) d
                on t.pj0101 = d.pj0101
                     left join (select pj0101, count(distinct (ID_CARD_NUMBER)) reportWorkerNum
                                from ${datasourceName}.PROJECT_WORKER_INFO
                                group by pj0101) c
                on t.pj0101 = c.pj0101
                     left join (select pj0101, count(0) managerNum from b_ps04 group by pj0101) f
                on t.pj0101 = f.pj0101
                     left join (select pj0101,
                                       count(distinct (PM_ID_CARD_NUMBER)) reportManagerNum
                                from ${datasourceName}.PROJECT_PM_INFO
                                group by pj0101) j
                on t.pj0101 = j.pj0101
                     left join (select pj0101, count(distinct (user_id)) attendanceNum
                                from b_kq02
                                where to_char(checkdate, 'yyyy-MM-dd') =
                                      to_char(sysdate, 'yyyy-MM-dd')
                                group by pj0101) h
                on t.pj0101 = h.pj0101
                     left join (select pj0101,
                                       count(distinct (id_card_number)) reportAttendanceNum
                                from ${datasourceName}.WORKER_ATTENDANCE_INFO
                                where to_char(attend_date, 'yyyy-MM-dd') =
                                      to_char(sysdate, 'yyyy-MM-dd')
                                group by pj0101) m
                on t.pj0101 = m.pj0101
        <where>
            <if test="projectStatus != null and projectStatus != ''">
                t.PRJSTATUS = #{projectStatus}
            </if>
            <if test="projectName != null and projectName != ''">
                and t.NAME like '%' || #{projectName} || '%'
            </if>
            <if test="areaCode != null and areaCode != ''">
                and t.AREACODE like '' || #{areaCode} || '%'
            </if>
        </where>
        order by t.areacode
    </select>
    <!--查询参建单位的差异-->
    <select id="getPartUnitList" resultType="io.renren.modules.enterprise.projectDataCompare.dto.PartUnitDTO">
        select b.corpname as corpName, b.corpcode as corpCode, a.corptype as corpType
        from b_cp02 a,
             b_cp01 b
        where b.cp0101 = a.cp0101
          and a.pj0101 = #{pj0101}
          and b.corpcode not in
              (select c.corp_code
               from ${name}.project_corp_info c
               where c.pj0101 = #{pj0101})
    </select>
    <!--查询班组差异数据-->
    <select id="getTeamInfoList" resultType="io.renren.modules.enterprise.projectDataCompare.dto.TeamInfoDTO">
        select a.teamname as teamName, a.responsiblepersonname as responsiblePersonName, a.responsiblepersonphone
        from b_tm01 a
        where a.pj0101 = #{pj0101}
          and a.tm0101 not in
              (select b.tm0101
               from ${name}.team_master_info b
               where b.pj0101 = #{pj0101})
    </select>
    <!--工人差异数据-->
    <select id="getWorkerInfoList" resultType="io.renren.modules.enterprise.projectDataCompare.dto.WorkerInfoDTO">
        select b.name, b.idcardnumber as idCardNumber, a.in_or_out as inOrOut
        from b_ps02 a,
             b_ps01 b
        where a.ps0101 = b.ps0101
          and a.pj0101 = #{pj0101}
          and b.idcardnumber not in
              (select c.id_card_number from ${name}.project_worker_info c where c.pj0101 = #{pj0101})
    </select>
    <!--管理人员差异数据-->
    <select id="getManagerInfoList" resultType="io.renren.modules.enterprise.projectDataCompare.dto.ManagerInfoDTO">
        select b.name, b.idcardnumber as idCardNumber, a.in_or_out as inOrOut
        from b_ps04 a,
             b_ps01 b
        where a.ps0101 = b.ps0101
          and a.pj0101 = #{pj0101}
          and b.idcardnumber not in
              (select c.pm_id_card_number
               from ${name}.project_pm_info c
               where c.pj0101 = #{pj0101})
    </select>
</mapper>