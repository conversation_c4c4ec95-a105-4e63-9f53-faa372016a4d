<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.tj01.dao.Tj01Dao">
    <select id="selectLastWeekList" resultType="io.renren.modules.enterprise.tj01.dto.Tj01DTO">
        select t.statistical_day                        as statisticalDay,
               t.project_info                           as projectInfo,
               t.part_units_info                        as partUnitsInfo,
               t.team_info                              as teamInfo,
               t.person_info                            as personInfo,
               t.attendance_info                        as attendanceInfo,
               rank() over (order by t.statistical_day) as serialNumber
        from b_tj01 t
        where t.statistical_day &gt;= to_char(sysdate - 7, 'yyyyMMdd')
        order by t.statistical_day
    </select>
</mapper>