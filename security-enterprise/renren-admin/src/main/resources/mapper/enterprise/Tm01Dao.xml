<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.tm01.dao.Tm01Dao">
    <select id="getList" resultType="io.renren.modules.enterprise.tm01.dto.Tm01PageDTO">
        select a.name projectName,
               t.responsiblepersonname,
               t.tm0101,
               t.cp0201,
               t.pj0101,
               t.teamname,
               t.entrytime,
               t.exittime,
               t.IN_OR_OUT
        from b_tm01 t,
             R_PJ01_DEPT r,
             b_pj01 a
        where t.pj0101 = r.pj0101
          and t.pj0101 = a.pj0101
          and r.DEPT_ID = #{deptId}
        <if test="teamname != null and teamname != ''">
            and t.TEAMNAME like '%' || #{teamname} || '%'
        </if>
        <if test="inOrOut != null and inOrOut != ''">
            and t.IN_OR_OUT = #{inOrOut}
        </if>
        <if test="pj0101 != null and pj0101 != ''">
            and t.PJ0101 = #{pj0101}
        </if>
    </select>

    <select id="queryByName" resultType="java.lang.Integer">
        select count(0)
        from B_TM01 t
        where t.teamname = #{teamName}
          and t.pj0101 = #{pj0101}
    </select>
    <!--查询所有的班组信息-->
    <select id="loadTm01Info" resultType="io.renren.common.common.dto.CommonDto">
        select a.TM0101 value, a.TEAMNAME label
        from B_TM01 a,
             R_PJ01_DEPT t
        where a.PJ0101 = t.PJ0101
          and t.DEPT_ID = #{deptId}
    </select>
    <update id="updateBatchExitTeam">
        update B_TM01 a
        set a.exittime =to_date(to_char(sysdate, 'yyyy-MM-dd'), 'yyyy-MM-dd'),
            a.in_or_out='2'
        where a.TM0101 in
        <foreach collection="asList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <update id="updateBatchEntryTeam">
        update B_TM01 a
        set a.exittime  ='',
            a.ENTRYTIME =to_date(to_char(sysdate, 'yyyy-MM-dd'), 'yyyy-MM-dd'),
            a.in_or_out='1'
        where a.TM0101 in
        <foreach collection="asList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <!--查询在场班组信息-->
    <select id="selectPresenceTeamInfo" resultType="io.renren.common.common.dto.CommonDto">
        select a.TM0101 value, a.TEAMNAME label
        from B_TM01 a,
             R_PJ01_DEPT t
        where a.PJ0101 = t.PJ0101
          and a.IN_OR_OUT = '1'
          and t.DEPT_ID = #{deptId}
    </select>
</mapper>