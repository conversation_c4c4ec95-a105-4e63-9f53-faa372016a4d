<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcPageDetailDao">
       <select id="getPageDetail" resultType="io.renren.modules.dust.dto.YcPageDetailDto">
            SELECT
                s.NAME AREANAME,
                t.*
            FROM
                (
                SELECT
                    b1.NAME,
                    b1.AREACODE,
                    temp.*,
                    ROW_NUMBER ( ) OVER ( PARTITION BY b1.PJ0101 ORDER BY temp.MONITORINGTIME DESC ) sn,
                    (
                    CASE

                            WHEN (
                            SELECT
                                COUNT( y.YJ0101 )
                            FROM
                                YC_YJ01 y
                            WHERE
                                y.PJ0101 = temp.PJ0101
                                AND TO_CHAR( y.WARDATE, 'yyyyMMdd' ) = TO_CHAR( SYSDATE, 'yyyymmdd' )
                                ) > 0 THEN
                                1 ELSE 0
                            END
                            ) isWarn
                        FROM
                            (
                            SELECT
                                y.PJ0101,
                                NVL( y.PM10VALUE, 0 ) PM10VALUE,
                                NVL( y.PM25VALUE, 0 ) PM25VALUE,
                                NVL( y.VOICE, 0 ) VOICE,
                                NVL( y.WINDDIRECTION, 0 ) WINDDIRECTION,
                                NVL( y.WINDSPEED, 0 ) WINDSPEED,
                                y.MONITORINGTIME
                            FROM
                                YC_DT01 y
                            ) temp
                            RIGHT JOIN B_PJ01 b1 ON b1.PJ0101 = temp.PJ0101
                        ORDER BY
                            temp.MONITORINGTIME DESC
                        ) t,
                        SYS_REGION s
                    WHERE
                        t.sn = 1
                        AND s.VALUE = t.AREACODE
                        AND t.AREACODE like '%' || #{areacode} || '%'
                        <if test="name != null and name.trim() != ''">
                        AND t.NAME  like '%' || #{name} || '%'
                        </if>
                        <if test="iswarn != null and iswarn.trim() != ''">
                        and t.isWarn = #{iswarn}
                        </if>
        </select>
        <select id="getCountyDetail" resultType="io.renren.modules.dust.dto.YcCountyDto">
            SELECT
                s.value AREACODE ,
                s.name AREANAME ,
                count( t.pj0101 ) as projectCount ,
                NVL(SUM(t.disCount), 0) as disCount,
                NVL(sum( t.warn ), 0) as warnCount ,
                NVL(trunc( ( sum( t.warn ) / count( t.pj0101 ) ), 2 ), 0) warnRate
            FROM
                (
                SELECT
                    p.pj0101,
                    p.areacode,
                    (
                    CASE
                       WHEN ( SELECT count( t.dt0201 ) FROM yc_dt02 t WHERE t.pj0101 = p.pj0101 ) > 0 THEN
                            1 ELSE 0
                        END
                        ) AS warn ,
                        ( SELECT COUNT( 1 ) FROM YC_DV01 y WHERE y.PJ0101 = p.PJ0101 AND y.DEVICESTATE = '0' ) disCount
                    FROM
                        b_pj01 p
                    ) t
                    RIGHT JOIN sys_region s ON t.areacode = s.value
                WHERE
                    s.PID LIKE concat( #{areacode}, '%' )
                GROUP BY
                s.name,
                s.value
    </select>
</mapper>