<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcYj01Dao">

    <select id="getYcYjList" resultType="io.renren.modules.dust.dto.YcYj01Dto">
        select * from YC_YJ01 y where y.pj0101=#{pj0101}
    </select>
    <select id="getYcYjDetail" resultType="io.renren.modules.dust.dto.YcYj01DetailDTO">
        SELECT * FROM YC_DT02 t WHERE t.PJ0101 = #{pj0101} AND t.warntype = #{warntype} AND TO_CHAR(t.MONITORINGTIME, 'yyyyMMdd') = #{date}
</select>
    <select id="warnInfo" resultType="java.lang.Integer">
        select decode(sum(t.warcount), null, 0, sum(t.warcount)) as warcount
          from YC_YJ01 t
         where t.pj0101 = #{pj0101}
           and to_char(t.wardate, 'yyyyMMdd') = to_char(sysdate, 'yyyyMMdd')
    </select>
    <select id="beforeList" resultType="java.util.Map">
        select to_char(t.wardate,'yyyyMMdd') as wardate, sum(t.warcount) as warcount
          from YC_YJ01 t
         where t.pj0101 = #{pj0101}
         group by t.wardate
         order by t.wardate desc
    </select>

    <select id="pageList" resultType="io.renren.modules.dust.dto.YcYj01Dto">
        select t.*, a.name
          from YC_YJ01 t, b_pj01 a
         where t.pj0101 = a.pj0101
           and a.pj0101 = #{pj0101}
        <if test="wardate !=null and wardate !=''">
            and to_char(t.wardate,'yyyy-MM-dd')=#{wardate}
        </if>
        <if test="wartype !=null and wartype !=''">
            and t.wartype=#{wartype}
        </if>
         order by t.wardate desc
    </select>
</mapper>