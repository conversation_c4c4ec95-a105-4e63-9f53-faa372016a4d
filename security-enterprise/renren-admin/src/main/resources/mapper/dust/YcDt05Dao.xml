<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcDt05Dao">

    <resultMap type="io.renren.modules.dust.entity.YcDt05Entity" id="ycDt05Map">
        <result property="dt0501" column="DT0501"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="warntype" column="WARNTYPE"/>
        <result property="success" column="SUCCESS"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="monitoringtime" column="MONITORINGTIME"/>
    </resultMap>

    <select id="getToDayYcDt05" resultMap="ycDt05Map">

        SELECT distinct t.*
        FROM (SELECT d.pj0101, d.warntype, d.monitoringtime
              FROM yc_dt02 d
              where to_char(d.monitoringtime, 'yyyyMMdd') =
                    to_char(sysdate, 'yyyyMMdd')) t

                     left outer join (SELECT *
                                      FROM yc_dt05 d
                                      where to_char(d.CREATE_TIME, 'yyyyMMdd') =
                                            to_char(sysdate, 'yyyyMMdd')) a
                on t.pj0101 = a.pj0101
                        and t.warntype = a.warntype
        where a.dt0501 is null

    </select>

    <select id="getWartingSendList" resultType="io.renren.modules.dust.dto.YcDt05SendDTO">

        select d.dt0501, d.pj0101, d.warntype, d.success, d.create_time, d.monitoringtime, t.name proname
        from yc_dt05 d, b_pj01 t
        where d.pj0101 = t.pj0101 and to_char(create_time, 'yyyyMMdd') = to_char(sysdate, 'yyyyMMdd')
          and success = '0'

    </select>
</mapper>