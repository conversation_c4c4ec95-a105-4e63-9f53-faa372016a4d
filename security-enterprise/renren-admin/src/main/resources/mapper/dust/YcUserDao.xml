<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcUserDao">
    <resultMap type="io.renren.modules.dust.entity.YcUserEntity" id="ycUserMap">
        <result property="id" column="ID"/>
        <result property="username" column="USERNAME"/>
        <result property="mobile" column="MOBILE"/>
        <result property="password" column="PASSWORD"/>
        <result property="showpassword" column="SHOWPASSWORD"/>
    </resultMap>

    <select id="getList" resultType="io.renren.modules.dust.dto.YcUserDTO">
        select t.pj0101 as id, t.code, t.name as projectName, t.areacode as areaCode
        from b_pj01 t,
             R_PJ01_DEPT a where t.PJ0101 = a.PJ0101
                             and a.DEPT_ID = #{deptId}
        <if test="projectName != null and projectName != ''">
            and t.name like '%' || #{projectName} || '%'
        </if>
        order by t.areacode
    </select>
    <select id="get" resultType="io.renren.modules.dust.dto.YcUserDTO">
        select t.pj0101 as id, t.code, t.name as projectName, t.areacode as areaCode
        from b_pj01 t
        where t.PJ0101 = #{id}
    </select>
    <select id="selectUserPj" resultType="java.util.Map">
        select PJ0101,
               name,
               CODE

        from b_pj01
        where pj0101 = #{pj0101}
    </select>

    <update id="updateUserPj">
        update b_pj01
        set CODE = #{code}
        where pj0101 = #{pj0101}
    </update>

    <select id="getAreacode" resultType="io.renren.common.common.dto.CommonDto">
        select t.name as label, t.value
        from SYS_REGION t
        where t.pid = 510000
    </select>
</mapper>