<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.camera.dao.YcDv03Dao">

    <select id="getPageList" resultType="io.renren.modules.camera.dto.YcDv03DTO">
        SELECT
        b.NAME,
        y.*
        FROM
        YC_DV03 y,
        B_PJ01 b,
        r_pj01_dept d
        WHERE
        y.PJ0101 = b.PJ0101
        AND b.PJ0101 = d.PJ0101
        AND d.DEPT_ID = #{deptId}
        <if test="name != null and name.trim() != ''">
            AND b.name like '%'||#{name}||'%'
        </if>
        <if test="devicestate != null and devicestate.trim() != ''">
            AND y.devicestate = #{devicestate}
        </if>

    </select>
    <select id="getNVRList" resultType="io.renren.common.common.dto.CommonDto">
        select t.dv0301 as value, t.devicename as label
              from yc_dv03 t
             where t.pj0101 = #{pj0101}
               and t.devicetype = '1'
    </select>
    <select id="getAreaCodePageData" resultType="io.renren.modules.camera.dto.CameraStatisticalDTO">
        select t.name as areaName,
               t.value as areaCode,
               t.totalProjectNum,
               t.equipmentProjectNum,
               (t.totalProjectNum - t.equipmentProjectNum) as unEquipmentProjectNum
          from (select s.name,
                       s.value,
                       (select count(1)
                          from b_pj01 x
                         where x.areacode like substr(s.value, 0, #{level} * 2) || '%') as totalProjectNum,
                       (select count(x.pj0101)
                          from b_pj01 x, yc_dv03 y
                         where x.pj0101 = y.pj0101
                           and x.areacode like substr(s.value, 0, #{level} * 2) || '%') as equipmentProjectNum
                  from sys_region s
                 where s.value like #{areaCode}||'%'
                   and s.tree_level = #{level}) t
         order by t.value
    </select>
    <select id="projectCameraPage" resultType="io.renren.modules.camera.dto.ProjectCameraDTO">
        SELECT
        p.pj0101,
        p.name as projectName,
        p.linkman as linkMan,
        p.linkphone as telephoneNumber,
        decode(k.devicestate, '1', '在线','0','离线','') as equipmentState,
        k.sn as equipmentNo
        FROM b_pj01 p left join yc_dv03 k on
        p.pj0101 = k.pj0101
        WHERE p.areacode like #{params.areaCode}||'%'
        <if test="params.isinstall == 1">
            and p.pj0101 in (select a.pj0101 from yc_dv03 a group by a.pj0101)
        </if>
        <if test="params.isinstall == 2">
            and p.pj0101 not in (select a.pj0101 from yc_dv03 a group by a.pj0101)
        </if>
        <if test="params.devicestate != '' and params.devicestate != null">
            and k.devicestate = #{params.devicestate}
        </if>
        <if test="params.prjstatus != '' and params.prjstatus != null">
            and p.prjstatus = #{params.prjstatus}
        </if>
        <if test="params.projectName != '' and params.projectName != null">
            and p.name like  #{params.projectName}
        </if>
        <if test="params.equipmentNo != '' and params.equipmentNo != null">
            and k.sn = #{params.equipmentNo}
        </if>
    </select>
</mapper>