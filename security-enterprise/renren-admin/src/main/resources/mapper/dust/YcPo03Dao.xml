<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcPo03Dao">

    <resultMap type="io.renren.modules.dust.entity.YcPo03Entity" id="bPo03Map">
        <result property="po0301" column="PO0301"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="po0201" column="PO0201"/>
        <result property="unitOfUse" column="UNIT_OF_USE"/>
        <result property="corpcode" column="CORPCODE"/>
        <result property="useType" column="USE_TYPE"/>
        <result property="timeOfOccurrence" column="TIME_OF_OCCURRENCE"/>
        <result property="operator" column="OPERATOR"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <select id="getListData" resultType="io.renren.modules.dust.dto.YcPo03DTO">
        SELECT a.*,c.mechanical_name
        FROM yc_po03 a,r_pj01_dept b,yc_po02 c where a.pj0101=b.pj0101
        and a.po0201=c.po0201
        and b.dept_id=#{deptId}
        <if test="po0201 !=null and po0201 !=''">
            and a.po0201=#{po0201}
        </if>
        order by a.create_date desc
    </select>


</mapper>
