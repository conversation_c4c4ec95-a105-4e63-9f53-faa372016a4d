<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcDt01Dao">
    <resultMap type="io.renren.modules.dust.entity.YcDt01Entity" id="ycDt01Map">
        <result property="dt0101" column="DT0101"/>
        <result property="dv0101" column="DV0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="monitoringtime" column="MONITORINGTIME"/>
        <result property="savetime" column="SAVETIME"/>
        <result property="pm10value" column="PM10VALUE"/>
        <result property="pm25value" column="PM25VALUE"/>
        <result property="voice" column="VOICE"/>
        <result property="temperature" column="TEMPERATURE"/>
        <result property="humidity" column="HUMIDITY"/>
        <result property="windspeed" column="WINDSPEED"/>
        <result property="winddirection" column="WINDDIRECTION"/>
        <result property="atmospheric" column="ATMOSPHERIC"/>
        <result property="type" column="DATA_TYPE"/>
        <result property="pm25monitor" column="PM25MONITOR"/>
        <result property="pm10monitor" column="PM10MONITOR"/>
        <result property="snapimage" column="SNAPIMAGE"/>
        <result property="spraystatus" column="SPRAYSTATUS"/>
    </resultMap>

    <select id="selectByTime" resultType="io.renren.modules.dust.entity.YcDt01Entity">
        select *
        from YC_DT01 t,
             r_pj01_dept a
        where t.pj0101 = a.pj0101
          and a.dept_id = #{deptId}
        <if test="starDate != null and starDate.trim() != ''">
            and t.monitoringtime between to_date(#{starDate}, 'yyyy-MM-dd')
                and to_date(#{endDate}, 'yyyy-MM-dd')
        </if>
    </select>
    <select id="pageList" resultType="io.renren.modules.dust.dto.YcDt01DTO">
        select t.*, a.name, case when t.snapimage is not null then '是' end as isimage
        from YC_DT01 t,
             b_pj01 a,
             r_pj01_dept b
        where t.pj0101 = a.pj0101
          and a.pj0101 = b.pj0101
          and b.dept_id = #{deptId}
          and t.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectDustProject">
    </include>)
                and to_char(t.monitoringtime, 'yyyy-MM-dd') = nvl(#{monitoringtime}, to_Char(sysdate, 'yyyy-MM-dd'))
        <if test="name != null and name.trim() != ''">
            and a.name like '%' || #{name} || '%'
        </if>
        ORDER BY t.DT0101 DESC
    </select>
    <select id="getPageDetail" resultType="io.renren.modules.dust.dto.YcPageDetailDto">
        SELECT *
        FROM
        (
        SELECT b1.NAME,
               b1.AREACODE,
               ydt1.PM25VALUE,
               ydt1.PM10VALUE,
               ydt1.VOICE,
               ydt1.WINDDIRECTION,
               ydt1.WINDSPEED,
               ydt1.MONITORINGTIME,
               ROW_NUMBER() OVER ( PARTITION BY b1.name ORDER BY ydt1.MONITORINGTIME DESC ) sn
        FROM B_PJ01 b1,
             YC_DT01 ydt1
        WHERE ydt1.PJ0101 = b1.PJ0101
        <if test="name != null and name.trim() != ''">
            and b1.NAME = #{NAME}
        </if>
        <if test="areacode != null and areacode.trim() != ''">
            and b1.AREACODE = #{AREACODE}
        </if>
        ) temp
        WHERE temp.sn = 1
    </select>
    <select id="getYcDt01Detail" resultMap="ycDt01Map">
        SELECT t.PJ0101,
               t.PM10VALUE     PM10VALUE,
               t.PM25VALUE     PM25VALUE,
               t.VOICE         VOICE,
               t.WINDDIRECTION WINDDIRECTION,
               t.TEMPERATURE   TEMPERATURE,
               t.WINDSPEED     WINDSPEED,
               t.monitoringtime
        FROM (
                 SELECT y.PJ0101,
                        y.PM10VALUE     PM10VALUE,
                        y.PM25VALUE     PM25VALUE,
                        y.VOICE         VOICE,
                        y.TEMPERATURE   TEMPERATURE,
                        y.WINDDIRECTION WINDDIRECTION,
                        y.WINDSPEED     WINDSPEED,
                        y.monitoringtime
                 FROM YC_DT01 y
                 ORDER BY y.monitoringtime DESC
             ) t
        WHERE ROWNUM = 1
          AND t.pj0101 = #{pj0101,jdbcType=VARCHAR}
    </select>
    <select id="getVideoUrl" resultType="java.util.List">
        SELECT URL
        FROM YC_DV03 t
        WHERE t.PJ0101 = #{pj0101,jdbcType=VARCHAR}
    </select>

    <select id="selectTrendChart" resultType="io.renren.modules.dust.dto.YcDt01DTO">
        select *
        from (select t.monitoringtime, t.pm10value, t.pm25value, t.voice
              from yc_dt01 t
              where t.pj0101 = #{pj0101}
              union
              select t.monitoringtime, t.pm10value, t.pm25value, t.voice
              from yc_Dt02 t
              where t.pj0101 = #{pj0101}) t
        where to_char(t.monitoringtime, 'yyyyMMdd') = to_char(sysdate, 'yyyyMMdd')
    </select>
    <select id="selectNewestData" resultType="io.renren.modules.dust.dto.YcDt01DTO">
        select t.monitoringtime,
               t.pm10value,
               t.pm25value,
               t.voice,
               t.temperature,
               t.windspeed,
               case
                   when t.winddirection = 0 or t.winddirection = 360 then
                       '正北'
                   when t.winddirection between 1 and 89 then
                       '东北'
                   when t.winddirection = 90 then
                       '正东'
                   when t.winddirection between 91 and 189 then
                       '东南'
                   when t.winddirection = 180 then
                       '正南'
                   when t.winddirection between 181 and 269 then
                       '西南'
                   when t.winddirection = 270 then
                       '正西'
                   when t.winddirection between 271 and 359 then
                       '西北'
                   end as winddirection
        from yc_dt01 t
        where t.dt0101 = (select max(a.dt0101) from yc_dt01 a where a.pj0101 = #{pj0101})
    </select>

    <select id="getUpdateData" resultType="io.renren.modules.dust.entity.YcRealDataEntity">
        SELECT a.pj0101,
               d.monitoringtime            as offlinetime,
               (select min(x.monitoringtime)
                from yc_dt01 x
                where x.pj0101 = a.pj0101) as registertime,
               a.dt0101
        FROM (Select Pj0101, Max(Dt0101) dt0101
              From Yc_Dt01 t
        where t.pj0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectDustProject">
    </include>)
    Group By Pj0101) a
                  left join yc_dt01 d
                            on a.dt0101 = d.dt0101
    </select>

    <select id="truncateData">
        delete
        from yc_real_data
    </select>
</mapper>