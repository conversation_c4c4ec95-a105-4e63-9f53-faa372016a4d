<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcPj01Dao">

    <select id="pageList" resultType="io.renren.modules.dust.dto.ProjectInfoDto">
        select *
          from (Select t.pj0101,
                       t.name,
                       (select a.name from sys_region a where a.value = d.areacode) as areaname,
                       d.areacode,
                       c.pm10value,
                       c.pm25value,
                       c.voice,
                       c.temperature,
                       c.monitoringtime,
                       case
                         when e.warcount is not null then
                          '1'
                         when e.warcount is null then
                          '0'
                       end as iswarn
                  From b_Pj01 t
                  left join (select t.pj0101, t.dt0101 mx from yc_real_data t ) b
                    on t.pj0101 = b.pj0101
                  left join yc_yj01 e
                   on t.pj0101 = e.pj0101
                   and to_char(e.wardate, 'yyyyMMdd') = to_char(sysdate, 'yyyyMMdd')
                  left join yc_dt01 c
                    on c.dt0101 = b.mx, r_Pj01_Dept a, sys_dept d
                 Where t.Pj0101 = a.Pj0101
                   and t.dept_id = d.id
                   and a.dept_id=#{deptId}
                   and t.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectDustProject"> </include>)
                <if test="areacode !=null and areacode !=''">
                    and d.areacode=#{areacode}
                </if>
                <if test="name !=null and name !=''">
                    and t.name like '%'||#{name}||'%'
                </if>) t where 1=1
        <if test="iswarn !=null and iswarn !=''">
            and t.iswarn = #{iswarn}
        </if>
    </select>

    <select id="selectTjData" resultType="io.renren.modules.dust.dto.MapDto">
        select (select count(1)
                  from B_PJ01 t, r_pj01_dept a,sys_dept b
                    where t.pj0101 = a.pj0101 and t.dept_id=b.id
                   and a.dept_id = #{deptId}
                <if test="areacode !=null and areacode !=''">
                    and b.areacode=#{areacode}
                </if>) as projects,
               (select count(1)
                  from b_pj01 t, r_pj01_dept a,sys_dept b
                 where t.pj0101 = a.pj0101 and t.dept_id=b.id
                   and a.dept_id = #{deptId}
                   and t.pj0101 in (select x.pj0101
                                      from YC_YJ01 x
                                     where to_char(x.wardate, 'yyyyMMdd') =
                                           to_char(sysdate, 'yyyyMMdd')
                                     group by x.pj0101)
                    <if test="areacode !=null and areacode !=''">
                        and b.areacode=#{areacode}
                    </if>) as warnprojects,
               (select count(1)
                  from b_pj01 t, r_pj01_dept a, yc_dv01 b,sys_dept d
                 where t.pj0101 = a.pj0101 and t.dept_id=d.id
                   and a.dept_id = #{deptId}
                   and t.pj0101 = b.pj0101
                   and b.devicestate = '0'
                   and t.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectDustProject"> </include>)
                <if test="areacode !=null and areacode !=''">
                    and d.areacode=#{areacode}
                </if>) as offlineprojects
          from dual
    </select>
</mapper>