<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcDv01Dao">

    <resultMap type="io.renren.modules.dust.entity.YcDv01Entity" id="ycDv01Map">
        <result property="dv0101" column="DV0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="devicemodel" column="DEVICEMODEL"/>
        <result property="devicename" column="DEVICENAME"/>
        <result property="manufacturer" column="MANUFACTURER"/>
        <result property="batch" column="BATCH"/>
        <result property="sn" column="SN"/>
        <result property="sim" column="SIM"/>
        <result property="devicestate" column="DEVICESTATE"/>
        <result property="person" column="PERSON"/>
        <result property="phone" column="PHONE"/>
        <result property="remark" column="REMARK"/>
        <result property="longitude" column="LONGITUDE"/>
        <result property="latitude" column="LATITUDE"/>
        <result property="url" column="URL"/>
    </resultMap>




    <select id="pageList"  resultType="io.renren.modules.dust.dto.YcDv01DTO">
<!--        select a.name, t.*,-->
<!--            (select max(x.monitoringtime) from yc_dt01 x where x.pj0101=t.pj0101) as offlinetime,-->
<!--            (select min(x.monitoringtime) from yc_dt01 x where x.pj0101=t.pj0101) as registertime,-->
<!--            (select x.name from sys_region x where x.value=c.areacode) as areacode-->
<!--            from YC_DV01 t, b_pj01 a, r_pj01_dept b,sys_Dept c-->
<!--            where t.pj0101 = a.pj0101-->
<!--            and a.pj0101 = b.pj0101-->
<!--            and b.dept_id = #{deptId}-->
<!--            and a.dept_id=c.id-->
<!--            and t.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectDustProject"> </include>)-->

        select a.name,
        t.*,
        d.offlinetime,
        d.registertime,
        (select x.name from sys_region x where x.value = c.areacode) as areacode
        from YC_DV01 t left join yc_real_data d
        on t.pj0101 = d.pj0101, b_pj01 a, r_pj01_dept b, sys_Dept c
        where t.pj0101 = a.pj0101
        and a.pj0101 = b.pj0101
        and b.dept_id = #{deptId}
        and a.dept_id = c.id
        and t.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectDustProject"> </include>)

        <if test="name != null and name != ''">
            and  a.name like  '%'|| #{name} || '%'
        </if>
        <if test="sn != null and sn != ''">
            and  t.sn like  '%'|| #{sn} || '%'
        </if>
        <if test="devicestate != null and devicestate != ''">
            and  t.devicestate = #{devicestate}
        </if>
        <if test="terminaltype != null and terminaltype != ''">
            and  t.terminaltype = #{terminaltype}
        </if>
        <if test="areacode != null and areacode != ''">
            and  c.areacode = #{areacode}
        </if>
     </select>



    <select id="getMapData"  resultType="io.renren.modules.dust.entity.YcDv01Entity">

      select pj0101,
      dv0101,
       terminaltype,
       (select dict_label
         from sys_dict_data
        where dict_type_id =
              (select id from sys_dict_type where dict_type = 'DEVICESTATE')
          and dict_value =yc_dv01.DEVICESTATE) as DEVICESTATE  , devicename, PERSON, PHONE, LONGITUDE,
        LATITUDE from yc_dv01  where pj0101 = #{pj0101}


    </select>

    <select id="getMapDt01AndDt02"  resultType="io.renren.modules.dust.entity.YcDt01Entity">


SELECT
	t1.*, ROWNUM  from (

 select  *
FROM    (SELECT PM10VALUE,
               PM25VALUE,
               VOICE,
               TEMPERATURE,
               HUMIDITY,
               WINDSPEED,
               ATMOSPHERIC,
               WINDDIRECTION,
               PJ0101,
               DV0101,
               MONITORINGTIME
          FROM yc_dt02 d2
        union all
        SELECT PM10VALUE,
               PM25VALUE,
               VOICE,
               TEMPERATURE,
               HUMIDITY,
               WINDSPEED,
               ATMOSPHERIC,
               WINDDIRECTION,
               PJ0101,
               DV0101,
               MONITORINGTIME
          FROM yc_dt01 d1)t  where dv0101 = #{dv0101} ORDER BY t.MONITORINGTIME DESC
) t1 WHERE rownum = 1

    </select>

    <select id="deviceInfo" resultType="io.renren.modules.dust.dto.YcDv01DTO">
        select t.devicename,
               t.sn,
               t.devicemodel,
               t.manufacturer,
               t.devicestate,
               (select a.monitoringtime
                  from yc_dt01 a
                 where a.dt0101 =
                       (select max(x.dt0101) from yc_dt01 x where x.pj0101 = #{pj0101})) as offlinetime
          from YC_DV01 t
         where t.pj0101 = #{pj0101}
    </select>
    <select id="selectMapData" resultType="io.renren.modules.dust.dto.MapDto">
        Select t.pj0101,
               e.name,
               t.longitude,
               t.latitude,
               t.devicestate,
               (select sum(x.warcount)
                  from yc_yj01 x
                 where x.pj0101 = t.pj0101
                   and to_char(x.wardate, 'yyyyMMdd') = to_char(sysdate, 'yyyyMMdd')) as warnnum,
               c.pm10value,
               c.pm25value,
               c.voice,
               c.monitoringtime
          From yc_dv01 t
          left join (Select dv0101, Max(Dt0101) Mx From Yc_Dt01 t Group By dv0101) b
            on t.dv0101 = b.dv0101
          left join yc_dt01 c
            on c.dt0101 = b.mx, r_Pj01_Dept a, sys_dept d, b_pj01 e
         Where t.Pj0101 = e.Pj0101
         and e.pj0101=a.pj0101
           and e.dept_id = d.id
           And a.Dept_Id = #{deptId}
        and t.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectDustProject"> </include>)
        <if test="areacode !=null and areacode !=''">
            and d.areacode=#{areacode}
        </if>
    </select>
</mapper>