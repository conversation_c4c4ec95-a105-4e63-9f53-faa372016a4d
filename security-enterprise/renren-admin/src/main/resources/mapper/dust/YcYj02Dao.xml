<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcYj02Dao">

    <select id="pageList" resultType="io.renren.modules.dust.dto.YcYj02DTO">
        select yj0201,
               areacode,
               areaname,
               projects,
               offlinenum,
               accessprojects,
               accessprecent||'%' as accessprecent,
               runprecent||'%' as runprecent,
               exceedprecent||'%' as exceedprecent,
               tjdate
          from YC_YJ02 t
         where t.areacode like #{areacode}||'%'
           and to_char(t.tjdate, 'yyyyMMdd') = to_char(sysdate, 'yyyyMMdd')
    </select>
</mapper>