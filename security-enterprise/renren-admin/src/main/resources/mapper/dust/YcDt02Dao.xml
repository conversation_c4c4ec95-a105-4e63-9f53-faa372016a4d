<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcDt02Dao">
    <resultMap type="io.renren.modules.dust.entity.YcDt02Entity" id="ycDt02Map">
        <result property="dt0201" column="DT0201"/>
        <result property="dv0101" column="DV0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="warntype" column="WARNTYPE"/>
        <result property="monitoringtime" column="MONITORINGTIME"/>
        <result property="savetime" column="SAVETIME"/>
        <result property="pm10value" column="PM10VALUE"/>
        <result property="pm25value" column="PM25VALUE"/>
        <result property="voice" column="VOICE"/>
        <result property="temperature" column="TEMPERATURE"/>
        <result property="humidity" column="HUMIDITY"/>
        <result property="windspeed" column="WINDSPEED"/>
        <result property="winddirection" column="WINDDIRECTION"/>
        <result property="atmospheric" column="ATMOSPHERIC"/>
        <result property="pm25monitor" column="PM25MONITOR"/>
        <result property="pm10monitor" column="PM10MONITOR"/>
        <result property="snapimage" column="SNAPIMAGE"/>
    </resultMap>


    <select id="selectByWarnType" resultType="io.renren.modules.dust.entity.YcDt02Entity">
        select t.*
        from YC_DT02 t,
             r_pj01_dept a
        where t.pj0101 = a.pj0101
          and a.dept_id = #{deptId}
        <if test="warnType != null and warnType != ''">
            and t.WARNTYPE = #{warnType}
        </if>
        ORDER BY t.MONITORINGTIME DESC
    </select>

    <select id="pageList" resultType="io.renren.modules.dust.dto.YcDt02DTO">
        select b.name,
               t.dt0201,
               t.dv0101,
               t.pj0101,
               warntype,
               monitoringtime,
               savetime,
               pm10value,
               pm25value,
               voice,
               temperature,
               humidity,
               windspeed,
               winddirection,
               atmospheric,
               pm25monitor,
               pm10monitor,
               snapimage
        from YC_DT02 t,
             r_pj01_dept a,
             b_pj01 b
        where t.pj0101 = b.pj0101
          and b.pj0101 = a.pj0101
          and a.dept_id = #{deptId}
          and b.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectDustProject">
    </include>)
                and to_char(t.monitoringtime, 'yyyy-MM-dd') = nvl(#{monitoringtime}, to_Char(sysdate, 'yyyy-MM-dd'))
        <if test="warntype != null and warntype != ''">
            and t.WARNTYPE = #{warntype}
        </if>
        <if test="name != null and name != ''">
            and b.name like '%' || #{name} || '%'
        </if>
        ORDER BY t.DT0201 DESC
    </select>
</mapper>