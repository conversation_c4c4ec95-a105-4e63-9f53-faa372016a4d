<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcPo02Dao">

    <resultMap type="io.renren.modules.dust.entity.YcPo02Entity" id="bPo02Map">
        <result property="po0201" column="PO0201"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="deviceId" column="DEVICE_ID"/>
        <result property="environmentalLabelNumber" column="ENVIRONMENTAL_LABEL_NUMBER"/>
        <result property="mechanicalModel" column="MECHANICAL_MODEL"/>
        <result property="mechanicalName" column="MECHANICAL_NAME"/>
        <result property="trademark" column="TRADEMARK"/>
        <result property="mechanicalType" column="MECHANICAL_TYPE"/>
        <result property="emissionStage" column="EMISSION_STAGE"/>
        <result property="manufacturer" column="MANUFACTURER"/>
        <result property="engineModel" column="ENGINE_MODEL"/>
        <result property="factoryTime" column="FACTORY_TIME"/>
        <result property="propertyUnit" column="PROPERTY_UNIT"/>
        <result property="corpcode" column="CORPCODE"/>
        <result property="remark" column="REMARK"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <select id="getListData" resultType="io.renren.modules.dust.dto.YcPo02DTO">
        SELECT a.*
        FROM yc_po02 a,r_pj01_dept b where a.pj0101=b.pj0101
        and b.dept_id=#{deptId}
        <if test="mechanicalName !=null and mechanicalName !=''">
            and a.mechanical_name like '%'||#{mechanicalName}||'%'
        </if>
        order by a.create_date desc
    </select>
    <select id="getPo02Dict" resultType="io.renren.modules.dust.dto.YcPo02DTO">
        SELECT a.po0201,a.mechanical_name
        FROM yc_po02 a,r_pj01_dept b where a.pj0101=b.pj0101
        and b.dept_id=#{deptId}
        order by a.create_date desc
    </select>


</mapper>
