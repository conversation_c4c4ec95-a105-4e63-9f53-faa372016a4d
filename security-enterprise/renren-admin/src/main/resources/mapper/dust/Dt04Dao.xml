<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.Dt04Dao">


    <select id="pageList"  resultType="io.renren.modules.dust.dto.Dt04DTO">
        select  t.*,
                a.name,
                a.areacode,
                (select x.username from sys_user x where x.dept_id = a.dept_id) as username
        from yc_Dt04 t, b_pj01 a
        where t.pj0101 = a.pj0101
        <if test="name != null and name != ''">
            and  a.name like '%'||#{name}||'%'
        </if>
    </select>

    <select id="selectByPJ0101" resultType="io.renren.modules.dust.dto.Dt04DTO">
        select t.*,a.name from yc_dt04 t,b_pj01 a where t.pj0101=a.pj0101 and t.pj0101=#{pj0101}
    </select>
</mapper>