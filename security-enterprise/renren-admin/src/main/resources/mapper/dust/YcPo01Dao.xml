<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.dust.dao.YcPo01Dao">

    <resultMap type="io.renren.modules.dust.entity.YcPo01Entity" id="po01Map">
        <result property="po0101" column="PO0101"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="purchaseunit" column="PURCHASEUNIT"/>
        <result property="creditcode" column="CREDITCODE"/>
        <result property="supplyunit" column="SUPPLYUNIT"/>
        <result property="purchasedate" column="PURCHASEDATE"/>
        <result property="oiltype" column="OILTYPE"/>
        <result property="purchaseno" column="PURCHASENO"/>
        <result property="memo" column="MEMO"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>
    <select id="getListData" resultType="io.renren.modules.dust.dto.YcPo01DTO">
        SELECT a.* FROM yc_po01 a,R_PJ01_DEPT b
        WHERE a.pj0101=b.pj0101 and b.dept_id=#{deptId}
        <if test="purchaseunit !=null and purchaseunit !=''">
            and a.purchaseunit like '%'||#{purchaseunit}||'%'
        </if>
        order by a.create_date desc
    </select>


</mapper>
