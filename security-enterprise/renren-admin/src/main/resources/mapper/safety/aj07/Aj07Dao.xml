<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj07.dao.Aj07Dao">

    <resultMap type="io.renren.modules.safety.aj07.entity.Aj07Entity" id="ajAj07Map">
        <result property="aj0701" column="AJ0701"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="qualitySafetyReportNo" column="QUALITY_SAFETY_REPORT_NO"/>
        <result property="sampleCode" column="SAMPLE_CODE"/>
        <result property="supervisionCode" column="SUPERVISION_CODE"/>
        <result property="witnessSampler" column="WITNESS_SAMPLER"/>
        <result property="witnessSampledDate" column="WITNESS_SAMPLED_DATE"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getAj07Page" resultType="io.renren.modules.safety.aj07.dto.Aj07PageDTO">

        SELECT t.pj0101,
               t.safetyno,
               t.name pjname,
               a.aj0701 ,
               a.sample_code,
               a.supervision_code,
               a.witness_sampler,
               a.witness_sampled_date
        FROM b_pj01 t, aj_aj07 a
        where t.pj0101 = a.pj0101
          and t.pj0101 = #{pj0101}

    </select>
</mapper>