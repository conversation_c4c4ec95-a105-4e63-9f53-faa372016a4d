<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj06.dao.Aj06Dao">

    <resultMap type="io.renren.modules.safety.aj06.entity.Aj06Entity" id="ajAj06Map">
        <result property="aj0601" column="AJ0601"/>
        <result property="qualitySafetyReportNo" column="QUALITY_SAFETY_REPORT_NO"/>
        <result property="clientUnit" column="CLIENT_UNIT"/>
        <result property="sampleType" column="SAMPLE_TYPE"/>
        <result property="sampleTypeCode" column="SAMPLE_TYPE_CODE"/>
        <result property="sampleCode" column="SAMPLE_CODE"/>
        <result property="inspectionFormCode" column="INSPECTION_FORM_CODE"/>
        <result property="simpleStatus" column="SIMPLE_STATUS"/>
        <result property="simpleName" column="SIMPLE_NAME"/>
        <result property="projectLocation" column="PROJECT_LOCATION"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="simpleKind" column="SIMPLE_KIND"/>
        <result property="simpleBrand" column="SIMPLE_BRAND"/>
        <result property="commonDiameter" column="COMMON_DIAMETER"/>
        <result property="manufacturer" column="MANUFACTURER"/>
        <result property="dateOfProduction" column="DATE_OF_PRODUCTION"/>
        <result property="heatNumber" column="HEAT_NUMBER"/>
        <result property="productGrade" column="PRODUCT_GRADE"/>
        <result property="strengthGrade" column="STRENGTH_GRADE"/>
        <result property="densityGrade" column="DENSITY_GRADE"/>
        <result property="specificationSize" column="SPECIFICATION_SIZE"/>
        <result property="designLevel" column="DESIGN_LEVEL"/>
        <result property="batchNumber" column="BATCH_NUMBER"/>
        <result property="curingConditions" column="CURING_CONDITIONS"/>
        <result property="requiredAge" column="REQUIRED_AGE"/>
        <result property="numberOfRepresentatives" column="NUMBER_OF_REPRESENTATIVES"/>
        <result property="sampledBy" column="SAMPLED_BY"/>
        <result property="sampledDate" column="SAMPLED_DATE"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getPageList" resultType="io.renren.modules.safety.aj06.dto.Aj06PageDTO">

        SELECT a.aj0601,
               t.pj0101,
               t.name pjname,
               a.sample_type,
               a.SAMPLE_NAME,
               a.PROJECT_LOCATION,
               a.SAMPLED_BY,
               a.SAMPLED_DATE
        FROM b_pj01 t, aj_aj06 a
        where t.pj0101 = a.pj0101
          and t.pj0101 = #{pj0101}
        order by t.create_date desc

    </select>

    <select id="getAj06Detail" resultType="io.renren.modules.safety.aj06.dto.Aj06DetailDTO">

        select a.aj0601,
               t.name pjname,
               a.client_unit,
               a.sample_type,
               a.sample_type_code,
               a.sample_code,
               a.inspection_form_code,
               a.sample_status,
               a.sample_name,
               a.project_location,
               a.sample_kind,
               a.sample_brand,
               a.common_diameter,
               a.manufacturer,
               a.date_of_production,
               a.heat_number,
               a.product_grade,
               a.strength_grade,
               a.density_grade,
               a.specification_size,
               a.design_level,
               a.batch_number,
               a.curing_conditions,
               a.required_age,
               a.number_of_representatives,
               a.sampled_by,
               a.sampled_date
        from b_pj01 t, aj_aj06 a where t.pj0101 = a.pj0101 and a.aj0601 = #{id}

    </select>
</mapper>