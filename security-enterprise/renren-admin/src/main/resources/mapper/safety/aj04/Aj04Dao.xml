<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj04.dao.Aj04Dao">
    <resultMap type="io.renren.modules.safety.aj04.dto.Aj04DTO" id="aj04Map">
        <result property="aj0401" column="AJ0401"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="aj0201" column="AJ0201"/>
        <result property="jobType" column="JOB_TYPE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="principalName" column="PRINCIPAL_NAME"/>
        <result property="paperworkType" column="PAPERWORK_TYPE"/>
        <result property="paperworkNumber" column="PAPERWORK_NUMBER"/>
        <result property="planTime" column="PLAN_TIME"/>
        <result property="managementDept" column="MANAGEMENT_DEPT"/>
        <result property="tellTime" column="TELL_TIME"/>
        <collection property="aj05DTOList" ofType="io.renren.modules.safety.aj05.dto.Aj05DTO">
            <result column="AJ0501" property="aj0501"/>
            <result column="PAPERWORK_TYPE" property="paperworkType"/>
            <result column="PAPERWORK_NUMBER" property="paperworkNumber"/>
            <result column="MANAGER_NAME" property="managerName"/>
        </collection>
    </resultMap>
    <!--查询详细信息-->
    <select id="selectListById" resultMap="aj04Map">
        select a.AJ0401,
               a.PJ0101,
               a.AJ0201,
               a.JOB_TYPE,
               a.COMPANY_NAME,
               a.COMPANY_CODE,
               a.PRINCIPAL_NAME,
               a.PAPERWORK_TYPE,
               a.PAPERWORK_NUMBER,
               a.PLAN_TIME,
               a.MANAGEMENT_DEPT,
               a.TELL_TIME,
               b.PAPERWORK_TYPE,
               b.PAPERWORK_TYPE,
               b.PAPERWORK_NUMBER,
               b.MANAGER_NAME,
               b.AJ0501
        from AJ_AJ04 a,
             AJ_AJ05 b
        where a.AJ0401 = b.AJ0401
          and a.AJ0401 = #{id}
          and b.WHETHER = '1'
    </select>

    <select id="getListData" resultType="io.renren.modules.safety.aj04.dto.Aj04PageDTO">
        select a.AJ0401,
               a.PJ0101,
               b.NAME             as projectName,
               d.DEVICE_SN        as deviceSn,
               a.JOB_TYPE         as jobType,
               a.COMPANY_NAME     as companyName,
               a.COMPANY_CODE     as companyCode,
               a.PRINCIPAL_NAME   as principalName,
               a.PAPERWORK_NUMBER as paperworkNumber,
               a.PLAN_TIME        as planTime,
               a.MANAGEMENT_DEPT  as managementDept,
               a.TELL_TIME        as tellTime
        from AJ_AJ04 a,
             b_pj01 b,
             R_PJ01_DEPT c,
             AJ_AJ02 d
        where a.PJ0101 = b.PJ0101
          and b.PJ0101 = c.PJ0101
          and a.AJ0201 = d.AJ0201
          and c.DEPT_ID = #{deptId}
    </select>
</mapper>