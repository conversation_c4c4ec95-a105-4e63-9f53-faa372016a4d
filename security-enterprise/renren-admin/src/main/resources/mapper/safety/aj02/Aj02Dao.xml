<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj02.dao.Aj02Dao">
    <resultMap type="io.renren.modules.safety.aj02.dto.Aj02DTO" id="aj02Map">
        <result property="aj0201" column="AJ0201"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="deviceSn" column="DEVICE_SN"/>
        <result property="cp0201" column="CP0201"/>
        <result property="acceptanceTime" column="ACCEPTANCE_TIME"/>
        <result property="managementDept" column="MANAGEMENT_DEPT"/>
        <result property="registerDate" column="REGISTER_DATE"/>
        <result property="logoutTime" column="LOGOUT_TIME"/>
        <collection property="aj03DtoList" ofType="io.renren.modules.safety.aj03.dto.Aj03DTO">
            <result property="aj0301" column="AJ0301"></result>
            <result property="certificateNumber" column="CERTIFICATE_NUMBER"></result>
            <result property="paperworkNumber" column="PAPERWORK_NUMBER"></result>
            <result property="paperworkType" column="PAPERWORK_TYPE"></result>
            <result property="workerName" column="WORKER_NAME"></result>
        </collection>
    </resultMap>
    <!--详细信息-->
    <select id="getInfo" resultMap="aj02Map">
        select a.AJ0201,
               a.PJ0101,
               a.DEVICE_SN,
               a.CP0201,
               a.ACCEPTANCE_TIME,
               a.MANAGEMENT_DEPT,
               a.REGISTER_DATE,
               a.LOGOUT_TIME,
               b.AJ0301,
               b.CERTIFICATE_NUMBER,
               b.PAPERWORK_NUMBER,
               b.PAPERWORK_TYPE,
               b.WORKER_NAME
        from AJ_AJ02 a,
             AJ_AJ03 b
        where a.AJ0201 = b.AJ0201
          and a.AJ0201 = #{id}
          and b.WHETHER = '1'
        order by b.AJ0301
    </select>
    <!--列表分页查询-->
    <select id="getListData" resultType="io.renren.modules.safety.aj02.dto.Aj02PageDTO">
        select a.PJ0101,
               b.NAME                      as projectName,
               a.AJ0201,
               a.CP0201,
               (select f.CORPNAME
                from b_cp02 e,
                     b_cp01 f
                where e.CP0101 = f.CP0101
                  and e.CP0201 = a.CP0201) as useUnit,
               a.DEVICE_SN                 as deviceSn,
               a.ACCEPTANCE_TIME           as acceptanceTime,
               a.MANAGEMENT_DEPT           as managementDept,
               a.REGISTER_DATE             as registerDate
        from AJ_AJ02 a,
             b_pj01 b,
             R_PJ01_DEPT c
        where b.PJ0101 = c.PJ0101
          and a.PJ0101 = b.PJ0101
          and c.DEPT_ID = #{deptId}
    </select>
    <!--查询起重机械设备信息-->
    <select id="selectLiftingEquipmentInfo" resultType="io.renren.common.common.dto.CommonDto">
        select a.AJ0201 as value, a.DEVICE_SN as label
        from AJ_AJ02 a,
             b_pj01 b,
             R_PJ01_DEPT c
        where a.PJ0101 = b.PJ0101
          and c.PJ0101 = b.PJ0101
          and c.DEPT_ID = #{deptId}
    </select>
</mapper>