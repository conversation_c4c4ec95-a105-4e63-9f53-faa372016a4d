<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj08.dao.Aj08Dao">

    <resultMap type="io.renren.modules.safety.aj08.entity.Aj08Entity" id="ajAj08Map">
        <result property="aj0801" column="AJ0801"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="qualitySafetyReportNo" column="QUALITY_SAFETY_REPORT_NO"/>
        <result property="inspectionCode" column="INSPECTION_CODE"/>
        <result property="inspectionFormType" column="INSPECTION_FORM_TYPE"/>
        <result property="inspectionFormTypeCode" column="INSPECTION_FORM_TYPE_CODE"/>
        <result property="qualityType" column="QUALITY_TYPE"/>
        <result property="numberOfGroups" column="NUMBER_OF_GROUPS"/>
        <result property="recheck" column="RECHECK"/>
        <result property="witnesser" column="WITNESSER"/>
        <result property="witnessCode" column="WITNESS_CODE"/>
        <result property="witnessCompany" column="WITNESS_COMPANY"/>
        <result property="inNum" column="IN_NUM"/>
        <result property="constructionCompany" column="CONSTRUCTION_COMPANY"/>
        <result property="inspectionCompany" column="INSPECTION_COMPANY"/>
        <result property="testingOrganization" column="TESTING_ORGANIZATION"/>
        <result property="commissionNumber" column="COMMISSION_NUMBER"/>
        <result property="commissionDate" column="COMMISSION_DATE"/>
        <result property="testCost" column="TEST_COST"/>
        <result property="accordingStandard" column="ACCORDING_STANDARD"/>
        <result property="deliveryMethod" column="DELIVERY_METHOD"/>
        <result property="reportReceivingMethod" column="REPORT_RECEIVING_METHOD"/>
        <result property="monitoringParams" column="MONITORING_PARAMS"/>
        <result property="inspectionStatus" column="INSPECTION_STATUS"/>
        <result property="inspectioner" column="INSPECTIONER"/>
        <result property="curingTemperature" column="CURING_TEMPERATURE"/>
        <result property="automaticDetection" column="AUTOMATIC_DETECTION"/>
        <result property="sampleReceiver" column="SAMPLE_RECEIVER"/>
        <result property="sampleDate" column="SAMPLE_DATE"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getPageList" resultType="io.renren.modules.safety.aj08.dto.Aj08PageDTO">

        select aj0801,
               t.pj0101,
               t.name pjname,
               inspection_code,
               inspection_form_type,
               inspection_form_type_code,
               quality_type,
               number_of_groups,
               recheck,
               witnesser,
               witness_code,
               witness_company
        from b_pj01 t, aj_aj08 a where t.pj0101 = a.pj0101 and t.pj0101 = #{pj0101}

    </select>

    <select id="getDetail" resultType="io.renren.modules.safety.aj08.dto.Aj08DetailDTO">

        select a.aj0801,
               t.pj0101,
               t.name pjname,
               a.inspection_code,
               a.inspection_form_type,
               a.inspection_form_type_code,
               a.quality_type,
               a.number_of_groups,
               a.recheck,
               a.witnesser,
               a.witness_code,
               a.witness_company,
               a.in_num,
               a.construction_company,
               a.inspection_company,
               a.testing_organization,
               a.commission_number,
               a.commission_date,
               a.test_cost,
               a.according_standard,
               a.delivery_method,
               a.report_receiving_method,
               a.monitoring_params,
               a.inspection_status,
               a.inspectioner,
               a.curing_temperature,
               a.automatic_detection,
               a.sample_receiver,
               a.sample_date,
               a.creator,
               a.create_date,
               a.updater,
               a.update_date
        from b_pj01 t, aj_aj08 a where t.pj0101 = a.pj0101 and a.aj0801 = #{id}

    </select>
</mapper>