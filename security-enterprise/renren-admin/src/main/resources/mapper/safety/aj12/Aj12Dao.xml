<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj12.dao.Aj12Dao">

    <resultMap type="io.renren.modules.safety.aj12.entity.Aj12Entity" id="ajAj12Map">
        <result property="aj1201" column="AJ1201"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="ps0401" column="PS0401"/>
        <result property="jobtype" column="JOBTYPE"/>
        <result property="latestKqtime" column="LATEST_KQTIME"/>
        <result property="aj12state" column="AJ12STATE"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getList" resultType="io.renren.modules.safety.aj12.dto.Aj12DTO">
        select a.*, b.name as pjname, d.name as ps04name
        from aj_aj12 a, b_pj01 b, b_ps04 c, b_ps01 d
        where a.pj0101 = b.pj0101
          and a.ps0401 = c.ps0401
          and c.ps0101 = d.ps0101
          and a.pj0101 = #{pj0101}
        <if test="jobtype != null and jobtype != ''">
            and a.jobtype = #{jobtype}
        </if>
    </select>

</mapper>