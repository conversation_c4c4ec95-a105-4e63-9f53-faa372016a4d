<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj10.dao.Aj10Dao">

    <resultMap type="io.renren.modules.safety.aj10.entity.Aj10Entity" id="ajAj10Map">
        <result property="aj1001" column="AJ1001"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="qualitySafetyReportNo" column="QUALITY_SAFETY_REPORT_NO"/>
        <result property="inspectionFormNo" column="INSPECTION_FORM_NO"/>
        <result property="reportFormType" column="REPORT_FORM_TYPE"/>
        <result property="reportFormTypeCode" column="REPORT_FORM_TYPE_CODE"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="aj0901" column="AJ0901"/>
        <result property="cp0201" column="CP0201"/>
        <result property="entrustNo" column="ENTRUST_NO"/>
        <result property="sampleSender" column="SAMPLE_SENDER"/>
        <result property="entrustDate" column="ENTRUST_DATE"/>
        <result property="pname" column="PNAME"/>
        <result property="samplingMethod" column="SAMPLING_METHOD"/>
        <result property="witnesser" column="WITNESSER"/>
        <result property="testNature" column="TEST_NATURE"/>
        <result property="testDate" column="TEST_DATE"/>
        <result property="reportDate" column="REPORT_DATE"/>
        <result property="accordStandard" column="ACCORD_STANDARD"/>
        <result property="reportResult" column="REPORT_RESULT"/>
        <result property="reportRemark" column="REPORT_REMARK"/>
        <result property="reportStatement" column="REPORT_STATEMENT"/>
        <result property="reportApproval" column="REPORT_APPROVAL"/>
        <result property="reportCheck" column="REPORT_CHECK"/>
        <result property="reportMain" column="REPORT_MAIN"/>
        <result property="generatedDate" column="GENERATED_DATE"/>
        <result property="detectionResult" column="DETECTION_RESULT"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getCorpList" resultType="io.renren.modules.safety.aj10.dto.AjCp02LabelAndValueDTO">

        select t.cp0201 as value ,
               b.corpname as label
        from b_cp02 t, r_pj01_dept a, b_cp01 b
        where t.pj0101 = a.pj0101
          and t.cp0101 = b.cp0101
          and a.dept_id = #{deptId}

    </select>

    <select id="getPage" resultType="io.renren.modules.safety.aj10.dto.Aj10PageDTO">

        select a.aj1001,
               t.pj0101,
               t.name pjname,
               t.safetyno,
               a.inspection_form_no,
               a.report_form_type,
               a.report_form_type_code,
               a.report_no,
               a.aj0901,
               (SELECT c.corpname
                FROM b_cp01 c, b_cp02 p
                where c.cp0101 = p.cp0101
                  and p.cp0201 = a.cp0201) corpname,
               a.cp0201,
               a.entrust_no,
               a.sample_sender,
               a.entrust_date,
               a.pname,
               a.sampling_method,
               a.witnesser
        from b_pj01 t, aj_aj10 a
        where t.pj0101 = a.pj0101
          and t.pj0101 = #{pj0101}

    </select>

    <select id="getDetail" resultType="io.renren.modules.safety.aj10.dto.Aj10DetailDTO">

        select a.aj1001,
               t.pj0101,
               t.name pjname,
               t.safetyno,
               a.inspection_form_no,
               a.report_form_type,
               a.report_form_type_code,
               a.report_no,
               a.aj0901,
               (SELECT c.corpname
                FROM b_cp01 c, b_cp02 p
                where c.cp0101 = p.cp0101
                  and p.cp0201 = a.cp0201) corpname,
               a.cp0201,
               a.entrust_no,
               a.sample_sender,
               a.entrust_date,
               a.pname,
               a.sampling_method,
               a.witnesser,
               a.test_nature,
               a.test_date,
               a.report_date,
               a.accord_standard,
               a.report_result,
               a.report_remark,
               a.report_statement,
               a.report_approval,
               a.report_check,
               a.report_main,
               a.generated_date,
               a.detection_result,
               a.creator,
               a.create_date,
               a.updater,
               a.update_date
        from b_pj01 t, aj_aj10 a
        where t.pj0101 = a.pj0101
          and a.aj1001 = #{id}

    </select>
</mapper>