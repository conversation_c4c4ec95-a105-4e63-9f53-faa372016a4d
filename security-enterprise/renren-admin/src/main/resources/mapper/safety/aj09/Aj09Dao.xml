<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj09.dao.Aj09Dao">

    <resultMap type="io.renren.modules.safety.aj09.entity.Aj09Entity" id="ajAj09Map">
        <result property="aj0801" column="AJ0801"/>
        <result property="testOrganName" column="TEST_ORGAN_NAME"/>
        <result property="testOrganAddress" column="TEST_ORGAN_ADDRESS"/>
        <result property="testOrganPostalcode" column="TEST_ORGAN_POSTALCODE"/>
        <result property="testOrganTel" column="TEST_ORGAN_TEL"/>
        <result property="testOrganFax" column="TEST_ORGAN_FAX"/>
        <result property="testOrganCorpcode" column="TEST_ORGAN_CORPCODE"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getLabelAndValue" resultType="io.renren.modules.safety.aj09.dto.Aj09LabelAndValueDTO">

        SELECT t.test_organ_name label, t.aj0901 value
        FROM aj_aj09 t
        where t.dept_id = #{deptId}
        order by t.create_date desc

    </select>
</mapper>