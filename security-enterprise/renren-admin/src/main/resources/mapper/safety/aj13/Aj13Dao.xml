<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj13.dao.Aj13Dao">

    <resultMap type="io.renren.modules.safety.aj13.entity.Aj13Entity" id="ajAj13Map">
        <result property="aj1301" column="AJ1301"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="superDepartment" column="SUPER_DEPARTMENT"/>
        <result property="assignee" column="ASSIGNEE"/>
        <result property="acceptComment" column="ACCEPT_COMMENT"/>
        <result property="startDate" column="START_DATE"/>
        <result property="endDate" column="END_DATE"/>
        <result property="oversightDept" column="OVERSIGHT_DEPT"/>
        <result property="corpCode" column="CORP_CODE"/>
        <result property="troubletype" column="TROUBLETYPE"/>
        <result property="troubleinfo" column="TROUBLEINFO"/>
        <result property="feedback" column="FEEDBACK"/>
        <result property="status" column="STATUS"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="pageList" resultType="io.renren.modules.safety.aj13.dto.Aj13DTO">
        select a.*, b.name pjname
        from aj_aj13 a, b_pj01 b
        where a.pj0101 = b.pj0101
        <if test="troubletype != null and troubletype != ''">
            and a.troubletype=#{troubletype}
        </if>
        <if test="pjname != null and pjname != ''">
            and b.name like '%'||#{pjname}||'%'
        </if>
        <if test="status != null and status != ''">
            and a.status=#{status}
        </if>
    </select>

    <select id="statistics" resultType="io.renren.modules.safety.aj13.dto.Aj13TJDTO">
        select a1.dict_label troubletype,nvl(b1.ct,0) ct
        from (select b.dict_value, b.dict_label
              from Sys_Dict_Type a, SYS_DICT_DATA b
              where a.id = b.dict_type_id
                and a.dict_type = 'TROUBLETYPE') a1,
             (select aa.troubletype, count(aa.aj1301) ct
              from aj_aj13 aa
              group by aa.troubletype) b1
        where a1.dict_value = b1.troubletype(+)
    </select>

    <select id="getInfo" resultType="io.renren.modules.safety.aj13.dto.Aj13DTO">
        select a.*, b.name pjname
        from aj_aj13 a, b_pj01 b
        where a.pj0101 = b.pj0101
        and a.aj1301=#{id}
    </select>
</mapper>