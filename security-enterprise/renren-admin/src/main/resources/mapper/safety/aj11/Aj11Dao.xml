<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj11.dao.Aj11Dao">

    <resultMap type="io.renren.modules.safety.aj11.entity.Aj11Entity" id="ajAj11Map">
        <result property="aj1101" column="AJ1101"/>
        <result property="qualitySafetyReportNo" column="QUALITY_SAFETY_REPORT_NO"/>
        <result property="pj0101" column="PJ0101"/>
        <result property="highRiskProNo" column="HIGH_RISK_PRO_NO"/>
        <result property="highRiskProName" column="HIGH_RISK_PRO_NAME"/>
        <result property="isSpecialConstruction" column="IS_SPECIAL_CONSTRUCTION"/>
        <result property="highRiskProType" column="HIGH_RISK_PRO_TYPE"/>
        <result property="isOverScale" column="IS_OVER_SCALE"/>
        <result property="isDemon" column="IS_DEMON"/>
        <result property="demonDate" column="DEMON_DATE"/>
        <result property="demonResult" column="DEMON_RESULT"/>
        <result property="demonSuggest" column="DEMON_SUGGEST"/>
        <result property="creator" column="CREATOR"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updater" column="UPDATER"/>
        <result property="updateDate" column="UPDATE_DATE"/>
    </resultMap>

    <select id="getPage" resultType="io.renren.modules.safety.aj11.dto.Aj11PageDTO">

        SELECT a.aj1101,
               t.pj0101,
               t.safetyno,
               t.name pjname,
               a.high_risk_pro_no,
               a.high_risk_pro_name,
               a.is_special_construction,
               a.high_risk_pro_type,
               a.is_over_scale,
               a.is_demon,
               a.demon_date,
               a.demon_result,
               a.demon_suggest
        FROM b_pj01 t, aj_aj11 a
        where t.pj0101 = a.pj0101 and t.pj0101 = #{pj0101}

    </select>

    <select id="getDetail" resultType="io.renren.modules.safety.aj11.dto.Aj11DetailDTO">

        SELECT a.aj1101,
               t.pj0101,
               t.safetyno,
               t.name pjname,
               a.high_risk_pro_no,
               a.high_risk_pro_name,
               a.is_special_construction,
               a.high_risk_pro_type,
               a.is_over_scale,
               a.is_demon,
               a.demon_date,
               a.demon_result,
               a.demon_suggest
        FROM b_pj01 t, aj_aj11 a
        where t.pj0101 = a.pj0101 and a.aj1101 = #{id}

    </select>
</mapper>