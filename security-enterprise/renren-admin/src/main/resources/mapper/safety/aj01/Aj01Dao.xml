<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj01.dao.Aj01Dao">
    <!--分页查询-->
    <select id="getListData" resultType="io.renren.modules.safety.aj01.dto.Aj01PageDTO">
        select t.AJ0101,
               a.NAME                 as projectName,
               t.SAFETY_RECORD_NUMBER as safetyRecordNumber,
               t.SUPER_DEPARTMENT     as superDepartment,
               t.ASSIGNE<PERSON>             as assignee,
               t.ACCEPT_DATE          as acceptDate,
               t.ACCEPT_COMMENT       as acceptComment,
               t.START_DATE           as startDate,
               t.OVERSIGHT_DEPT       as oversightDept,
               t.CORP_CODE            as corpCode
        from AJ_AJ01 t,
             b_pj01 a,
             R_PJ01_DEPT b
        where t.PJ0101 = a.PJ0101
          and b.PJ0101 = a.PJ0101
          and b.DEPT_ID = #{deptId}
    </select>
</mapper>