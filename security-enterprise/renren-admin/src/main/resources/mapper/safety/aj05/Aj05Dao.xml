<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj05.dao.Aj05Dao">
    <update id="updateWhether">
        update aj_aj05 t
        set t.whether='0' where t.aj0401 = #{aj0401}
                            and t.AJ0501 not in
        <foreach collection="aj0501List" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>