<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.safety.aj03.dao.Aj03Dao">
    <resultMap type="io.renren.modules.safety.aj03.entity.Aj03Entity" id="aj03Map">
        <result property="aj0301" column="AJ0301"/>
        <result property="aj0201" column="AJ0201"/>
        <result property="workerName" column="WORKER_NAME"/>
        <result property="paperworkType" column="PAPERWORK_TYPE"/>
        <result property="paperworkNumber" column="PAPERWORK_NUMBER"/>
        <result property="certificateNumber" column="CERTIFICATE_NUMBER"/>
    </resultMap>

    <update id="updateWhether">
        update aj_aj03 t
        set t.whether='0' where t.aj0201 = #{aj0201}
                            and t.AJ0301 not in
        <foreach collection="aj0301List" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>