<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.supervision.personneldataStatistical.dao.PersonnelDataStatisticalDao">


    <select id="getWorkType"
            resultType="io.renren.modules.supervision.personneldataStatistical.dto.PersonnelDataStatisticalDTO">
            select d.dict_label worktypecode, a.workTypeCount
            from (
            select a.worktypecode, count(a.worktypecode) workTypeCount
              from b_ps02 a, r_pj01_dept b,b_pj01 c
             where a.pj0101 = b.pj0101
               and a.pj0101 = c.pj0101
               and b.dept_id = #{deptId}
               and a.worktypecode is not null
               and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
                <if test="prjstatus != null and prjstatus != ''">
                    and c.prjstatus = #{prjstatus}
                </if>
             group by a.worktypecode
             ) a,
                sys_dict_data d
            where d.dict_type_id = '206'
            and d.dict_value = a.worktypecode
            order by workTypeCount desc
    </select>
    <select id="getNativeData"
            resultType="io.renren.modules.supervision.personneldataStatistical.dto.PersonnelDataStatisticalDTO">
            select x.name nativecode, a.nativeCount
            from (
             select concat(t.native,'0000') nativecode, count(t.native) nativeCount
            from (select substr(c.idcardnumber, 1, 2) native
                from b_ps02 a, r_pj01_dept b, b_ps01 c,b_pj01 d
                where a.pj0101 = b.pj0101
                and a.pj0101 = d.pj0101
                and a.ps0101 = c.ps0101
                and b.dept_id = #{deptId}
                <if test="prjstatus != null and prjstatus != ''">
                    and d.prjstatus = #{prjstatus}
                </if>
                and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
                ) t
                group by t.native
                ) a,
                sys_region x
                where x.value = a.nativecode
                order by nativeCount desc
    </select>
    <select id="getGenderData"
            resultType="io.renren.modules.supervision.personneldataStatistical.dto.PersonnelDataStatisticalDTO">
            select x.dict_label genderCode, a.genderCount
            from (
            select t.gender genderCode, count(t.gender) genderCount
            from (select c.gender
                   from b_ps02 a, r_pj01_dept b, b_ps01 c,b_pj01 d
                  where a.pj0101 = b.pj0101
                    and a.pj0101 = d.pj0101
                    and a.ps0101 = c.ps0101
                    and b.dept_id = #{deptId}
                    and c.gender is not null
                    <if test="prjstatus != null and prjstatus != ''">
                        and d.prjstatus = #{prjstatus}
                    </if>
                    and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
                    ) t
                group by t.gender
                )a,
            sys_dict_data x
            where x.dict_type_id = '1160061077912858625'
            and x.dict_value = a.genderCode
            order by genderCount desc
    </select>
    <select id="getAgeData"
            resultType="io.renren.modules.supervision.personneldataStatistical.dto.PersonnelDataStatisticalDTO">
                select x.ageGroup, count(x.ageGroup) ageCount
                from (select case
                when z.age >= 18 and age &lt;= 23 then
                '18岁-23岁'
                when z.age >= 24 and age &lt;= 29 then
                '24岁-29岁'
                when z.age >= 30 and age &lt;= 34 then
                '30岁-34岁'
                when z.age >= 35 and age &lt;= 39 then
                '35岁-39岁'
                when z.age >= 40 and age &lt;= 44 then
                '40岁-44岁'
                when z.age >= 45 and age &lt;= 49 then
                '45岁-49岁'
                when z.age >= 50 and age &lt;= 54 then
                '50岁-54岁'
                when z.age >= 55 and age &lt;= 59 then
                '55岁-59岁'
                when z.age >= 60 and age &lt;= 64 then
                '60岁-64岁'
                else
                '65岁以上'
                end as ageGroup
                from (select to_char(sysdate, 'yyyy') -
                substr(t.idcardnumber, 7, 4) as age
                from (select c.idcardnumber
                from b_ps02 a, r_pj01_dept b, b_ps01 c,b_pj01 d
                where a.pj0101 = b.pj0101
                and a.pj0101 = d.pj0101
                and a.ps0101 = c.ps0101
                and b.dept_id = #{deptId}
                <if test="prjstatus != null and prjstatus != ''">
                    and d.prjstatus = #{prjstatus}
                </if>
                and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
                )t
                )z
                )x
                group by x.ageGroup
                order by ageGroup
    </select>
    <select id="workTypeCount" resultType="double">
            select count(a.worktypecode) count
            from b_ps02 a, r_pj01_dept b,b_pj01 d
          where a.pj0101 = b.pj0101
            and a.pj0101 = d.pj0101
            <if test="prjstatus != null and prjstatus != ''">
                and d.prjstatus = #{prjstatus}
            </if>
            and b.dept_id = #{deptId}
            and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
            and a.worktypecode is not null
    </select>
    <select id="allNativeData" resultType="java.lang.Double">
           select count(c.idcardnumber) nativeCount
           from b_ps02 a, r_pj01_dept b, b_ps01 c,b_pj01 d
          where a.pj0101 = b.pj0101
            and a.ps0101 = c.ps0101
            and a.pj0101 = d.pj0101
            <if test="prjstatus != null and prjstatus != ''">
                and d.prjstatus = #{prjstatus}
            </if>
            and b.dept_id = #{deptId}
            and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
    </select>
    <select id="allGenderData" resultType="java.lang.Double">
          select count(c.gender) genderCount
          from b_ps02 a, r_pj01_dept b, b_ps01 c,b_pj01 d
          where a.pj0101 = b.pj0101
          and a.ps0101 = c.ps0101
          and a.pj0101 = d.pj0101
          <if test="prjstatus != null and prjstatus != ''">
                and d.prjstatus = #{prjstatus}
          </if>
          and b.dept_id = #{deptId}
          and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
    </select>
    <select id="allAgeData" resultType="java.lang.Double">
           select count(x.age)
      from (select to_char(sysdate,'yyyy') - substr(t.idcardnumber, 7, 4) as age
              from (select c.idcardnumber
                      from b_ps02 a, r_pj01_dept b, b_ps01 c,b_pj01 d
                     where a.pj0101 = b.pj0101
                       and a.pj0101 = d.pj0101
                        <if test="prjstatus != null and prjstatus != ''">
                            and d.prjstatus = #{prjstatus}
                        </if>
                       and a.ps0101 = c.ps0101
                       and b.dept_id = #{deptId}
                       and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)) t
                       ) x
    </select>


</mapper>