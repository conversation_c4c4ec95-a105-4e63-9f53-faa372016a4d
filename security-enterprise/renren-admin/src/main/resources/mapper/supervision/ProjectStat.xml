<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.supervision.projectStatistic.dao.ProjectStatDao">
    <!--项目统计信息-->
    <select id="selectDataList" resultType="io.renren.modules.supervision.projectStatistic.dto.ProjectStatDTO">
        select a.name                        as areaName,
               a.areaCode                    as areaCode,
               nvl(c.projectcount, '0')      as projectCount,
               nvl(e.projectundercount, '0') as projectUnderCount,
               nvl(m.projectmonthcount, '0') as projectMonthCount
        from (select t.name, t.value as areaCode, substr(t.value, 0, ${treeLevel}) as value
              from sys_region t
        <where>
            <choose>
                <when test="treeLevel != null and treeLevel == 8">
                    t.id = (select p.id from sys_region p where p.value = #{areaCode})
                </when>
                <otherwise>
                    and t.pid = (select p.id from sys_region p where p.value = #{areaCode})
                </otherwise>
            </choose>
        </where>
        ) a
            left join (select substr(b.areacode, 0, ${treeLevel}) as areacode
                            , count(0)                            as projectCount
                       from b_pj01 b,
                            R_PJ01_DEPT R_PJ01_DEPT where b.PJ0101 = R_PJ01_DEPT.PJ0101
                                                      and R_PJ01_DEPT.DEPT_ID = #{deptId}
                                                      and b.PJ0101 in (<include
            refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>)
    group by substr(b.areacode, 0, ${treeLevel})) c
              on a.value = c.areacode
                  left join (select substr(d.areacode, 0, ${treeLevel}) as areacode,
                                    count(0)                            as projectUnderCount
                             from b_pj01 d,
                                  R_PJ01_DEPT R_PJ01_DEPT where d.PJ0101 = R_PJ01_DEPT.PJ0101
                                                            and R_PJ01_DEPT.DEPT_ID = #{deptId}
                                                            and d.PJ0101 in (<include
            refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>)
                and d.prjstatus = '3'
    group by substr(d.areacode, 0, ${treeLevel})) e
              on a.value = e.areacode
                  left join (select substr(f.areacode, 0, ${treeLevel}) as areacode,
                                    count(0)                            as projectMonthCount
                             from b_pj01 f,
                                  R_PJ01_DEPT R_PJ01_DEPT where f.PJ0101 = R_PJ01_DEPT.PJ0101
                                                            and R_PJ01_DEPT.DEPT_ID = #{deptId}
                                                            and f.PJ0101 in (<include
            refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>)
                and to_char(f.create_date, 'yyyy-MM') =
                    to_char(sysdate, 'yyyy-MM')
    group by substr(f.areacode, 0, ${treeLevel})) m
              on a.value = m.areacode
    order by a.value
    </select>
    <!--项目信息分页-->
    <select id="selectPageList" resultType="io.renren.modules.supervision.projectStatistic.dto.ProjectStatPageDTO">
        select t.pj0101,
               t.name                                                    projectName,
               t.areacode                                                areaCode,
               t.linkman                                                 linkMan,
               t.linkphone                                               linkPhone,
               nvl(b.workercount, '0')                                   workerCount,
               nvl(d.managercount, '0')                                  managerCount,
               (select count(0) from b_kq02 e where t.pj0101 = e.pj0101) attendanceCount,
               nvl(r.teamcount, '0')                                     teamCount,
               nvl(n.partunitcount, '0')                                 partUnitCount
        from b_pj01 t
                 left join (select a.pj0101, count(0) workerCount
                            from b_ps02 a
                            where a.in_or_out = '1'
                            group by a.pj0101) b
                           on t.pj0101 = b.pj0101
                 left join (select c.pj0101, count(0) managerCount
                            from b_ps04 c
                            where c.in_or_out = '1'
                            group by c.pj0101) d
                           on t.pj0101 = d.pj0101
                 left join (select q.pj0101, count(0) teamCount
                            from b_tm01 q
                            group by q.pj0101) r
                           on t.pj0101 = r.pj0101
                 left join (select m.pj0101, count(0) partUnitCount
                            from b_cp02 m
                            group by m.pj0101) n
                           on t.pj0101 = n.pj0101
        where t.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>)
        <if test="params.areaCode != '' and params.areaCode != null">
            and t.areacode like '' || #{params.areaCode} || '%'
        </if>
        <if test="params.projectStat != null and params.projectStat != ''">
            and t.prjstatus = #{params.projectStat}
        </if>
        <if test="params.month != '' and params.month != null">
            and to_char(t.create_date, 'yyyy-MM') = #{params.month}
        </if>
        <if test="params.projectName != '' and params.projectName != null">
            and t.NAME like '' || #{params.projectName} || '%'
        </if>

        order by t.CREATE_DATE desc
    </select>
</mapper>