<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.supervision.equipmentStatistical.dao.EquipmentStatisticalDao">
    <select id="getAreaCodePageData"
            resultType="io.renren.modules.supervision.equipmentStatistical.dto.EquipmentStatisticalDTO">

        SELECT t.VALUE as areaCode,t.name as areaName,
        nvl(q.outEquipmentNum,0) as outEquipmentNum,
        nvl(q.inEquipmentNum,0) as inEquipmentNum,
        nvl(q.totalEquipmentNum,0) as totalEquipmentNum
        FROM sys_region t
        left join
        ( SELECT substr(p.areacode,0,${level}*2+2)as areacode ,
        count(k.kq0101)  as totalEquipmentNum,
        count(decode(nvl(k.network_status,'2'), '2',1,null)) as outEquipmentNum,
        count(decode(nvl(k.network_status,'2'), '1',1,null)) as inEquipmentNum
        FROM b_kq01 k,b_pj01 p ,R_PJ01_DEPT m
        WHERE p.pj0101 = k.pj0101
        and p.PJ0101=m.PJ0101
        and m.DEPT_ID=#{deptId}
        and p.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
        and  k.whether = '1'  GROUP BY substr(p.areacode,0,${level}*2+2) ) q
        on
        substr(t.value,0,${level}*2+2) = q.areacode
        <where>
            <choose>
                <when test="level != null and level == 3">
                    t.id = (select p.id from sys_region p where p.value = #{areaCode})
                </when>
                <otherwise>
                    and t.pid = (select p.id from sys_region p where p.value = #{areaCode})
                </otherwise>
            </choose>
        </where>
    order by areaCode
    </select>

    <select id="getOutEquipmentPageData"
            resultType="io.renren.modules.supervision.equipmentStatistical.dto.OutEquipmentDTO">
        SELECT
                p.pj0101,
                p.name as projectName,
                p.linkman as linkMan,
                p.linkphone as telephoneNumber,
               decode(k.NETWORK_STATUS,'1','在线','离线') as equipmentState,
               k.deviceserialno as equipmentNo

        FROM b_pj01 p ,b_kq01 k
        WHERE p.pj0101 = k.pj0101
        and p.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
        and k.whether = '1'
        <if test="params.network_status != '' and params.network_status != null">
            and k.network_status = #{params.network_status}
        </if>
        <if test="params.prjstatus != '' and params.prjstatus != null">
            and p.prjstatus = #{params.prjstatus}
        </if>
        <if test="params.areaCode != '' and params.areaCode != null">
            and p.areacode like #{params.areaCode}||'%'
        </if>
        <if test="params.projectName != '' and params.projectName != null">
            and p.name like  #{params.projectName}
        </if>
        <if test="params.equipmentNo != '' and params.equipmentNo != null">
            and k.deviceserialno = #{params.equipmentNo}
        </if>
    </select>
</mapper>