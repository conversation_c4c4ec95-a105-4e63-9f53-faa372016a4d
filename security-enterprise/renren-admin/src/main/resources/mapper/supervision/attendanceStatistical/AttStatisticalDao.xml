<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.supervision.attendanceStatistical.dao.AttStatisticalDao">
    <!-- 查询考勤统计数据 -->
    <select id="getPageData" resultType="io.renren.modules.supervision.attendanceStatistical.dto.AttStatisticalDTO">
        SELECT q.*,
               decode(q.managerNum + q.workerNum, 0, '0.00%', to_char(
                                                                      NVL(ROUND(
                                                                                  (q.attManagerNum2 + q.attWorkerNum2) / ((q.managerNum + q.workerNum) * (to_date(#{endDate},
        'yyyy-MM-dd') -
        to_date(#{startDate},
        'yyyy-MM-dd') + 1)),
                                                                                  4) * 100, 0), 'fm9999999990.00') ||
                                                              '%')
                   as totalRate
        FROM (
        SELECT t.name                                                                                as projectName,
               t.pj0101,
               decode(s2.workerNum, null, 0, s2.workerNum)                                           as workerNum,
               decode(k.attWorkerNum, null, 0, k.attWorkerNum)                                       as attWorkerNum,
               decode(k.attWorkerNum2, null, 0, k.attWorkerNum2)                                     as attWorkerNum2,
               to_char(NVL(ROUND(attWorkerNum2 / (workerNum * (to_date(#{endDate},'yyyy-MM-dd') - to_date(#{startDate},'yyyy-MM-dd') + 1)), 4) * 100, 0), 'fm9999999990.00') || '%'   as workerRate,
               decode(s4.managerNum, null, 0, s4.managerNum)                                         as managerNum,
               decode(k2.attManagerNum, null, 0, k2.attManagerNum)                                   as attManagerNum,
               decode(k2.attManagerNum2, null, 0, k2.attManagerNum2)                                 as attManagerNum2,
               to_char(NVL(ROUND(attManagerNum2 / (managerNum * (to_date(#{endDate},'yyyy-MM-dd') - to_date(#{startDate},'yyyy-MM-dd') + 1)), 4) * 100, 0), 'fm9999999990.00') || '%' as managerRate
        FROM
        (SELECT t.name, t.pj0101
         FROM b_pj01 t,
              R_PJ01_DEPT R_PJ01_DEPT  where t.PJ0101 = R_PJ01_DEPT.PJ0101
                                         and R_PJ01_DEPT.DEPT_ID = #{deptId}
                                         and t.PJ0101 in (<include
            refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>)
                and t.prjstatus = '3'
        <if test="projectName != null and projectName != ''">
            and t.name like #{projectName}
        </if>
        <if test="areaCode != null and areaCode != ''">
            and t.areacode LIKE #{areaCode} || '%'
        </if>
        )t
            left join
        (SELECT s2.pj0101, nvl(count(s2.ps0201), 0) as workerNum
         FROM b_ps02 s2
         WHERE s2.in_or_out = '1'
         GROUP BY s2.pj0101) s2
        on s2.pj0101 = t.pj0101
            left join
        (SELECT k.pj0101, nvl(count(distinct k.user_id), 0) as attWorkerNum,
                          nvl(count(distinct k.user_id || to_char(k.CHECKDATE, 'yyyy-MM-dd')), 0) as attWorkerNum2
         FROM b_kq02 k,
              b_ps02 s2
        WHERE k.user_id = s2.ps0201
          and s2.in_or_out = '1'
        <if test="startDate != null and startDate != ''">
            and to_char(k.checkdate, 'yyyy-MM-dd') >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and to_char(k.checkdate, 'yyyy-MM-dd') &lt;= #{endDate}
        </if>
        GROUP BY k.pj0101 )k
        on k.pj0101 = t.pj0101
            left join
        (SELECT s4.pj0101, count(s4.ps0401) as managerNum FROM b_ps04 s4 WHERE s4.in_or_out = '1' GROUP BY s4.pj0101) s4
        on s4.pj0101 = t.pj0101
            left join
        (SELECT k.pj0101, nvl(count(distinct k.user_id), 0) as attManagerNum,
                          nvl(count(distinct k.user_id || to_char(k.CHECKDATE, 'yyyy-MM-dd')), 0) as attManagerNum2
         FROM b_kq02 k,
              b_ps04 s4
        WHERE k.user_id = s4.ps0401
          and s4.in_or_out = '1'
        <if test="startDate != null and startDate != ''">
            and to_char(k.checkdate, 'yyyy-MM-dd') >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and to_char(k.checkdate, 'yyyy-MM-dd') &lt;= #{endDate}
        </if>
        GROUP BY k.pj0101 )k2
        on k2.pj0101 = t.pj0101
        ) q   where 1 = 1
        <if test='type != null and type == "1"'>
            and attWorkerNum || attManagerNum != '00'
        </if>
        <if test='type != null and type == "2"'>
            and attWorkerNum = 0
            and attManagerNum = 0
        </if>
        ORDER BY to_number(replace(totalRate, '%')) desc, workerNum desc
        <!--decode(q.managerNum+q.workerNum,0,0, NVL(ROUND((q.attManagerNum+q.attWorkerNum)/(q.managerNum+q.workerNum), 4) *
        100,0))
        desc-->
    </select>

    <select id="getAreaCodePageData"
            resultType="io.renren.modules.supervision.attendanceStatistical.dto.AreaAttStatisticalDTO">
        SELECT c.*,
               to_char(decode(c.attProjectNum, 0, 0.00, ROUND(c.attProjectNum / c.inProjectNum, 4) * 100),
                       'fm9999999990.00') || '%'
                                                as attProjectRate,
               c.inProjectNum - c.attProjectNum as noAttProjectNum
        FROM (
        SELECT t.value                 as areaCode,
               t.name                  as areaName,
               nvl(p.inProjectNum, 0)  as inProjectNum,
               nvl(k.attProjectNum, 0) as attProjectNum
        FROM sys_region t
            left join
        (SELECT substr(p.areacode, 0, ${level} * 2 + 2) as areacode, count(p.pj0101) as inProjectNum
         FROM b_pj01 p WHERE
            p.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>)
                and p.prjstatus = '3'
    GROUP BY substr(p.areacode, 0, ${level} * 2 + 2) ) p
              on substr(t.value, 0, ${level} * 2 + 2) = p.areacode
                  left join
    (SELECT substr(p.areacode, 0, ${level} * 2 + 2) as areacode, count(distinct k.pj0101) as attProjectNum
     FROM b_kq02 k,
          b_pj01 p where p.pj0101 = k.pj0101
                     and p.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>)
                and p.prjstatus = '3'
        <if test="checkDate != null and checkDate != ''">
            and to_char(k.checkdate, 'yyyy-MM-dd') = #{checkDate}
        </if>
        GROUP BY substr(p.areacode, 0, ${level} * 2 + 2)) k
        on k.areacode = substr(t.value, 0, ${level} * 2 + 2)
        <where>
            <choose>
                <when test="level != null and level == 3">
                    t.id = (select p.id from sys_region p where p.value = #{areaCode})
                </when>
                <otherwise>
                    and t.pid = (select p.id from sys_region p where p.value = #{areaCode})
                </otherwise>
            </choose>
        </where>
        )
            c
        ORDER BY areaCode
        <!--to_number(replace(attProjectRate,'%')) desc-->
    </select>
</mapper>