<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.supervision.attendanceStatistical.dao.AttendancePersonDao">


    <select id="getPageDataByIsOut"
            resultType="io.renren.modules.supervision.attendanceStatistical.dto.AttendancePersonDTO">
                select distinct b.name,
                b.idcardnumber,
                b.nation,
                b.gender,
                b.cellphone,
                a.worktypecode,
                a.ps0201 userid
                from b_ps02 a, b_ps01 b
                where a.pj0101 = #{pj0101}
                and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
                and a.ps0101 = b.ps0101
                and a.in_or_out = '1'
    </select>
    <select id="getPageDataByIsCheck"
            resultType="io.renren.modules.supervision.attendanceStatistical.dto.AttendancePersonDTO">
                select distinct b.name,
                b.idcardnumber,
                b.nation,
                b.gender,
                b.cellphone,
                a.worktypecode,
                a.ps0201 userid
                from b_ps02 a, b_ps01 b,b_kq02 c
                where a.pj0101 = #{pj0101}
                and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
                and a.ps0101 = b.ps0101
                and a.in_or_out = '1'
                and c.user_id = a.ps0201
                and to_char(c.checkdate,'yyyy-MM-dd') = #{checkDate}
    </select>
    <select id="getManagePageDataByIsOut"
            resultType="io.renren.modules.supervision.attendanceStatistical.dto.AttendancePersonDTO">
                select distinct b.name,
                b.idcardnumber,
                b.nation,
                b.gender,
                b.cellphone,
                a.jobtype,
                a.ps0401 userid
                from b_ps04 a, b_ps01 b
                where a.pj0101 = #{pj0101}
                and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
                and a.ps0101 = b.ps0101
                and a.in_or_out = '1'
    </select>
    <select id="getManagePageDataByIsCheck"
            resultType="io.renren.modules.supervision.attendanceStatistical.dto.AttendancePersonDTO">
                select distinct b.name,
                b.idcardnumber,
                b.nation,
                b.gender,
                b.cellphone,
                a.jobtype,
                a.ps0401 userid
                from b_ps04 a, b_ps01 b,b_kq02 c
                where a.pj0101 = #{pj0101}
                and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
                and a.ps0101 = b.ps0101
                and a.in_or_out = '1'
                and c.user_id = a.ps0401
                and to_char(c.checkdate,'yyyy-MM-dd') = #{checkDate}
    </select>
    <select id="getKqByMonth" resultType="java.lang.String">
                select distinct to_char(a.checkdate, 'yyyy-MM-dd')
                from b_kq02 a
                where a.user_id = #{userId}
                and to_char(a.checkdate, 'yyyy-MM') = #{checkDate}
                and a.PJ0101 in (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
                order by to_char(a.checkdate, 'yyyy-MM-dd')
    </select>
</mapper>