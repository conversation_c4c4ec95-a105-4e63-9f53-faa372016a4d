<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.supervision.continuousNoAttendanceStatistical.dao.NoAttendanceStatisticalDao">
    <select id="getAbsenceDays"
            resultType="io.renren.modules.supervision.continuousNoAttendanceStatistical.dto.NoAttendanceStatisticalDTO">
        SELECT r.areacode, r.pj0101, r.name, r.linkman, r.linkphone, r.absencedays
        FROM (select a.areacode,
                     a.pj0101,
                     a.name,
                     a.linkman,
                     a.linkphone,
                     round(to_number(sysdate - fday)) absencedays
        from b_pj01 a,
             (select pj0101, max(CHECKDATE) fday
              from b_kq02 t
              group by pj0101) b,
        (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>) z
    where prjstatus = 3
        <if test="params.areacode != null and params.areacode.trim() != ''">
            and areacode like #{params.areacode} || '%'
        </if>
        and a.pj0101 not in
            (select x.pj0101
             from b_kq02 x
             where TO_CHAR(CHECKDATE, 'yyyy-MM-dd') >=
                   TO_CHAR(sysdate, 'yyyy-MM-dd'))
        and a.pj0101 = b.pj0101
        and a.pj0101 = z.pj0101
        order by a.areacode desc) r
        where 1 = 1
        <if test="params.absencedays != null and params.absencedays.trim() != ''">
            and r.absencedays <![CDATA[ >= ]]> #{params.absencedays}
        </if>
    </select>

    <select id="exportAbsenceDays"
            resultType="io.renren.modules.supervision.continuousNoAttendanceStatistical.dto.NoAttendanceStatisticalDTO">
        SELECT r.areacode, r.pj0101, r.name, r.linkman, r.linkphone, r.absencedays
        FROM (select a.areacode,
                     a.pj0101,
                     a.name,
                     a.linkman,
                     a.linkphone,
                     round(to_number(sysdate - fday)) absencedays
        from b_pj01 a,
             (select pj0101, max(CHECKDATE) fday
              from b_kq02 t
              group by pj0101) b,
        (<include refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>) z
    where prjstatus = 3
        <if test="params.areacode != null and params.areacode.trim() != ''">
            and areacode like #{params.areacode} || '%'
        </if>
        and a.pj0101 not in
            (select x.pj0101
             from b_kq02 x
             where TO_CHAR(CHECKDATE, 'yyyy-MM-dd') >=
                   TO_CHAR(sysdate, 'yyyy-MM-dd'))
        and a.pj0101 = b.pj0101
        and a.pj0101 = z.pj0101
        order by a.areacode desc) r
        where 1 = 1
        <if test="params.absencedays != null and params.absencedays.trim() != ''">
            and r.absencedays <![CDATA[ >= ]]> #{params.absencedays}
        </if>
    </select>
</mapper>