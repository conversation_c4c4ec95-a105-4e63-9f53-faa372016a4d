<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="io.renren.modules.supervision.home.dao.HomeDao">

    <select id="getCurrentArea" resultType="java.lang.String">
        select r.name
        from sys_dept t,
             sys_region r
        where t.areacode = r.value
          and t.id = #{deptId}
    </select>

    <select id="getProjectSum" resultType="java.lang.Long">
        select count(*)
        from b_pj01 t,
        r_pj01_dept a
        where t.pj0101 = a.pj0101
        and a.dept_id = #{deptId} and t.pj0101 in (<include
            refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
    </select>

    <select id="getConstructionProjects" resultType="java.lang.Long">
        select count(*)
        from b_pj01 t,
        r_pj01_dept a
        where t.pj0101 = a.pj0101
        and t.prjstatus = '3' and a.dept_id = #{deptId} and t.pj0101 in (<include
            refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
    </select>

    <select id="getManagerSum" resultType="java.lang.Long">
        select count(*)
        from b_ps04 t
        where t.pj0101 in (select b.pj0101
        from b_Pj01 b,
        r_pj01_dept r
        where b.pj0101 = r.pj0101
        and b.pj0101 in
        (<include
            refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
        and r.dept_id = #{deptId}) and t.in_or_out='1'
    </select>

    <select id="getWorkerSum" resultType="java.lang.Long">
        select count(*)
        from b_ps02 t
        where t.pj0101 in (select b.pj0101
        from b_Pj01 b,
        r_pj01_dept r
        where b.pj0101 = r.pj0101
        and b.pj0101 in
        (<include
            refid="io.renren.common.common.dao.CommonDao.selectRealNameProject"></include>)
        and r.dept_id = #{deptId}) and t.in_or_out='1'
    </select>

    <select id="getPersonAttendance" resultType="java.lang.Long">
        select count(distinct(c.user_id))
        from b_pj01 a, r_pj01_dept b, b_kq02 c
        where a.pj0101 = b.pj0101
          and a.pj0101 = c.pj0101
          and b.dept_id = #{deptId} and to_char(c.checkdate,'yyyy-MM-dd') = to_char(sysdate,'yyyy-MM-dd')
    </select>

    <select id="statisticsAttendance" resultType="io.renren.modules.supervision.home.dto.StatisticsAttendance">
        select t.today days,
        (select count(distinct(c.user_id))
        from b_pj01 a, r_pj01_dept b, b_kq02 c
        where a.pj0101 = b.pj0101
        and a.pj0101 = c.pj0101
        and b.dept_id = #{deptId}
        and to_char(c.checkdate, 'yyyy-MM-dd') = t.today) attendanceNum
        from (SELECT to_char(SYSDATE - LEVEL + 1, 'yyyy-MM-dd') today
        FROM DUAL
        connect BY LEVEL <![CDATA[<=]]> 7) t order by t.today
    </select>

</mapper>