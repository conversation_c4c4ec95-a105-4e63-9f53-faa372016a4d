<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.supervision.map.dao.ProjectMapDao">
    <!--查询项目信息-->
    <select id="selectProjectInfo" resultType="io.renren.modules.supervision.map.dto.ProjectMapDTO">
        select a.pj0101, a.name as projectName, a.lng, a.lat, a.PRJSTATUS as projectStatus
        from b_pj01 a,
             r_pj01_dept b
        where a.pj0101 = b.pj0101
          and b.dept_id = #{deptId}
        <if test="projectStatus != null and projectStatus != ''">
            and a.PRJSTATUS = #{projectStatus}
        </if>
        <if test="areaCode != null and areaCode != ''">
            and a.AREACODE like '' || #{areaCode} || '%'
        </if>
        <if test="projectName != null and projectName != ''">
            and a.NAME like '%' || #{projectName} || '%'
        </if>
        order by a.AREACODE
    </select>
</mapper>