<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.enterprise.projectledger.dao.ProjectLedgerDao">
    <select id="selectProjectLedgerEntityList"
            resultType="io.renren.modules.enterprise.projectledger.entity.ProjectLedgerEntity">
        select p.*,
               decode(p.workerInNumZb, 0, 0, ROUND(p.workerAttendanceNumZb * 100 / p.workerInNumZb, 2)) ||
               '%' as workerAttendanceRateZb,
               decode(p.managerInNumZb, 0, 0, ROUND(p.managerAttendanceNumZb * 100 / p.managerInNumZb, 2)) ||
               '%' as managerAttendanceRateZb,
               decode(p.projectManagerCount, 0, 0, ROUND(p.projectManagerAttCount * 100 / p.projectManagerCount, 2)) ||
               '%' as projectManagerAttendanceRateZb,
               decode(p.workerInNumJl, 0, 0, ROUND(p.workerAttendanceNumJl * 100 / p.workerInNumJl, 2)) ||
               '%' as workerAttendanceRateJl,
               decode(p.managerInNumJl, 0, 0, ROUND(p.managerAttendanceNumJl * 100 / p.managerInNumJl, 2)) ||
               '%' as managerAttendanceRateJl,
               decode(p.generalManagerCount, 0, 0, ROUND(p.generalManagerAttCount * 100 / p.generalManagerCount, 2)) ||
               '%' as supervisorAttendanceRateJl,
               decode(p.workerInNumJs, 0, 0, ROUND(p.workerAttendanceNumJs * 100 / p.workerInNumJs, 2)) ||
               '%' as workerAttendanceRateJs,
               decode(p.managerInNumJs, 0, 0, ROUND(p.managerAttendanceNumJs * 100 / p.managerInNumJs, 2)) ||
               '%' as managerAttendanceRateJs,
               decode(p.workerInNumQt, 0, 0, ROUND(p.workerAttendanceNumQt * 100 / p.workerInNumQt, 2)) ||
               '%' as workerAttendanceRateQt,
               decode(p.managerInNumQt, 0, 0, ROUND(p.managerAttendanceNumQt * 100 / p.managerInNumQt, 2)) ||
               '%' as managerAttendanceRateQt,
               decode(p.totalWorkerInNum, 0, 0, ROUND(p.totalWorkerAttendanceNum * 100 / p.totalWorkerInNum, 2)) ||
               '%' as totalWorkerAttendanceRate,
               decode(p.totalManagerInNum, 0, 0, ROUND(p.totalManagerAttendanceNum * 100 / p.totalManagerInNum, 2)) ||
               '%' as totalManagerAttendanceRate
        from (select t.pj0101,
                     t.name                                                       as projectName,
                     (select x.name from sys_region x where t.areacode = x.value) as areaCode,
                     (select count(0)
                      from b_kq01 y
                      where t.pj0101 = y.pj0101
                        and y.whether = '1'
                        and y.network_status = '1')                               as equipmentNum,
                     t.linkman                                                    as linkMan,
                     t.linkphone                                                  as linkCellphone,
                     nvl(t1.workerCount, '0')                                     as totalWorkerInNum,
                     nvl(t2.workerAttendanceCount, '0')                           as totalWorkerAttendanceNum,
                     nvl(t3.managerCount, '0')                                    as totalManagerInNum,
                     nvl(t4.managerAttendanceCount, '0')                          as totalManagerAttendanceNum,
                     nvl(t5.workerCount, '0')                                     as workerInNumZb,
                     nvl(t6.workerAttendanceCount, '0')                           as workerAttendanceNumZb,
                     nvl(t7.managerCount, '0')                                    as managerInNumZb,
                     nvl(t8.managerAttendanceCount, '0')                          as managerAttendanceNumZb,
                     nvl(t9.managerJobTypeCount, '0')                             as projectManagerCount,
                     nvl(t10.managerJobTypeAttCount, '0')                         as projectManagerAttCount,
                     nvl(t11.workerCount, '0')                                    as workerInNumJs,
                     nvl(t12.workerAttendanceCount, '0')                          as workerAttendanceNumJs,
                     nvl(t13.managerCount, '0')                                   as managerInNumJs,
                     nvl(t14.managerAttendanceCount, '0')                         as managerAttendanceNumJs,
                     nvl(t15.workerCount, '0')                                    as workerInNumJl,
                     nvl(t16.workerAttendanceCount, '0')                          as workerAttendanceNumJl,
                     nvl(t17.managerCount, '0')                                   as managerInNumJl,
                     nvl(t18.managerAttendanceCount, '0')                         as managerAttendanceNumJl,
                     nvl(t19.managerJobTypeCount, '0')                            as generalManagerCount,
                     nvl(t20.managerJobTypeAttCount, '0')                         as generalManagerAttCount,
                     nvl(t21.workerCount, '0')                                    as workerInNumQt,
                     nvl(t22.workerAttendanceCount, '0')                          as workerAttendanceNumQt,
                     nvl(t23.managerCount, '0')                                   as managerInNumQt,
                     nvl(t24.managerAttendanceCount, '0')                         as managerAttendanceNumQt
        from b_pj01 t
        <!--项目总体情况今日考勤统计-->
        left join (
        <include refid="countWorkerNumByCorpType">
            <property name="corpType" value="''"/>
        </include>
        ) t1 on t.PJ0101 = t1.PJ0101
            left join (
        <include refid="countWorkerAttendanceNumByCorpType">
            <property name="corpType" value="''"/>
            <property name="day" value="''"/>
        </include>
        ) t2 on t.PJ0101 = t2.PJ0101
            left join (
        <include refid="countManagerNumByCorpType">
            <property name="corpType" value="''"/>
        </include>
        ) t3 on t.PJ0101 = t3.PJ0101
            left join (
        <include refid="countManagerAttendanceNumByCorpType">
            <property name="corpType" value="''"/>
        </include>
        ) t4 on t.PJ0101 = t4.PJ0101
        <!--总包单位今日考勤统计-->
        left join (
        <include refid="countWorkerNumByCorpType">
            <property name="corpType" value="'9'"/>
        </include>
        ) t5 on t.PJ0101 = t5.PJ0101
            left join (
        <include refid="countWorkerAttendanceNumByCorpType">
            <property name="corpType" value="'9'"/>
        </include>
        ) t6 on t.PJ0101 = t6.PJ0101
            left join (
        <include refid="countManagerNumByCorpType">
            <property name="corpType" value="'9'"/>
        </include>
        ) t7 on t.PJ0101 = t7.PJ0101
            left join (
        <include refid="countManagerAttendanceNumByCorpType">
            <property name="corpType" value="'9'"/>
        </include>
        ) t8 on t.PJ0101 = t8.PJ0101
            left join (
        <include refid="countManagerNumByCorpTypeAndJobType">
            <property name="corpType" value="'9'"/>
            <property name="jobType" value="'1009'"/>
        </include>
        ) t9 on t.PJ0101 = t9.PJ0101
            left join (
        <include refid="countManagerAttendanceNumByCorpTypeAndJobType">
            <property name="corpType" value="'9'"/>
            <property name="jobType" value="'1009'"/>
        </include>
        ) t10 on t.PJ0101 = t10.PJ0101
        <!--建设单位今日考勤统计-->
        left join (
        <include refid="countWorkerNumByCorpType">
            <property name="corpType" value="'8'"/>
        </include>
        ) t11 on t.PJ0101 = t11.PJ0101
            left join (
        <include refid="countWorkerAttendanceNumByCorpType">
            <property name="corpType" value="'8'"/>
        </include>
        ) t12 on t.PJ0101 = t12.PJ0101
            left join (
        <include refid="countManagerNumByCorpType">
            <property name="corpType" value="'8'"/>
        </include>
        ) t13 on t.PJ0101 = t13.PJ0101
            left join (
        <include refid="countManagerAttendanceNumByCorpType">
            <property name="corpType" value="'8'"/>
        </include>
        ) t14 on t.PJ0101 = t14.PJ0101
        <!--监理单位今日考勤统计-->
        left join (
        <include refid="countWorkerNumByCorpType">
            <property name="corpType" value="'7'"/>
        </include>
        ) t15 on t.PJ0101 = t15.PJ0101
            left join (
        <include refid="countWorkerAttendanceNumByCorpType">
            <property name="corpType" value="'7'"/>
        </include>
        ) t16 on t.PJ0101 = t16.PJ0101
            left join (
        <include refid="countManagerNumByCorpType">
            <property name="corpType" value="'7'"/>
        </include>
        ) t17 on t.PJ0101 = t17.PJ0101
            left join (
        <include refid="countManagerAttendanceNumByCorpType">
            <property name="corpType" value="'7'"/>
        </include>
        ) t18 on t.PJ0101 = t18.PJ0101
            left join (
        <include refid="countManagerNumByCorpTypeAndJobType">
            <property name="corpType" value="'7'"/>
            <property name="jobType" value="'1001'"/>
        </include>
        ) t19 on t.PJ0101 = t19.PJ0101
            left join (
        <include refid="countManagerAttendanceNumByCorpTypeAndJobType">
            <property name="corpType" value="'7'"/>
            <property name="jobType" value="1001"/>
        </include>
        ) t20 on t.PJ0101 = t20.PJ0101
        <!--其它参建单位今日考勤情况-->
        left join (
        <include refid="countWorkerNumByCorpType">
            <property name="corpType" value="'-1'"/>
        </include>
        ) t21 on t.PJ0101 = t21.PJ0101
            left join (
        <include refid="countWorkerAttendanceNumByCorpType">
            <property name="corpType" value="'-1'"/>
        </include>
        ) t22 on t.PJ0101 = t22.PJ0101
            left join (
        <include refid="countManagerNumByCorpType">
            <property name="corpType" value="'-1'"/>
        </include>
        ) t23 on t.PJ0101 = t23.PJ0101
            left join (
        <include refid="countManagerAttendanceNumByCorpType">
            <property name="corpType" value="'-1'"/>
        </include>
        ) t24 on t.PJ0101 = t24.PJ0101
        where t.PRJSTATUS = #{projectStatus}
        <if test="pj0101 != null and pj0101 != ''">
            and t.PJ0101 = #{pj0101}
        </if>
        <if test="areaCode != null and areaCode != ''">
            and t.AREACODE like '' || #{areaCode} || '%'
        </if>
        order by t.AREACODE
        ) p,
            R_PJ01_DEPT dept where dept.PJ0101 = p.PJ0101
                               and dept.DEPT_ID = #{deptId}
                               and p.PJ0101 in (<include
            refid="io.renren.common.common.dao.CommonDao.selectRealNameProject">
    </include>)
    </select>

    <!--根据参建类型查询在场工人数量-->
    <sql id="countWorkerNumByCorpType">
        select a1.PJ0101, count(a1.PS0201) workerCount
        from b_ps02 a1,
        B_TM01 a2,
        b_cp02 a3 where a2.CP0201 = a3.CP0201
        and a2.TM0101 = a1.TM0101
        and a1.IN_OR_OUT = '1'
        <if test="${corpType} != '' and ${corpType} != null">
            <choose>
                <when test="${corpType} == '-1'.toString()">
                    and a3.CORPTYPE not in ('7', '8', '9')
                </when>
                <otherwise>
                    and a3.CORPTYPE = ${corpType}
                </otherwise>
            </choose>
        </if>
        group by a1.PJ0101
    </sql>

    <!--根据参建类型查询管理人员在场数量-->
    <sql id="countManagerNumByCorpType">
        SELECT b1.PJ0101, count(0) managerCount
        FROM b_ps04 b1,
             b_cp02 b2
        WHERE b2.CP0201 = b1.CP0201
          and b1.IN_OR_OUT = '1'
        <if test="${corpType} != '' and ${corpType} != null">
            <choose>
                <when test="${corpType} == '-1'.toString()">
                    and b2.CORPTYPE not in ('7', '8', '9')
                </when>
                <otherwise>
                    and b2.CORPTYPE = ${corpType}
                </otherwise>
            </choose>
        </if>
        group by b1.PJ0101
    </sql>

    <!--根据参建类型查询今日在场工人考勤数量-->
    <sql id="countWorkerAttendanceNumByCorpType">
        select c4.pj0101, count(distinct (c4.USER_ID)) workerAttendanceCount
        from b_cp02 c1,
             b_ps02 c2,
             b_tm01 c3,
             b_kq02 c4
        where c3.cp0201 = c1.cp0201
          and c3.tm0101 = c2.tm0101
          and c2.PS0201 = c4.USER_ID
          and c2.in_or_out = '1'
          and c2.ps0201 = c4.user_id
          and to_char(c4.checkdate, 'yyyy-MM-dd') = #{dayDate}
        <if test="${corpType} != '' and ${corpType} != null">
            <choose>
                <when test="${corpType} == '-1'.toString()">
                    and c1.CORPTYPE not in ('7', '8', '9')
                </when>
                <otherwise>
                    and c1.CORPTYPE = ${corpType}
                </otherwise>
            </choose>
        </if>
        group by c4.pj0101
    </sql>

    <!--根据参建类型查询今日在场管理人员考勤数量-->
    <sql id="countManagerAttendanceNumByCorpType">
        select d3.PJ0101, count(distinct (d3.USER_ID)) managerAttendanceCount
        from b_cp02 d1,
             b_ps04 d2,
             b_kq02 d3
        where d1.CP0201 = d2.CP0201
          and d2.in_or_out = '1'
          and d2.PS0401 = d3.USER_ID
          and to_char(d3.checkdate, 'yyyy-MM-dd') = #{dayDate}
        <if test="${corpType} != '' and ${corpType} != null">
            <choose>
                <when test="${corpType} == '-1'.toString()">
                    and d1.CORPTYPE not in ('7', '8', '9')
                </when>
                <otherwise>
                    and d1.CORPTYPE = ${corpType}
                </otherwise>
            </choose>
        </if>
        group by d3.pj0101
    </sql>

    <!--根据参建类型,管理类型 查询此在场的管理人员人数-->
    <sql id="countManagerNumByCorpTypeAndJobType">
        SELECT e1.PJ0101, count(0) managerJobTypeCount
        FROM b_ps04 e1,
        b_cp02 e2 WHERE e2.CP0201 = e1.CP0201
        and e1.IN_OR_OUT = '1'
        <if test="${corpType} != '' and ${corpType} != null">
            <choose>
                <when test="corpType == '-1'.toString()">
                    and e2.CORPTYPE not in ('7', '8', '9')
                </when>
                <otherwise>
                    and e2.corptype = ${corpType}
                </otherwise>
            </choose>
        </if>
        <if test="${jobType} != '' and ${jobType} != null">
            and e1.jobtype = ${jobType}
        </if>
        group by e1.pj0101
    </sql>

    <!--根据参见类型,管理类型 查询此在场的类管理人员今日考勤数量-->
    <sql id="countManagerAttendanceNumByCorpTypeAndJobType">
        SELECT f3.PJ0101, count(distinct (f3.USER_ID)) managerJobTypeAttCount
        FROM b_ps04 f1,
        b_cp02 f2,
        B_KQ02 f3
        WHERE f2.CP0201 = f1.CP0201
        and f1.PS0401 = f3.USER_ID
        and f1.IN_OR_OUT = '1'
        and to_char(f3.CHECKDATE, 'yyyy-MM-dd') = #{dayDate}
        <if test="${corpType} != '' and ${corpType} != null">
            <choose>
                <when test="${corpType} == '-1'.toString()">
                    and f2.CORPTYPE not in ('7', '8', '9')
                </when>
                <otherwise>
                    and f2.corptype = ${corpType}
                </otherwise>
            </choose>
        </if>
        <if test="${jobType} != '' and ${jobType} != null">
            and f1.jobtype = ${jobType}
        </if>
        group by f3.pj0101
    </sql>

    <select id="selectLevelByAreacode" resultType="java.lang.String">
        SELECT t.tree_level
        FROM sys_region t
        WHERE t.value = #{areacode}
    </select>
</mapper>