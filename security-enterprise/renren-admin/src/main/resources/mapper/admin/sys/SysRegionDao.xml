<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.admin.sys.dao.SysRegionDao">
    <select id="getList" resultType="io.renren.modules.admin.sys.entity.SysRegionEntity">
        select *
        from sys_region
        <where>
            <if test="treeLevel != null">
                tree_level = #{treeLevel}
            </if>
            <if test="pid != null and pid.trim() != ''">
                and pid = #{pid}
            </if>
        </where>
        order by sort
    </select>

    <select id="getTreeList" resultType="map">
        select id, pid, name
        from sys_region
        order by sort
    </select>

    <select id="getListByLevel" resultType="io.renren.modules.admin.sys.entity.SysRegionEntity">
        select id, pid, name
        from sys_region
        where TREE_LEVEL = #{value}
        order by sort
    </select>

    <select id="getById" resultType="io.renren.modules.admin.sys.entity.SysRegionEntity">
        select t1.*, (select t2.name from sys_region t2 where t2.id = t1.pid) parentName
        from sys_region t1
        where t1.id = #{value}
    </select>

    <select id="getCountByPid" resultType="int">
        select count(*)
        from sys_region
        where pid = #{value}
    </select>
    <select id="getRegionData" resultType="io.renren.modules.admin.sys.dto.SysRegionDTO">
        select *
        from SYS_REGION t
        where t.whether = '1'
        order by t.ID asc
    </select>
    <select id="getRegionVersion" resultType="java.lang.String">
        select t.STATUS
        from SYS_TRIGGER t
        where t.NAME = 'SYS_REGION'
    </select>
    <select id="getAreaData" resultType="java.util.Map">
        SELECT name AS label,
               VALUE
        FROM SYS_REGION
        WHERE PID LIKE concat(#{areacode}, '%')
    </select>
    <select id="selectAreaByDept" resultType="io.renren.common.common.dto.CommonDto">
        select t.name as label, t.value
        from SYS_REGION t
        where t.value like #{areacode} || '%'
          and t.tree_level = '3'
    </select>
    <select id="selectUserAreaTree" resultType="io.renren.modules.admin.sys.dto.SysRegionDTO">
        select *
        from sys_region t
        start with t.value = #{areaCode}
        connect by prior t.id = t.pid
    </select>
    <select id="getTreeLevelByValue" resultType="java.lang.String">
        select t.TREE_LEVEL
        from SYS_REGION t
        where t.VALUE = #{areaCode}
    </select>
</mapper>