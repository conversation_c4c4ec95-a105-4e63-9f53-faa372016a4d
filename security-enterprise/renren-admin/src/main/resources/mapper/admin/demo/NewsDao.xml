<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.admin.demo.dao.NewsDao">
    <select id="getList" resultType="io.renren.modules.admin.demo.entity.NewsEntity">
        select *
        from SYS_COMMON_PROBLEM where 1 = 1
        <if test="title != null and title.trim() != ''">
            and title like #{title}
        </if>
        <if test="startDate != null and startDate.trim() != ''">
            and pub_date >= #{startDate}
        </if>
        <if test="endDate != null and endDate.trim() != ''">
            <![CDATA[
            and pub_date <= #{endDate}
            ]]>
        </if>
        order by SORT
    </select>
    <select id="selectTitleList" resultType="io.renren.modules.admin.demo.dto.NewsDTO">
        select a.ID, a.TITLE
        from SYS_COMMON_PROBLEM a
        order by a.SORT
    </select>
</mapper>