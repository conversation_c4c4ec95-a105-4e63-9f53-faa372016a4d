<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.admin.activiti.dao.ProcessBizRouteDao">
    <resultMap id="ProcessBizRouteEntity" type="io.renren.modules.admin.activiti.entity.ProcessBizRouteEntity">
        <result property="id" column="ID"/>
        <result property="procDefId" column="PROC_DEF_ID"/>
        <result property="bizRoute" column="BIZ_ROUTE"/>
        <result property="procDefKey" column="PROC_DEF_KEY"/>
        <result property="version" column="VERSION"/>
    </resultMap>

    <select id="getProcDefBizRoute" resultMap="ProcessBizRouteEntity">
        select * from tb_process_biz_route where PROC_DEF_ID = #{proDefId}
    </select>

    <select id="getLatestProcDefBizRoute" resultMap="ProcessBizRouteEntity">
        select * from tb_process_biz_route where PROC_DEF_KEY = #{procDefKey}
        order by version desc

    </select>

</mapper>