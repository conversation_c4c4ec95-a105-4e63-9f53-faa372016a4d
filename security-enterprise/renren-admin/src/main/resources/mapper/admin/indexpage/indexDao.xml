<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.admin.indexpage.dao.indexDao">
    <resultMap id="ProjectDTO" type="io.renren.modules.admin.indexpage.dto.ProjectDTO">
        <result property="name" column="name"/>
        <result property="owner" column="owner"/>
        <result property="generalUnit" column="generalunit"/>
        <result property="address" column="address"/>
        <result property="region" column="region"/>
        <result property="constructionPermit" column="constructionPermit"/>
        <result property="code" column="code"/>
        <result property="state" column="state"/>
    </resultMap>

    <resultMap id="PersonNumberDTO" type="io.renren.modules.admin.indexpage.dto.PersonNumberDTO">
        <result property="workerNum" column="workernum"/>
        <result property="bePresentNum" column="bepresentnum"/>
        <result property="tmNum" column="tmnum"/>
        <result property="managementNum" column="managementnum"/>
    </resultMap>

    <select id="getProjectInfo" resultType="io.renren.modules.admin.indexpage.dto.ProjectDTO" resultMap="ProjectDTO">
        SELECT p.name,
               (SELECT c1.corpname
                FROM b_cp01 c1,
                     b_cp02 c2
                where c1.cp0101 = c2.cp0101
                  and c2.pj0101 = p.pj0101
                  and c2.CORPTYPE = '8'
                  and c2.IN_OR_OUT = '1')                                   as owner,
               (SELECT c1.corpname
                FROM b_cp01 c1,
                     b_cp02 c2
                where c1.cp0101 = c2.cp0101
                  and c2.pj0101 = p.pj0101
                  and c2.CORPTYPE = '9'
                  and c2.IN_OR_OUT = '1')                                   as generalunit,
               (SELECT c1.corpname
                FROM b_cp01 c1,
                     b_cp02 c2
                where c1.cp0101 = c2.cp0101
                  and c2.pj0101 = p.pj0101
                  and c2.CORPTYPE = '7'
                  and c2.IN_OR_OUT = '1')                                   as supervisionUnit,
               p.address,
               (SELECT a.name FROM SYS_REGION a where a.value = p.areacode) as region,
               k.builderlicensenum                                          as constructionPermit,
               p.CODE,
               (select y.dict_label
                from SYS_DICT_DATA y,
                     sys_dict_type d
                where y.dict_type_id = d.id
                  and d.dict_type = 'PRJSTATUS'
                  and y.dict_value = p.PRJSTATUS)                           as state
        FROM r_pj01_dept t,
             b_pj01 p,
             b_pj02 k
        where t.pj0101 = p.pj0101
          and k.pj0101 = p.pj0101
          and t.DEPT_ID = #{deptId}
    </select>

    <select id="getPersonNumber" resultType="io.renren.modules.admin.indexpage.dto.PersonNumberDTO"
            resultMap="PersonNumberDTO">
        SELECT k.*, (k.workerNum + k.managementNum) as bepresentnum
        FROM (
                 select (SELECT count(*) FROM b_ps02 a where a.pj0101 = p.pj0101 and a.IN_OR_OUT = 1)
                            workernum,
                        (SELECT COUNT(*) FROM b_tm01 d where d.pj0101 = p.pj0101 and d.IN_OR_OUT = 1)
                            tmnum,
                        (SELECT COUNT(*) FROM b_ps04 e where e.pj0101 = p.pj0101 and e.IN_OR_OUT = 1)
                            managementnum
                 from r_pj01_dept t,
                      b_pj01 p
                 where t.pj0101 = p.pj0101
                   and t.DEPT_ID = #{deptId}) k
    </select>

    <select id="getAttendanceNumber" resultType="io.renren.modules.admin.indexpage.dto.AttendanceNumberDTO">
        SELECT (SELECT COUNT(distinct a.USER_ID)
                FROM b_kq02 a,
                     b_ps02 c
                where a.USER_ID = c.ps0201
                  and a.pj0101 = p.pj0101
                  and c.pj0101 = p.pj0101
                  and to_char(a.checkdate, 'yyyy-MM-dd') = to_char(sysdate, 'yyyy-MM-dd')) as workerAttendanceNum,
               (SELECT COUNT(distinct d.USER_ID)
                FROM b_kq02 d,
                     b_ps04 f
                where d.USER_ID = f.ps0401
                  and d.pj0101 = p.pj0101
                  and f.pj0101 = p.pj0101
                  and to_char(d.checkdate, 'yyyy-MM-dd') = to_char(sysdate, 'yyyy-MM-dd')) as managementAttendanceNum
        FROM r_pj01_dept t,
             b_pj01 p
        where p.pj0101 = t.pj0101
          and t.dept_id = #{deptId}
    </select>

    <select id="getTotalPayment" resultType="java.math.BigDecimal">
        SELECT (select nvl(sum(c.accountnum), 0)
                from b_pj15 c
                where c.pj0101 = p.pj0101
                  and c.flag = '1'
                  and c.issuedetail =
                      '1')
        FROM r_pj01_dept t,
             b_pj01 p
        where p.pj0101 = t.pj0101
          and t.dept_id = #{deptId}
    </select>

    <select id="getParticipatingUnits" resultType="io.renren.modules.admin.indexpage.dto.ParticipatingUnitsDTO">
        select p.name, p.type, nvl(c1.managerTotal, 0) + nvl(d1.workerTotal, 0) as personNum
        from (select b.corpname                        as name,
                     a.cp0201,
                     (select d.dict_label
                      from sys_dict_type c,
                           sys_dict_data d
                      where c.id = d.dict_type_id
                        and c.dict_type = 'CORPTYPE'
                        and d.dict_value = a.corptype) as type
              from b_cp02 a,
                   b_cp01 b
              where a.cp0101 = b.cp0101
                and a.in_or_out = '1'
                and a.pj0101 = #{pj0101}) p
                 left join (select c.cp0201, count(c.ps0401) as managerTotal
                            from b_ps04 c
                            where c.pj0101 = #{pj0101}
                              and c.in_or_out = '1'
                            group by c.cp0201) c1
                           on p.cp0201 = c1.cp0201
                 left join (select f.cp0201, count(0) as workerTotal
                            from b_ps02 e,
                                 b_tm01 f
                            where e.tm0101 = f.tm0101
                              and e.pj0101 = #{pj0101}
                              and e.in_or_out = '1'
                            group by f.cp0201) d1
                           on p.cp0201 = d1.cp0201
    </select>

    <select id="getManagerAttendance" resultType="io.renren.modules.admin.indexpage.dto.ManagerAttendanceDTO">
        select p.ps0401                         as userId,
               (select y.dict_label
                from SYS_DICT_DATA y,
                     sys_dict_type d
                where y.dict_type_id = d.id
                  and d.dict_type = 'CORPTYPE'
                  and y.dict_value = p1.corptype) || '-' ||
               (select y.dict_label
                from SYS_DICT_DATA y,
                     sys_dict_type d
                where y.dict_type_id = d.id
                  and d.dict_type = 'JOBTYPE'
                  and y.dict_value = p.jobtype) as managerType,
               p2.name                          as managerName
        from b_ps04 p
                 inner join b_cp02 p1
                            on p.cp0201 = p1.cp0201
                                and p1.pj0101 = #{pj0101}
                 inner join b_ps01 p2
                            on p.ps0101 = p2.ps0101
        where p.in_or_out = '1'
          and p.pj0101 = #{pj0101}
    </select>

    <select id="getFileInfoDTO" resultType="io.renren.modules.admin.indexpage.dto.FileInfoDTO">
        SELECT t3.archivesname as name, decode(q.total, 0, 0, null, 0, 1) as state
        FROM (
                 SELECT count(p2.pj0201) as total, p2.ot0301
                 FROM b_pj02 p2,
                      r_pj01_dept t,
                      b_pj01 p
                 where p2.pj0101 = p.pj0101
                   and p.pj0101 = t.pj0101
                   and to_char(p2.archivesdate, 'yyyymm') = to_char(ADD_MONTHS(SYSDATE, -1), 'yyyymm')
                   and t.DEPT_ID = #{deptId}
                 group by p2.ot0301
             ) q
                 right join B_OT03 t3 on t3.ot0301 = q.ot0301
        where t3.updatetype = 1
        union
        SELECT t3.archivesname as name, decode(q.total, 0, 0, null, 0, 1) as state
        FROM (
                 SELECT count(p2.pj0201) as total, p2.ot0301
                 FROM b_pj02 p2,
                      r_pj01_dept t,
                      b_pj01 p
                 where p2.pj0101 = p.pj0101
                   and p.pj0101 = t.pj0101
                   and t.DEPT_ID = #{deptId}
                 group by p2.ot0301
             ) q
                 right join B_OT03 t3 on t3.ot0301 = q.ot0301
        where t3.updatetype = 0
        union
        SELECT t3.archivesname as name, decode(q.total, 0, 0, null, 0, 1) as state
        FROM (
                 SELECT count(p2.pj0201) as total, p2.ot0301
                 FROM b_pj02 p2,
                      r_pj01_dept t,
                      b_pj01 p
                 where p2.pj0101 = p.pj0101
                   and p.pj0101 = t.pj0101
                   and to_char(p2.archivesdate, 'q') = to_char(sysdate, 'q')
                   and to_char(p2.archivesdate, 'yyyy') = to_char(sysdate, 'yyyy')
                   and t.DEPT_ID = #{deptId}
                 group by p2.ot0301
             ) q
                 right join B_OT03 t3 on t3.ot0301 = q.ot0301
        where t3.updatetype = 2
    </select>
    <select id="getMonthAttendance" resultType="io.renren.modules.admin.indexpage.dto.MonthAttendanceDTO">
        SELECT to_char(d.time, 'yyyy/MM/dd') as time, nvl(b.AttendanceNum, 0) as AttendanceNum
        FROM (
                 select trunc(sysdate - 15) + rownum as time from dual connect by 15 >= rownum) d
                 left join (select count(distinct k.USER_ID)          as AttendanceNum,
                                   to_char(k.CHECKDATE, 'yyyy-MM-dd') as time
                            from b_kq02 k
                            where to_char(k.CHECKDATE, 'yyyy-MM-dd') &gt;=
                                  to_char((sysdate - 14), 'yyyy-MM-dd')
                              and k.pj0101 = #{pj0101}
                              and k.PERSON_TYPE &lt;= 2
                            group by to_char(k.CHECKDATE, 'yyyy-MM-dd')) b
                           on to_char(d.time, 'yyyy-MM-dd') = b.time
        order by d.time
    </select>

    <select id="getEquipmentInfo" resultType="io.renren.modules.admin.indexpage.dto.EquipmentInfoDTO">
        SELECT k.deviceserialno,
               (SELECT DICT_LABEL
                FROM sys_dict_data d,
                     sys_dict_type v
                WHERE d.dict_type_id = v.id
                  and v.dict_type = 'NETWORK_STATUS'
                  and d.dict_value = k.NETWORK_STATUS) as networkStatus
        FROM b_kq01 k,
             r_pj01_dept t
        WHERE t.pj0101 = k.pj0101
          and k.WHETHER = '1'
          and t.DEPT_ID = #{deptId}
    </select>

    <select id="getManagerAttTotal" resultType="io.renren.modules.admin.indexpage.dto.ManagerAttendanceDTO">
        select m.user_id as userId, count(m.kq0201) as managerAttendance
        from b_kq02 m
        where to_char(m.checkdate, 'yyyy-MM-dd') = #{date}
          and m.pj0101 = #{pj0101}
          and m.PERSON_TYPE = '2'
        group by m.user_id
    </select>
</mapper>