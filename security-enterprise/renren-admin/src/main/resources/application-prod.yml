spring:
  servlet:
    multipart:
      location: /u01/pro/gkcloud/upload/tmp
  datasource:
    druid:
      #Oracle
      driver-class-name: oracle.jdbc.OracleDriver
      #      正式环境
      url: *******************************************
      username: gk_nmg_smz
      password: gk_nmg_smz
      initial-size: 10
      max-active: 20
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: XinYeGk@123
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  #RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: xygk
    password: xygk@123
    virtual-host: /
  redis:
    database: 0
    host: localhost
    port: 6379
    password: jzgxygk@123 # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
expFile:
  attendanceRecord: /u01/pro/gkcloud/template/AttendanceRecord.xlsx    #考勤表导出模板文件路径
  payrollRegister: /u01/pro/gkcloud/template/PayrollRegister.xlsx    #工资发放花名册导出模板文件路径
  workerRecord: /u01/pro/gkcloud/template/WorkerRecord.xlsx   #备案表导出模板文件路径
  projectLedger: /u01/pro/gkcloud/template/ProjectLedger.xlsx    #项目台账导出模板文件路径
  contractExport: /u01/pro/gkcloud/template/四川省建筑工人简易劳动合同书.docx
#knife4j配置信息
knife4j:
  # 开启增强配置
  enable: true
  # 开启生产环境屏蔽
  production: true
#住建厅上报库用户名
datasource:
  name: GK_NMG_SMZ_ZJSB
#摄像设备服务地址
camera:
  url: http://127.0.0.1:8010
#考勤设备校验地址
deviceCheck:
  url: http://gkwechat.xinyegk.com/api/eq01/register
rasKey:
  privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALkOYGmESBVIpHK7LALc2Wc0JfRq0qDMmbj17JtQ3BGA9yZvFlVbw+D6ZzlCXcxyD8hZTqe6IE9WRE47FGzmOJtlpYS81psgKDusATfIAfjMujSUONF+/nDm4HgvpXvC3i/OSPUTVdXbAoEOA/t99u4v4WjgtoOdAUw2xBlCxWldAgMBAAECgYA9uF/LkYUBJENEyMx6//G6jBgElwQ9AUBcAhmyC5v6cor71J0cRwjjpmB7JnlmULyW08J6CofwLhOGDApZ78b87A5zL5Tekv7Qq+wdXZ5WxFB5jfWKBLCNR7mVnliJF/58jNCcA+es+6uP/J4V+cROGqGQPinNYN10BVFkZkKPOQJBANtw5AE94rYTTTkV/gAcaji1UJSjJ2NCZqvpdvgXEYF1ZuGndbeCF3zp3rxzSQy5kSZrVzeCkWIFoyNEQtRO2NsCQQDX4vsgFaNnmyGHH94aTjXjOWiYmauU1Fq2MB61Y0ZXJtEVg/NBFKQR9kxxxbLLoRqegvEg2gzzA9k0CsSoJSAnAkAOZrk/75zKUDf4MB5MSZEbCaMxLgfutrPgQmEhZPosIdVPp9rM4UfDVeHdQj+gCymgeNjvdq0t3qrXLKqqHkaTAkBMrjSgis8Fh9Z61ljfYxRE5yk9uqd1L5Ag0iq2JtLfNcxiJgILBbfZIXC5oI0HQuvfjYb0Mj8TOjS2BXUgfD9VAkEA2NXO9XtdyCFFXcXziXluRxOfAci38vuMGH5fMnZoy+PmydV6JROoyLgolKsicAVeeQvImG1nmLRaP0mdxDk4SQ==