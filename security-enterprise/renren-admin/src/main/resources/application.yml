server:
  undertow:
    threads:
      # 指定工作者线程的 I/0 线程数，默认为 2 或者 CPU 的个数
      io: 8
      # 指定工作者线程个数,默认为 I/O线程个数的8倍
      worker: 256
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 内存大于 128 MB 时，bufferSize 为 16 KB 减去 20 字节，这 20 字节用于协议头
    buffer-size: 16364
    # 是否分配的直接内存(NIO直接分配的堆外内存)
    direct-buffers: true
  servlet:
    context-path: /
    session:
      cookie:
        http-only: true
  port: 8001

spring:
  # 环境 dev|test|prod
  profiles:
    active: dev
  messages:
    encoding: UTF-8
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 200MB
      enabled: true
  activiti:
    check-process-definitions: false

# 是否开启redis缓存  true开启   false关闭
renren.redis.open: true
# 附件储存方式：1、本地储存；2、附件服务器储存 ，默认附件服务器存储
upload:
  isOpenLocal: false
#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: io.renren.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: id_worker
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
