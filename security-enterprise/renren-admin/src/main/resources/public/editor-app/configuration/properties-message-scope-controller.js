/*
 * Activiti Modeler component part of the Activiti project
 * Copyright 2005-2014 Alfresco Software, Ltd. All rights reserved.
 * 
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 */

angular.module('activitiModeler').controller('ActivitiMessageRefCtrl', [ '$scope', function($scope) {

    // Find the parent shape on which the message definitions are defined
    var messageDefinitionsProperty = undefined;
    var parent = $scope.selectedShape;
    while (parent !== null && parent !== undefined && messageDefinitionsProperty === undefined) {
        if (parent.properties && parent.properties['oryx-messagedefinitions']) {
            messageDefinitionsProperty = parent.properties['oryx-messagedefinitions'];
        } else {
            parent = parent.parent;
        }
    }

    try {
        messageDefinitionsProperty = JSON.parse(messageDefinitionsProperty);
        if (typeof messageDefinitionsProperty == 'string') {
            messageDefinitionsProperty = JSON.parse(messageDefinitionsProperty);
        }
    } catch (err) {
        // Do nothing here, just to be sure we try-catch it
    }

    $scope.messageDefinitions = messageDefinitionsProperty;


    $scope.messageChanged = function() {
    	$scope.updatePropertyInModel($scope.property);
    };
}]);