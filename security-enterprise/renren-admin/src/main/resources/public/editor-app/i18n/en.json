{"HEADER.BRAND": "流程设计器", "HEADER.BRAND_TAGLINE": "Powered by JeeSite.com", "PAGE.HEADER": "<EMAIL>", "ACTION.OK": "确定", "ACTION.SAVE": "保存", "ACTION.SAVE-AND-CLOSE": "保存并关闭", "ACTION.SEND": "发送", "ACTION.CANCEL": "取消", "ACTION.SELECT": "选中", "ACTION.ADD": "添加", "ACTION.REMOVE": "移除", "ACTION.MOVE.UP": "上移", "ACTION.MOVE.DOWN": "下移", "MAIN_NAVIGATION_ORCHESTRATIONS": "Orchestrations", "MAIN_NAVIGATION_DISPATCH_RULES": "Dispatch Rules", "MAIN_NAVIGATION_ASSET_GROUPS": "Assert Groups", "MAIN_NAVIGATION_SOLUTIONS": "Solutions", "TOOLBAR.ACTION.CLOSE": "关闭设计模型", "TOOLBAR.ACTION.SAVE": "保存设计模型", "TOOLBAR.ACTION.VALIDATE": "验证设计模型", "TOOLBAR.ACTION.CUT": "剪切 ", "TOOLBAR.ACTION.COPY": "复制", "TOOLBAR.ACTION.PASTE": "粘贴", "TOOLBAR.ACTION.DELETE": "删除", "TOOLBAR.ACTION.UNDO": "撤销", "TOOLBAR.ACTION.REDO": "重做", "TOOLBAR.ACTION.ZOOMIN": "缩小", "TOOLBAR.ACTION.ZOOMOUT": "放大", "TOOLBAR.ACTION.ZOOMACTUAL": "缩放到实际大小", "TOOLBAR.ACTION.ZOOMFIT": "缩放到适应大小", "TOOLBAR.ACTION.MOVE": "移动", "TOOLBAR.ACTION.IMPORT": "导入", "TOOLBAR.ACTION.EXPORT": "导出", "TOOLBAR.ACTION.BENDPOINT.ADD": "给个线条添加一个点，以成为折线", "TOOLBAR.ACTION.BENDPOINT.REMOVE": "删除一个线条上的点，方便成为直线", "TOOLBAR.ACTION.ALIGNHORIZONTAL": "水平对齐", "TOOLBAR.ACTION.ALIGNVERTICAL": "垂直对齐", "TOOLBAR.ACTION.SAMESIZE": "相同大小", "TOOLBAR.ACTION.HELP": "帮助", "TOOLBAR.ACTION.FEEDBACK": "反馈", "KICKSTART.PROCESS_TOOLBAR.ACTION.SAVE": "Save the model", "KICKSTART.PROCESS_TOOLBAR.ACTION.VALIDATE": "Validate the model", "KICKSTART.PROCESS_TOOLBAR.ACTION.HELP": "Start the guided tour", "KICKSTART.PROCESS_TOOLBAR.ACTION.FEEDBACK": "Provide feedback", "FORM_TOOLBAR.ACTION.SAVE": "Save the model", "FORM_TOOLBAR.ACTION.VALIDATE": "Validate the model", "FORM_TOOLBAR.ACTION.HELP": "Start the guided tour", "FORM_TOOLBAR.ACTION.FEEDBACK": "Provide feedback", "APP_DEFINITION_TOOLBAR.ACTION.SAVE": "Save the app definition", "APP_DEFINITION_TOOLBAR.ACTION.VALIDATE": "Validate the app definition", "APP_DEFINITION_TOOLBAR.ACTION.HELP": "Start the guided tour", "APP_DEFINITION_TOOLBAR.ACTION.FEEDBACK": "Provide feedback", "BUTTON.ACTION.DELETE.TOOLTIP": "Delete the element from the model", "BUTTON.ACTION.MORPH.TOOLTIP": "Change the element type", "ELEMENT.AUTHOR": "作者", "ELEMENT.DATE_CREATED": "创建日期", "ELEMENT.SELECTED_EMPTY_TITLE": "(没有名字)", "PROPERTY.REMOVED": "移除", "PROPERTY.EMPTY": "未设置", "PROPERTY.PROPERTY.EDIT.TITLE": "编辑 \"{{title}}\"", "PROPERTY.FEEDBACK.TITLE": "请填写您的意见反馈", "PROPERTY.ASSIGNMENT.TITLE": "分配用户", "PROPERTY.ASSIGNMENT.TYPE": "Type", "PROPERTY.ASSIGNMENT.TYPE.IDENTITYSTORE": "Identity store", "PROPERTY.ASSIGNMENT.TYPE.STATIC": "Static values", "PROPERTY.ASSIGNMENT.ASSIGNEE": "用户编码", "PROPERTY.ASSIGNMENT.MATCHING": "Use &uparrow; and &downarrow; to select and press Enter to confirm or use the mouse", "PROPERTY.ASSIGNMENT.ASSIGNEE_PLACEHOLDER": "Enter an assignee", "PROPERTY.ASSIGNMENT.EMPTY": "未分配用户", "PROPERTY.ASSIGNMENT.ASSIGNEE_DISPLAY": "用户 {{assignee}}", "PROPERTY.ASSIGNMENT.CANDIDATE_USERS_DISPLAY": "{{length}} 个候选用户", "PROPERTY.ASSIGNMENT.CANDIDATE_USERS": "候选用户编码", "PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS_DISPLAY": "{{length}} 候选用户组", "PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS": "候选用户组编码", "PROPERTY.ASSIGNMENT.USER_IDM_DISPLAY": "用户 {{firstName}} {{lastName}}", "PROPERTY.ASSIGNMENT.USER_IDM_EMAIL_DISPLAY": "用 {{email}}", "PROPERTY.ASSIGNMENT.IDM_EMPTY": "Process initiator", "PROPERTY.ASSIGNMENT.IDM.TYPE": "Assignment", "PROPERTY.ASSIGNMENT.IDM.NO_CANDIDATE_USERS": "No candidate users selected...", "PROPERTY.ASSIGNMENT.IDM.NO_CANDIDATE_GROUPS": "No candidate groups selected...", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.INITIATOR": "Assigned to process initiator", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.USER": "Assigned to single user", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.USERS": "Candidate users", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.GROUPS": "Candidate groups", "PROPERTY.ASSIGNMENT.EMAIL.HELP": "Type an email address and press Enter to continue", "PROPERTY.EXECUTIONLISTENERS.DISPLAY": "{{length}} 个执行监听器", "PROPERTY.EXECUTIONLISTENERS.EMPTY": "未设置执行监听器", "PROPERTY.EXECUTIONLISTENERS.EVENT": "Event", "PROPERTY.EXECUTIONLISTENERS.CLASS": "Class", "PROPERTY.EXECUTIONLISTENERS.CLASS.PLACEHOLDER": "Enter a classname", "PROPERTY.EXECUTIONLISTENERS.EXPRESSION": "Expression", "PROPERTY.EXECUTIONLISTENERS.EXPRESSION.PLACEHOLDER": "Enter an expression", "PROPERTY.EXECUTIONLISTENERS.DELEGATEEXPRESSION": "Delegate expression", "PROPERTY.EXECUTIONLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "Enter a delegate expression", "PROPERTY.EXECUTIONLISTENERS.UNSELECTED": "No execution listener selected", "PROPERTY.EXECUTIONLISTENERS.FIELDS.NAME": "Name", "PROPERTY.EXECUTIONLISTENERS.FIELDS.NAME.PLACEHOLDER": "Enter a name", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EXPRESSION": "Expression", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EXPRESSION.PLACEHOLDER": "Enter an expression", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRINGVALUE": "String value", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRINGVALUE.PLACEHOLDER": "Enter a string value", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRING": "String", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRING.PLACEHOLDER": "Enter a string", "PROPERTY.EXECUTIONLISTENERS.FIELDS.IMPLEMENTATION": "Implementation", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EMPTY": "No Field selected", "PROPERTY.FIELDS": "{{length}} fields", "PROPERTY.FIELDS.EMPTY": "No fields selected", "PROPERTY.FIELDS.NAME": "Name", "PROPERTY.FIELDS.NAME.PLACEHOLDER": "Enter a name", "PROPERTY.FIELDS.EXPRESSION": "Expression", "PROPERTY.FIELDS.EXPRESSION.PLACEHOLDER": "Enter an expression", "PROPERTY.FIELDS.STRINGVALUE": "String value", "PROPERTY.FIELDS.STRINGVALUE.PLACEHOLDER": "Enter a string value", "PROPERTY.FIELDS.STRING": "String", "PROPERTY.FIELDS.STRING.PLACEHOLDER": "Enter a string", "PROPERTY.FIELDS.IMPLEMENTATION": "Implementation", "PROPERTY.FIELDS.UNSELECTED": "No Field selected", "PROPERTY.FORMPROPERTIES.VALUE": "{{length}} 个表单属性", "PROPERTY.FORMPROPERTIES.EMPTY": "未设置表单属性", "PROPERTY.FORMPROPERTIES.ID": "编码", "PROPERTY.FORMPROPERTIES.ID.PLACEHOLDER": "请填写编码", "PROPERTY.FORMPROPERTIES.NAME": "名称", "PROPERTY.FORMPROPERTIES.NAME.PLACEHOLDER": "请填写名称", "PROPERTY.FORMPROPERTIES.TYPE": "类型", "PROPERTY.FORMPROPERTIES.DATEPATTERN": "日期格式", "PROPERTY.FORMPROPERTIES.DATEPATTERN.PLACEHOLDER": "请填写日期格式", "PROPERTY.FORMPROPERTIES.VALUES": "所有值", "PROPERTY.FORMPROPERTIES.ENUMVALUES.EMPTY": "请选择...", "PROPERTY.FORMPROPERTIES.VALUES.ID": "编码", "PROPERTY.FORMPROPERTIES.VALUES.NAME": "名称", "PROPERTY.FORMPROPERTIES.VALUES.ID.PLACEHOLDER": "请填写编码", "PROPERTY.FORMPROPERTIES.VALUES.NAME.PLACEHOLDER": "请填写名称", "PROPERTY.FORMPROPERTIES.EXPRESSION": "表达式", "PROPERTY.FORMPROPERTIES.EXPRESSION.PLACEHOLDER": "请填写表达式", "PROPERTY.FORMPROPERTIES.VARIABLE": "变量名", "PROPERTY.FORMPROPERTIES.VARIABLE.PLACEHOLDER": "请填写变量名", "PROPERTY.FORMPROPERTIES.REQUIRED": "必填", "PROPERTY.FORMPROPERTIES.READABLE": "只读", "PROPERTY.FORMPROPERTIES.WRITABLE": "读写", "PROPERTY.INPARAMETERS.VALUE": "{{length}} in-parameters", "PROPERTY.INPARAMETERS.EMPTY": "No in-parameters configured", "PROPERTY.OUTPARAMETERS.VALUE": "{{length}} out-parameters", "PROPERTY.OUTPARAMETERS.EMPTY": "No out-parameters configured", "PROPERTY.PARAMETER.SOURCE": "Source", "PROPERTY.PARAMETER.SOURCE.PLACEHOLDER": "Enter a source", "PROPERTY.PARAMETER.SOURCEEXPRESSION": "Source expression", "PROPERTY.PARAMETER.SOURCEEXPRESSION.PLACEHOLDER": "Enter a source expression", "PROPERTY.PARAMETER.TARGET": "Target", "PROPERTY.PARAMETER.TARGET.PLACEHOLDER": "Enter a target", "PROPERTY.PARAMETER.EMPTY": "No parameter selected", "PROPERTY.SUBPROCESSREFERENCE.EMPTY": "No reference selected", "PROPERTY.SUBPROCESSREFERENCE.TITLE": "Collapsed subprocess reference", "PROPERTY.SUBPROCESSREFERENCE.ERROR.SUBPROCESS": "There was an error loading the subprocesses. Try again later", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.ROOT": "Folders", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.LOADING": "Loading folders...", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.EMPTY": "This folder contains no sub-folders", "PROPERTY.SUBPROCESSREFERENCE.SUBPROCESS.LOADING": "Loading subprocesses...", "PROPERTY.SUBPROCESSREFERENCE.SUBPROCESS.EMPTY": "This folder contains no subprocesses", "PROPERTY.FORMREFERENCE.EMPTY": "No reference selected", "PROPERTY.FORMREFERENCE.TITLE": "Form reference", "PROPERTY.FORMREFERENCE.ERROR.FORM": "There was an error loading the forms. Try again later", "PROPERTY.FORMREFERENCE.FOLDER.ROOT": "Folders", "PROPERTY.FORMREFERENCE.FOLDER.LOADING": "Loading folders...", "PROPERTY.FORMREFERENCE.FOLDER.EMPTY": "This folder contains no sub-folders", "PROPERTY.FORMREFERENCE.FORM.LOADING": "Loading forms...", "PROPERTY.FORMREFERENCE.FORM.EMPTY": "This folder contains no forms", "PROPERTY.TASKLISTENERS.VALUE": "{{length}} 个任务监听器", "PROPERTY.TASKLISTENERS.EMPTY": "未设置任务监听器", "PROPERTY.TASKLISTENERS.EVENT": "Event", "PROPERTY.TASKLISTENERS.CLASS": "Class", "PROPERTY.TASKLISTENERS.CLASS.PLACEHOLDER": "Enter a classname", "PROPERTY.TASKLISTENERS.EXPRESSION": "Expression", "PROPERTY.TASKLISTENERS.EXPRESSION.PLACEHOLDER": "Enter an expression", "PROPERTY.TASKLISTENERS.DELEGATEEXPRESSION": "Delegate expression", "PROPERTY.TASKLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "Enter a delegate expression", "PROPERTY.TASKLISTENERS.UNSELECTED": "No task listener selected", "PROPERTY.TASKLISTENERS.FIELDS.NAME": "Name", "PROPERTY.TASKLISTENERS.FIELDS.NAME.PLACEHOLDER": "Enter a name", "PROPERTY.TASKLISTENERS.FIELDS.EXPRESSION": "Expression", "PROPERTY.TASKLISTENERS.FIELDS.EXPRESSION.PLACEHOLDER": "Enter an expression", "PROPERTY.TASKLISTENERS.FIELDS.STRINGVALUE": "String value", "PROPERTY.TASKLISTENERS.FIELDS.STRINGVALUE.PLACEHOLDER": "Enter a string value", "PROPERTY.TASKLISTENERS.FIELDS.STRING": "String", "PROPERTY.TASKLISTENERS.FIELDS.STRING.PLACEHOLDER": "Enter a string", "PROPERTY.TASKLISTENERS.FIELDS.IMPLEMENTATION": "Implementation", "PROPERTY.TASKLISTENERS.FIELDS.EMPTY": "No Field selected", "PROPERTY.EVENTLISTENERS.DISPLAY": "{{length}} 个事件监听器", "PROPERTY.EVENTLISTENERS.EMPTY": "未设置事件监听器", "PROPERTY.EVENTLISTENERS.EVENTS": "Events", "PROPERTY.EVENTLISTENERS.RETHROW": "Rethrow event?", "PROPERTY.EVENTLISTENERS.CLASS": "Class", "PROPERTY.EVENTLISTENERS.CLASS.PLACEHOLDER": "Enter a classname", "PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION": "Delegate expression", "PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "Enter a delegate expression", "PROPERTY.EVENTLISTENERS.ENTITYTYPE": "Entity type", "PROPERTY.EVENTLISTENERS.ENTITYTYPE.PLACEHOLDER": "Enter an entity type", "PROPERTY.EVENTLISTENERS.RETHROWTYPE": "Rethrow event type", "PROPERTY.EVENTLISTENERS.ERRORCODE": "Error code", "PROPERTY.EVENTLISTENERS.ERRORCODE.PLACEHOLDER": "Enter an error code", "PROPERTY.EVENTLISTENERS.MESSAGENAME": "Message name", "PROPERTY.EVENTLISTENERS.MESSAGENAME.PLACEHOLDER": "Enter a message name", "PROPERTY.EVENTLISTENERS.SIGNALNAME": "Signal name", "PROPERTY.EVENTLISTENERS.SIGNALNAME.PLACEHOLDER": "Enter a signal name", "PROPERTY.EVENTLISTENERS.UNSELECTED": "No event listener selected", "PROPERTY.SIGNALDEFINITIONS.DISPLAY": "{{length}} 个信号定义", "PROPERTY.SIGNALDEFINITIONS.EMPTY": "未设置信号定义", "PROPERTY.SIGNALDEFINITIONS.SCOPE-GLOBAL": "Global", "PROPERTY.SIGNALDEFINITIONS.SCOPE-PROCESSINSTANCE": "Process Instance", "PROPERTY.SIGNALDEFINITIONS.ID": "Id", "PROPERTY.SIGNALDEFINITIONS.NAME": "Name", "PROPERTY.SIGNALDEFINITIONS.SCOPE": "<PERSON><PERSON>", "PROPERTY.MESSAGEDEFINITIONS.DISPLAY": "{{length}} 个消息定义", "PROPERTY.MESSAGEDEFINITIONS.EMPTY": "未设置消息定义", "PROPERTY.MESSAGEDEFINITIONS.ID": "Id", "PROPERTY.MESSAGEDEFINITIONS.NAME": "Name", "PROPERTY.SEQUENCEFLOW.ORDER.EMPTY": "No sequence flow order determined", "PROPERTY.SEQUENCEFLOW.ORDER.NOT.EMPTY": "Sequence flow order set", "PROPERTY.SEQUENCEFLOW.ORDER.NO.OUTGOING.SEQUENCEFLOW.FOUND": "No outgoing sequence flow found.", "PROPERTY.SEQUENCEFLOW.ORDER.DESCRIPTION": "Set the order in which the sequence flow need to be evaluated:", "PROPERTY.SEQUENCEFLOW.ORDER.SEQUENCEFLOW.VALUE": "Sequence flow to {{targetType}} {{targetTitle}}", "PROPERTY.SEQUENCEFLOW.CONDITION.TITLE": "Sequence flow condition", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.TITLE": "Condition type", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.VARIABLE": "Select variables", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.STATIC": "Static value", "PROPERTY.SEQUENCEFLOW.CONDITION.STATIC": "Condition expression", "PROPERTY.SEQUENCEFLOW.CONDITION.STATIC_PLACEHOLDER": "Fill-in expression value", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.TYPE": "Variable type", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-CONDITION": "No condition", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.FORM-FIELD": "Form field", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.FORM-OUTCOME": "Form outcome", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-FIELD": "Select field", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-FIELDS-AVAILABLE": "No fields available", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-FORM": "Select form", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-FORMS-AVAILABLE": "No forms available", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-OPERATOR": "Select operator", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.EQUALS": "Equals", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NOTEQUALS": "Not equals", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.LESSTHAN": "Less than", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.GREATERTHAN": "Greater than", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-OUTCOME": "Select outcome", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-OUTCOMES-AVAILABLE": "No outcomes available", "PROPERTY.SEQUENCEFLOW.CONDITION.NO-CONDITION-DISPLAY": "No condition", "MODEL.SAVE.TITLE": "Save model", "MODEL.NAME": "Name", "MODEL.DESCRIPTION": "Description", "MODEL.SAVE.NEWVERSION": "Save this as a new version?  This means you can always go back to a previous version", "MODEL.SAVE.COMMENT": "Comment", "MODEL.SAVE.SAVING": "Saving model", "MODEL.LASTMODIFIEDDATE": "Last saved", "MODEL.SAVE.ERROR": "Unexpected error: could not save model", "EVENT_TYPE.ACTIVITY.COMPENSATE.TOOLTIP": "An activity is about to be executed as a compensation for another activity. The event targets the activity that is about to be executed for compensation", "EVENT_TYPE.ACTIVITY.COMPLETED.TOOLTIP": "An activity has been completed successfully", "EVENT_TYPE.ACTIVITY.ERROR.RECEIVED.TOOLTIP": "An activity has received an error event. Dispatched before the actual error has been received by the activity", "EVENT_TYPE.MEMBERSHIP.CREATED.TOOLTIP": "A new membership has been created", "EVENT_TYPE.MEMBERSHIP.DELETED.TOOLTIP": "A single membership has been deleted", "EVENT_TYPE.MEMBERSHIPS.DELETED.TOOLTIP": "All memberships in the related group have been deleted. No individual events will be dispatched due to possible performance reasons", "EVENT_TYPE.TASK.ASSIGNED.TOOLTIP": "A task as been assigned. This is thrown alongside with an ENTITY_UPDATED event", "EVENT_TYPE.TASK.COMPLETED.TOOLTIP": "A task has been completed. Dispatched before the task entity is deleted", "EVENT_TYPE.UNCAUGHT.BPMNERROR.TOOLTIP": "When a BPMN Error was thrown, but was not caught within in the process", "EVENT_TYPE.VARIABLE.CREATED.TOOLTIP": "A new variable has been created", "EVENT_TYPE.VARIABLE.DELETED.TOOLTIP": "An existing variable has been deleted", "EVENT_TYPE.VARIABLE.UPDATED.TOOLTIP": "An existing variable has been updated"}