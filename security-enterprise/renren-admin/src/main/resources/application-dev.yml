spring:
  datasource:
    druid:
#      #Oracle
      driver-class-name: oracle.jdbc.OracleDriver
#      url: *******************************************
      url: **************************************
#      url: **********************************************
      username: gk_nmg_smz_dev
      password: gk_nmg_smz_dev
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 6000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  #RabbitMQ配置
  rabbitmq:
    host: **********
    port: 5672
    username: xygk
    password: xygk@123
    virtual-host: /
  redis:
    database: 0
    host: **********
    port: 6379
    password:    # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
expFile:
  attendanceRecord: E:/gkcloud/金键云导出模板/AttendanceRecord.xlsx    #考勤表导出模板文件路径
  payrollRegister: E:/gkcloud/金键云导出模板/PayrollRegister.xlsx    #工资发放花名册导出模板文件路径
  workerRecord: E:/gkcloud/金键云导出模板/WorkerRecord.xlsx   #备案表导出模板文件路径
  projectLedger: E:/ProjectLedger.xlsx    #项目台账导出模板文件路径
  contractExport: E:/gkcloud/金键云导出模板/四川省建筑工人简易劳动合同书.docx
#住建厅上报库用户名
datasource:
  name: ZJT0617
#摄像设备服务地址
camera:
  url: http://127.0.0.1:8010
#考勤设备校验地址
deviceCheck:
  url: http://gkwechat.xinyegk.com/api/eq01/register
rasKey:
  privateKey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAJWQNEwEcSfkwBU3d1jM5cJytvZqxgIcUKZmPg5bSvXKk/4ExKpjRJK6V5BQ3eYex4vOhA3Grttxr91aVUvAc5AFNJcNeHWuQJiL23fBFJD1m2G0+KgEV/0Ufm10kkY9oIwbWK8qH8mAdnzERpLkibMfuRMaJfWIwK8/ltWvWFZJAgMBAAECgYAFli1d6YyQgOrr3/aFSqIKcV6ENdjPm18n2AnZX2+dXsPIaUUvWCXnlS5QJbUkM2IgxsYH/bPqXoTDL+yft8CZQesbz4BiFqVKZ8JZ1EVnfd4K475o4q1iFTW1U2feYJp1Xgu94sFgWXTDgNGZBDSkATBP6s9sBomA1ib/SPsnQQJBANYXYXQ/bW6AurLuoYB2de0HF1pDHNLEN+JesAGjFggZuzrHFfr1XaERYvR+JSZ1sDYHQ+BbHL6dfZZoV2v2IfcCQQCy1ywfeh7wVlhMtyDaoeUmHkAg2QiR96onRR82/g+IQ0NwWSKLRN4w2N6qcGTFBBgODbhPnPScYgEsi5+6wzm/AkAIFPXe144f/8gMfSmnmdE103C0PZhW9IVPjFeEmE16nM4Ol/liceTI8ckUhbaNTyAJyhhr3cSmBXf6N5IlRzNdAkEAq3bllOBPHXqLejHxy+yAeQVz0oqDTv+3IaOCBAWd+rUSyXqMRh6nMQocoijUVJhSP9cjqSEA3ZaRLZZIdJWMnQJBAJIPB/4zsG4idn7SPRM4F7di65hFGYNqt+hQxV1+Pjns7pkS92FqsVXMFB3M9BhNZMh1ZhRLDsfdqGEFe39INlw=
#附件服务器地址
#fdfs:
#  so-timeout: 600000
#  connect-timeout: 6000
#  thumb-image:
#    height: 150
#    width: 150
#  tracker-list:
#    - **************:22122
#  web-server-url: http://**************:8089/
##多数据源的配置，需要引用renren-dynamic-datasource
#dynamic:
#  datasource:
#    slave1:
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      url: *************************************************************
#      username: sa
#      password: 123456
#    slave2:
#      driver-class-name: org.postgresql.Driver
#      url: *************************************************
#      username: postgres
#      password: 123456