package io.renren.modules.enterprise.pj01info.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.pa01.entity.Pa01Entity;
import io.renren.modules.enterprise.pj01info.dto.*;
import io.renren.modules.enterprise.pj01info.service.Pj01InfoService;
import io.renren.modules.enterprise.pj02.dto.Pj02DTO;
import io.renren.modules.enterprise.pj02.service.Pj02Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@RestController
@RequestMapping("enterprise/pj01Info")
@Api(tags = "项目查询模块")
public class Pj01InfoController {
    @Autowired
    private Pj01InfoService pj01InfoService;
    @Autowired
    private Pj02Service pj02Service;
    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:pj01Info:page")
    public Result<PageData<Pj01InfoDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Pj01InfoDTO> page = pj01InfoService.selectListData(params);

        return new Result<PageData<Pj01InfoDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:pj01Info:info")
    public Result<Pj01InfoDTO> get(@PathVariable("id") Long id) {
        Pj01InfoDTO data = pj01InfoService.getProjectInfo(id);
        return new Result<Pj01InfoDTO>().ok(data);
    }

    @GetMapping("/pa01/{pj0101}")
    @ApiOperation("获取专户信息")
    public Result<Pa01Entity> getPa01(@PathVariable("pj0101") Long pj0101){
        Pa01Entity pa01Entity = pj01InfoService.getSpecialAccount(pj0101);
        return new Result<Pa01Entity>().ok(pa01Entity);
    }

    @GetMapping("/contract/{pj0101}")
    @ApiOperation("获取项目合同信息")
    @RequiresPermissions("enterprise:pj01Info:info")
    public Result<List<Ot01DTO>> getContract(@PathVariable("pj0101") Long pj0101) {
        List<Ot01DTO> list = pj01InfoService.getBusinessData(pj0101, "01");
        return new Result<List<Ot01DTO>>().ok(list);
    }

    @GetMapping("/permit/{pj0101}")
    @ApiOperation("获取施工许可证信息")
    @RequiresPermissions("enterprise:pj01Info:info")
    public Result<Map<String, Object>> getPermit(@PathVariable("pj0101") Long pj0101) {
        Map<String, Object> map = new HashMap<>();
        //查询施工许可证信息
        Pj02DTO pj02DTO = pj02Service.getByPj0101(pj0101);
        map.put("pj02DTO", pj02DTO);
        //查询项目合同
        List<Ot01DTO> list = pj01InfoService.getBusinessData(pj0101, "03");
        map.put("permit", list);
        return new Result<Map<String, Object>>().ok(map);
    }

    @GetMapping("cp02Page")
    @ApiOperation("参建单位分页")
    public Result<PageData<Cp02PageDTO>> cp02Page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Cp02PageDTO> page = pj01InfoService.cp02PageList(params);
        return new Result<PageData<Cp02PageDTO>>().ok(page);
    }

    @GetMapping("teamPage")
    @ApiOperation("班组分页")
    public Result<PageData<Tm01PageDTO>> teamPage(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Tm01PageDTO> page = pj01InfoService.teamPageList(params);
        return new Result<PageData<Tm01PageDTO>>().ok(page);
    }

    @GetMapping("workerPage")
    @ApiOperation("工人分页")
    public Result<PageData<Ps02PageDTO>> workerPage(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Ps02PageDTO> page = pj01InfoService.workerPageList(params);
        return new Result<PageData<Ps02PageDTO>>().ok(page);
    }

    @GetMapping("managerPage")
    @ApiOperation("管理人员分页")
    public Result<PageData<Ps04PageDTO>> managerPage(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Ps04PageDTO> page = pj01InfoService.managerPageList(params);
        return new Result<PageData<Ps04PageDTO>>().ok(page);
    }

    @GetMapping("attendancePage")
    @ApiOperation("考勤分页")
    public Result<PageData<Kq02PageDTO>> attendancePage(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Kq02PageDTO> page = pj01InfoService.attendancePageList(params);
        return new Result<PageData<Kq02PageDTO>>().ok(page);
    }
}