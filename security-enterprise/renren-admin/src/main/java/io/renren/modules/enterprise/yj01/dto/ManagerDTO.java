package io.renren.modules.enterprise.yj01.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/31 15:04
 */
@Data
@ApiModel(value = "总包单位项目经理信息")
public class ManagerDTO implements Serializable {

    private static final long serialVersionUID = 3688589979560917757L;

    @ApiModelProperty(value = "人员数量")
    private Integer managerNum;

    @ApiModelProperty(value = "人员ID")
    private String managerId;

}
