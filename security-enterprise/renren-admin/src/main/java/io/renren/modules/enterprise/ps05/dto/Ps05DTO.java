package io.renren.modules.enterprise.ps05.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020-06-03 12:37
 */
@Data
@ApiModel(value = "人员注册")
public class Ps05DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0501;

    @ApiModelProperty(value = "平台返回的人员ID")
    private String guid;

    @ApiModelProperty(value = "人员照片ID")
    private String imageGuid;


    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "人员类型")
    private String personType;


}
