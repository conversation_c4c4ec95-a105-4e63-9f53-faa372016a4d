package io.renren.modules.enterprise.excelexp.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/14 9:38
 */
@Data
@ApiModel(value = "考勤表导出")
public class TimeSheetDTO implements Serializable {

    private static final long serialVersionUID = -3813767771693470172L;
    @Excel(name = "班组名称", mergeVertical = true)
    private String teamName;

    @Excel(name = "姓名", mergeVertical = true)
    private String name;

    @Excel(name = "身份证号码", mergeVertical = true)
    private String idCardNumber;

//    @Excel(name = "个人签字", mergeVertical = true)
//    private String signature;
//
//    @Excel(name = "备注", mergeVertical = true)
//    private String remark;
//
    private String attTotal;

    private String hoursTotal;

    @ExcelCollection(name = "")
    private List<AttendanceDetail> detailList;

}
