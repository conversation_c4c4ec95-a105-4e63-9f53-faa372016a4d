package io.renren.modules.enterprise.ps08.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 工人不良记录信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-01-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PS08")
public class Ps08Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long ps0801;
    /**
     * 人员ID
     */
    private Long ps0201;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 不良行为发生日期
     */
    private Date happenDate;
    /**
     * 不良行为描述
     */
    private String content;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}