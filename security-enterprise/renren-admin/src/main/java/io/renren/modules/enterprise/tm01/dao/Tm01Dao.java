package io.renren.modules.enterprise.tm01.dao;

import io.renren.common.common.dto.CommonDto;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.tm01.dto.Tm01PageDTO;
import io.renren.modules.enterprise.tm01.entity.Tm01Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Mapper
public interface Tm01Dao extends BaseDao<Tm01Entity> {
    /**
     * 查询分页列表
     *
     * @param params
     * @return
     */
    List<Tm01PageDTO> getList(Map<String, Object> params);

    /**
     * 通过班组名称和项目id查询班组是否存在
     *
     * @param teamName 班组名称
     * @param pj0101   项目ID
     * @return Integer
     */
    Integer queryByName(@Param("teamName") String teamName, @Param("pj0101") Long pj0101);

    /**
     * 查询当前登录用户下边的班组信息
     *
     * @param params Map<String, Object> 查询参数
     * @return List<CommonDto>
     */
    List<CommonDto> loadTm01Info(Map<String, Object> params);

    /**
     * 批量退场班组信息
     *
     * @param asList 班组id
     */
    void updateBatchExitTeam(@Param("asList") List<Long> asList);

    /**
     * 批量进场班组
     *
     * @param asList 班组id
     */
    void updateBatchEntryTeam(@Param("asList") List<Long> asList);

    /**
     * 查询在场班组信息
     *
     * @param deptId 当前登录用户的机构ID
     * @return List
     */
    List<CommonDto> selectPresenceTeamInfo(Long deptId);
}