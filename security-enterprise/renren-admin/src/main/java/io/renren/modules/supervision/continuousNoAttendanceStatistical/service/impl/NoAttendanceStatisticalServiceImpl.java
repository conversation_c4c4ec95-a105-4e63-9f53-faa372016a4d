package io.renren.modules.supervision.continuousNoAttendanceStatistical.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.DateUtils;
import io.renren.common.utils.ExcelUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.kq02.dao.Kq02Dao;
import io.renren.modules.enterprise.kq02.dto.Kq02AttendanceDTO;
import io.renren.modules.enterprise.kq02.dto.Kq02DTO;
import io.renren.modules.enterprise.kq02.entity.Kq02Entity;
import io.renren.modules.supervision.continuousNoAttendanceStatistical.dao.NoAttendanceStatisticalDao;
import io.renren.modules.supervision.continuousNoAttendanceStatistical.dto.NoAttendanceStatisticalDTO;
import io.renren.modules.supervision.continuousNoAttendanceStatistical.entity.NoAttendanceStatisticalEntity;
import io.renren.modules.supervision.continuousNoAttendanceStatistical.service.NoAttendanceStatisticalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chris
 * @Date : 2022-04-20
 **/
@Service
public class NoAttendanceStatisticalServiceImpl extends CrudServiceImpl<NoAttendanceStatisticalDao, NoAttendanceStatisticalEntity, NoAttendanceStatisticalDTO> implements NoAttendanceStatisticalService {

    @Autowired
    private NoAttendanceStatisticalDao attendanceStatisticalDao;

    @Override
    public PageData<NoAttendanceStatisticalDTO> getAbsenceDays(Map<String, Object> params) {
        IPage<NoAttendanceStatisticalEntity> page = getPage(params, "", false);
        params.put("deptId", SecurityUser.getDeptId());
        String areacode = (String) params.get("areacode");
        //处理行政区划
        params.put("areacode", CommonUtils.coverAreaCode(areacode));
        IPage<NoAttendanceStatisticalDTO> absenceDays = baseDao.getAbsenceDays(page, params);
        return getPageData(absenceDays, NoAttendanceStatisticalDTO.class);
    }

    @Override
    public void exportAbsenceDays(HttpServletResponse response, Map<String, Object> params) {
        String areacode = (String) params.get("areacode");
        //处理行政区划
        params.put("areacode", CommonUtils.coverAreaCode(areacode));
        params.put("deptId", SecurityUser.getDeptId());
        List<NoAttendanceStatisticalDTO> kq02AttendanceDTOS = baseDao.exportAbsenceDays(params);
        if (kq02AttendanceDTOS.size() == 0) {
            throw new RenException("无内容可以导出");
        }
        try {
            ExcelUtils.exportExcel(response, "连续无考勤项目统计表"+ DateUtils.format(new Date(),"yyyyMMdd"), kq02AttendanceDTOS, NoAttendanceStatisticalDTO.class);
        } catch (IOException e) {
            throw new RenException(e.getMessage());
        }
    }

    @Override
    public QueryWrapper<NoAttendanceStatisticalEntity> getWrapper(Map<String, Object> params) {
        return null;
    }
}
