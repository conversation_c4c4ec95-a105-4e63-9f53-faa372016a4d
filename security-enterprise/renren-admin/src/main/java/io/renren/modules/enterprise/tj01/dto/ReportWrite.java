package io.renren.modules.enterprise.tj01.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 住建厅数据写入情况
 *
 * <AUTHOR>
 * @since 1.0.0 2023-10-31
 */
@Data
@ApiModel(value = "住建厅数据写入查询")
public class ReportWrite implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "数量")
    private Integer sumCount;


}