package io.renren.modules.enterprise.pj01report.dao;


import io.renren.common.common.dto.CommonDto;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj01report.entity.BPj01ReportParamsEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 数据上报配置表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-14
 */
@Mapper
public interface BPj01ReportParamsDao extends BaseDao<BPj01ReportParamsEntity> {
    /**
     * 查询项目上报的配置信息
     * @param pj0101 项目ID
     * @return
     */
    List<BPj01ReportParamsEntity> selectParamList(Long pj0101);

    /**
     * 查询项目上报的配置信息
     * @return
     */
    List<CommonDto> selectReportParamList();
}