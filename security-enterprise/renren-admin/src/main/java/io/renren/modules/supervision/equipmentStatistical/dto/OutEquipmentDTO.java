package io.renren.modules.supervision.equipmentStatistical.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description 离线设备信息对象
 * @className: OutEquipmentDTO
 * @author: lrl
 * @date: 2021-12-28 13:38
 **/
@Data
public class OutEquipmentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目ID")
    private String pj0101;

    @ApiModelProperty(value = "联系人")
    private String linkMan;

    @ApiModelProperty(value = "联系电话")
    private String telephoneNumber;

    @ApiModelProperty(value = "设备序列号")
    private String equipmentNo;

    @ApiModelProperty(value = "设备状态")
    private String equipmentState;
}
