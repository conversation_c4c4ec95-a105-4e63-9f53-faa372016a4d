package io.renren.modules.enterprise.ps02.strategy.handler;

import cn.hutool.core.collection.CollUtil;
import io.renren.common.constant.enums.DeviceTypeEnums;
import io.renren.modules.enterprise.kq01.dto.Kq01DTO;
import io.renren.modules.enterprise.kq05.dto.PersonDTO;
import io.renren.modules.enterprise.kq05.entity.Kq05Entity;
import io.renren.modules.enterprise.ps02.strategy.AbstractPs02IssuePersonHandleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * wotu设备处理handler
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2025/6/17 9:44
 **/
@Slf4j
@Component
public class WoTuProcessHandler extends AbstractPs02IssuePersonHandleStrategy {

    /**
     * 人员类型
     */
    private final static String PERSON_TYPE = "1";

    /**
     * 人员下发，设备类型
     *
     * @return
     */
    @Override
    public DeviceTypeEnums businessType() {
        return DeviceTypeEnums.WO_TU_FACE;
    }

    /**
     * 销权
     *
     * @param pj0101     项目Id
     * @param userId     人员Id
     * @param personType 人员类型
     */
    @Override
    public void personSellRight(Long pj0101,
                                   Long userId,
                                   String personType,
                                   Kq01DTO kq01DTO) {
        List<Kq01DTO> kq01DTOList = getKq01DTOList(pj0101);
        if (CollUtil.isEmpty(kq01DTOList)) {
            log.info("WoTuProcessHandler personSellRight is empty");
            return;
        }
        kq01DTOList.forEach(item -> {
            Kq05Entity kq05Entity = new Kq05Entity();
            kq05Entity.setUserId(userId);
            kq05Entity.setDeviceserialno(item.getDeviceserialno());
            kq05Entity.setCmdCode("REMOVE_PERSON");
            kq05Entity.setTerminaltype(item.getTerminaltype());
            kq05Entity.setPersonType(personType);
            kq05Dao.insert(kq05Entity);
        });
    }

    /**
     * 下发人员
     *
     * @param pj0101     项目id
     * @param userId     人员Id
     * @param name       姓名
     * @param imageUrl   人员图片
     * @param personType 人员类型
     */
    @Override
    public void issuedWorkerPersonnel(Long pj0101, Long userId, String name, String imageUrl, String personType, String deviceType) {
        //1、人员注册
        Kq05Entity kq05Entity = new Kq05Entity();
        kq05Entity.setUserId(userId);
        kq05Entity.setPersonType(personType);
        kq05Entity.setTerminaltype(deviceType);
        kq05Entity.setCmdCode("REG_PERSON");
        kq05Entity.setParams(name);
        kq05Dao.insert(kq05Entity);
        //2、人员添加照片
        Kq05Entity kq05PersonDTO = new Kq05Entity();
        kq05PersonDTO.setUserId(userId);
        kq05PersonDTO.setPersonType(personType);
        kq05PersonDTO.setTerminaltype(deviceType);
        kq05PersonDTO.setCmdCode("ADD_PERSON_IMAGE");
        //判断是否本地储存
        Boolean isOpenLocal = fileProper.getIsOpenLocal();
        kq05PersonDTO.setParams(isOpenLocal ? fileProper.getPath() + imageUrl : imageUrl);
        kq05Dao.insert(kq05PersonDTO);
    }

    /**
     * 下发人员数据
     */
    @Override
    public void issuedDeviceAddPerson(Long userId, Long pj0101, String personType, String terminaltype, String cmdCode, String deviceserialno) {
        Kq05Entity kq05DeviceDTO = new Kq05Entity();
        kq05DeviceDTO.setUserId(userId);
        kq05DeviceDTO.setPersonType(personType);
        kq05DeviceDTO.setTerminaltype(terminaltype);
        kq05DeviceDTO.setDeviceserialno(deviceserialno);
        kq05DeviceDTO.setCmdCode("DEVICE_ADD_PERSON");
        kq05Dao.insert(kq05DeviceDTO);
    }

    /**
     * 更新人员图片
     *
     * @param pj0101     项目id
     * @param userId     人员Id
     * @param personType 人员类型
     */
    public void updatePersonImage(Long pj0101, Long userId, String imageUrl, String personType, String deviceType) {
        Kq05Entity kq05Entity = new Kq05Entity();
        kq05Entity.setUserId(userId);
        //判断是否本地储存
        Boolean isOpenLocal = fileProper.getIsOpenLocal();
        kq05Entity.setParams(isOpenLocal ? fileProper.getPath() + imageUrl : imageUrl);
        kq05Entity.setCmdCode("UPDATE_PERSON_IMAGE");
        kq05Entity.setTerminaltype(deviceType);
        kq05Entity.setPersonType(personType);
        kq05Dao.insert(kq05Entity);
    }

    /**
     * 下发人员（重写）
     *
     * @param pj0101         项目id
     * @param userId         人员Id
     * @param personType     人员类型
     * @param terminaltype   设备类型
     * @param deviceserialno 设备序列号
     */
    public void personSellRight(Long pj0101,
                                Long userId,
                                String personType,
                                String terminaltype,
                                String deviceserialno) {
        Kq05Entity kq05Entity = new Kq05Entity();
        kq05Entity.setUserId(userId);
        kq05Entity.setDeviceserialno(deviceserialno);
        kq05Entity.setCmdCode("REMOVE_PERSON");
        kq05Entity.setTerminaltype(terminaltype);
        kq05Entity.setPersonType(personType);
        kq05Dao.insert(kq05Entity);
    }

    /**
     * 下发人员
     */
    @Override
    public void issuedWorkerPersonnel(List<Long> userIds, String personType) {
        List<PersonDTO> person = ps02Dao.selectPersonByIds(userIds);
        for (PersonDTO personDTO : person) {
            //人员授权到设备
            kq05Service.saveCreatePerson(personDTO.getPj0101(), personDTO.getUserId(), personDTO.getName(), personDTO.getImageUrl(), personType);
        }
    }

    /**
     * 下发人员（管理人员）
     *
     * @param userIds
     * @param personType
     */
    @Override
    public void issuedManagerPersonnel(List<Long> userIds, String personType) {
        List<PersonDTO> personList = getManagerList(userIds);
        personList.forEach(personDTO -> {
            //人员授权到设备
            kq05Service.saveCreatePerson(personDTO.getPj0101(), personDTO.getUserId(), personDTO.getName(), personDTO.getImageUrl(), personType);
        });
    }
}
