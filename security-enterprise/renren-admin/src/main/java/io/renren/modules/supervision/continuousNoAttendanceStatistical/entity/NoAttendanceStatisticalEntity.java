package io.renren.modules.supervision.continuousNoAttendanceStatistical.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chris
 * @Date : 2022-04-20
 **/
@Data
@EqualsAndHashCode(callSuper=false)
public class NoAttendanceStatisticalEntity {
    /**
     * 行政区划
     */
    @Excel(name = "")
    private String areacode;

    /**
     * pj0101
     */
    private String pj0101;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    private String name;

    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名")
    private String linkman;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话")
    private String linkphone;

    /**
     * 缺勤天数
     */
    @Excel(name = "缺勤天数")
    private String absencedays;
}
