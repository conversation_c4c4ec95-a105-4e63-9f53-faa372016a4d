package io.renren.modules.enterprise.ps09.dto;

import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "项目关键岗位分页信息")
public class Ps09Page implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0901;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "企业ID")
    private Long cp0201;

    @ApiModelProperty(value = "人员ID")
    private Long ps0101;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目所在地")
    private String areaCode;

    @ApiModelProperty(value = "姓名")
    private String personName;

    @ApiModelProperty(value = "身份证号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idCardNumber;

    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    @ApiModelProperty(value = "手机号码")
    private String cellPhone;

    @ApiModelProperty(value = "岗位类型")
    private String jobType;

    @ApiModelProperty(value = "注册时间")
    private Date createDate;

    @ApiModelProperty(value = "审核时间")
    private Date auditDate;

    @ApiModelProperty(value = "审核状态")
    private String state;

    @ApiModelProperty(value = "不通过原因")
    private String notPassReasons;

}