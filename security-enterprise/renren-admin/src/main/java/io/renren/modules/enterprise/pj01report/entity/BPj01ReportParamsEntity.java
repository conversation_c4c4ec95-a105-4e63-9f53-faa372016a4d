package io.renren.modules.enterprise.pj01report.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据上报配置表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PJ01_REPORT_PARAMS")
public class BPj01ReportParamsEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 上报地方
     */
    private String reportAreaCode;
    /**
     * 队列名称
     */
    private String queueName;
    /**
     * 路由KEY
     */
    private String routingKey;
}