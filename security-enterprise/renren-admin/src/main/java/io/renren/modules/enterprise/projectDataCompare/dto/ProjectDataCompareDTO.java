package io.renren.modules.enterprise.projectDataCompare.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021-12-08 13:20
 */
@Data
@ApiModel(value = "项目对比情况")
public class ProjectDataCompareDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目ID")
    private String pj0101;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "行政区划")
    private String areaCode;

    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    @ApiModelProperty(value = "联系电话")
    private String contactNumber;

    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    @ApiModelProperty(value = "最新考勤时间")
    private Date latestAttendance;

    @ApiModelProperty(value = "参建单位")
    private String partUnitNum;

    @ApiModelProperty(value = "上报库参建单位")
    private String reportPartUnitNum;

    @ApiModelProperty(value = "班组")
    private String teamNum;

    @ApiModelProperty(value = "上报库班组")
    private String reportTeamNum;

    @ApiModelProperty(value = "工人")
    private String workerNum;

    @ApiModelProperty(value = "上报库工人")
    private String reportWorkerNum;

    @ApiModelProperty(value = "管理人员")
    private String managerNum;

    @ApiModelProperty(value = "上报库管理人员")
    private String reportManagerNum;

    @ApiModelProperty(value = "考勤人数")
    private String attendanceNum;

    @ApiModelProperty(value = "上报库考勤人数")
    private String reportAttendanceNum;
}
