package io.renren.modules.enterprise.ps09.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.ps09.dto.Ps09Page;
import io.renren.modules.enterprise.ps09.entity.Ps09Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/18 15:49
 */
@Mapper
public interface Ps09Dao extends BaseDao<Ps09Entity> {
    /**
     * 查询分页数据
     *
     * @param params Map<String, Object>
     * @return List<Ps09Page>
     */
    List<Ps09Page> selectPageList(Map<String, Object> params);

    /**
     * 查询人员是否存在
     *
     * @param pj0101  项目ID
     * @param cp0201  人员ID
     * @param jobType 岗位类型
     * @return int
     */
    int selectPersonIsExist(@Param("pj0101") Long pj0101, @Param("cp0201") Long cp0201, @Param("jobType") String jobType);
}
