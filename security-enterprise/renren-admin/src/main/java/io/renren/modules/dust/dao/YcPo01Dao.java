package io.renren.modules.dust.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.dust.dto.YcPo01DTO;
import io.renren.modules.dust.entity.YcPo01Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 用油管理
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface YcPo01Dao extends BaseDao<YcPo01Entity> {
    /**
     * @description 分页查询
     * <AUTHOR>
     * @date 2022年08月18日 8:55
     */
    List<YcPo01DTO> getListData(Map<String, Object> params);
}
