package io.renren.modules.supervision.map.service;

import io.renren.modules.supervision.map.dto.ProjectMapDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/20 9:01
 */
public interface ProjectMapService {
    /**
     * 查询项目信息
     *
     * @param params Map<String, Object>
     * @return List<ProjectMapDTO>
     */
    List<ProjectMapDTO> getProjectInfoList(Map<String, Object> params);
}
