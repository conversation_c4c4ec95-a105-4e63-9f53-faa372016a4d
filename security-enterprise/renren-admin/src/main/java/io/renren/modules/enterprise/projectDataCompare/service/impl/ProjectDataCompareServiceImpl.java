package io.renren.modules.enterprise.projectDataCompare.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.BaseServiceImpl;
import io.renren.modules.enterprise.projectDataCompare.dao.ProjectDataCompareDao;
import io.renren.modules.enterprise.projectDataCompare.dto.*;
import io.renren.modules.enterprise.projectDataCompare.service.ProjectDataCompareService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-12-08 13:32
 */
@Service
public class ProjectDataCompareServiceImpl extends BaseServiceImpl<ProjectDataCompareDao, ProjectDataCompareDTO> implements ProjectDataCompareService {
    /**
     * 行政区划后2位
     */
    private final static String END_STR = "00";

    @Value("${datasource.name: GK_NMG_SMZ_ZJSB}")
    private String name;

    @Override
    public PageData<ProjectDataCompareDTO> page(Map<String, Object> params) {
        String areaCode = (String) params.get("areaCode");
        //当行政区划最后2位是00的时候，截取前4位作为行政区划进行查询
        if (StringUtils.isNotBlank(areaCode)) {
            String subStr = StrUtil.sub(areaCode, areaCode.length() - 2, areaCode.length());
            if (END_STR.equals(subStr)) {
                areaCode = StrUtil.sub(areaCode, 0, 4);
            }
        }
        params.put("areaCode", areaCode);
        params.put("datasourceName", name);
        IPage<ProjectDataCompareDTO> page = getPage(params, "", false);
        List<ProjectDataCompareDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), ProjectDataCompareDTO.class);
    }

    @Override
    public List<PartUnitDTO> partUnitList(String pj0101) {

        return baseDao.getPartUnitList(pj0101,name);
    }

    @Override
    public List<TeamInfoDTO> teamInfoList(String pj0101) {
        return baseDao.getTeamInfoList(pj0101,name);
    }

    @Override
    public List<WorkerInfoDTO> workerInfoList(String pj0101) {
        return baseDao.getWorkerInfoList(pj0101,name);
    }

    @Override
    public List<ManagerInfoDTO> managerInfoList(String pj0101) {
        return baseDao.getManagerInfoList(pj0101,name);
    }
}
