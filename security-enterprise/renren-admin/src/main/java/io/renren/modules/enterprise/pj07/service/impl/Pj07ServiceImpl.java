package io.renren.modules.enterprise.pj07.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.pj07.dao.Pj07Dao;
import io.renren.modules.enterprise.pj07.dto.Pj07DTO;
import io.renren.modules.enterprise.pj07.dto.Pj07Page;
import io.renren.modules.enterprise.pj07.entity.Pj07Entity;
import io.renren.modules.enterprise.pj07.service.Pj07Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/7 9:41
 */
@Service
public class Pj07ServiceImpl extends CrudServiceImpl<Pj07Dao, Pj07Entity, Pj07DTO> implements Pj07Service {
    @Override
    public QueryWrapper<Pj07Entity> getWrapper(Map<String, Object> params) {

        String id = (String) params.get("id");

        QueryWrapper<Pj07Entity> wrapper = new QueryWrapper<>();

        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public PageData<Pj07Page> pageList(Map<String, Object> params) {

        params.put("deptId", SecurityUser.getDeptId());

        String areaCode = (String) params.get("areaCode");

        params.put("areaCode", CommonUtils.coverAreaCode(areaCode));

        IPage<Pj07Entity> page = getPage(params, "", false);

        List<Pj07Page> list = baseDao.getPageList(params);

        return getPageData(list, page.getTotal(), Pj07Page.class);
    }

    @Override
    public void saveOrUpdate(Pj07DTO dto) {
        QueryWrapper<Pj07Entity> wrapper = new QueryWrapper<>();
        wrapper.eq("pj0101", dto.getPj0101());
        Integer selectCount = baseDao.selectCount(wrapper);
        Pj07Entity pj07Entity = ConvertUtils.sourceToTarget(dto, Pj07Entity.class);
        if (selectCount > 0) {
            baseDao.updateById(pj07Entity);
            return;
        }
        baseDao.insert(pj07Entity);
    }
}
