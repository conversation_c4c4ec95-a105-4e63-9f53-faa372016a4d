package io.renren.modules.supervision.attendanceStatistical.service;


import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.supervision.attendanceStatistical.dto.AreaAttStatisticalDTO;
import io.renren.modules.supervision.attendanceStatistical.dto.AttStatisticalDTO;
import io.renren.modules.supervision.attendanceStatistical.entity.AttStatisticalEntity;

import java.util.List;
import java.util.Map;

/**
 * 考勤统计
 *
 * <AUTHOR>
 * @since 1.0.0 2021-04-26
 */
public interface AttStatisticalService extends CrudService<AttStatisticalEntity, AttStatisticalDTO> {

    /**
     * 考勤统计分页数据
     * @param params 查询参数
     * @return 考勤统计分页数据
     */
    PageData<AttStatisticalDTO>  getPageData(Map<String, Object> params);
    /**
     * 市州考勤统计分页数据
     * @param params 查询参数
     * @return 市州考勤统计分页数据
     */
    List<AreaAttStatisticalDTO> getAreaCodePageData(Map<String, Object> params);
}