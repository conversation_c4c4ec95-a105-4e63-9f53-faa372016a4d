package io.renren.modules.enterprise.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/6 8:51
 */
@Data
@ApiModel(value = "消息推送列表")
public class MessagePage implements Serializable {
    private static final long serialVersionUID = -8589022915605965468L;

    @ApiModelProperty(value = "项目ID")
    private Long cp0101;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "所属区域")
    private String areaCode;

    @ApiModelProperty(value = "联系人")
    private String linkMan;

    @ApiModelProperty(value = "联系电话")
    private String linkPhone;

    @ApiModelProperty(value = "项目状态")
    private String projectStatus;
}
