package io.renren.modules.supervision.personneldataStatistical.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:人员数据统计分析
 * @author: jinghai
 * @date: 2022/3/8 13:31
 **/
@Data
public class PersonnelDataStatisticalDTO implements Serializable {


    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "工种代码")
    private String workTypeCode;

    @ApiModelProperty(value = "工种数量")
    private Long workTypeCount;

    @ApiModelProperty(value = "工种占比")
    private Double workerTypePercentage;

    @ApiModelProperty(value = "籍贯代码")
    private String nativeCode;

    @ApiModelProperty(value = "籍贯数量")
    private Long nativeCount;

    @ApiModelProperty(value = "籍贯占比")
    private Double nativePercentage;

    @ApiModelProperty(value = "性别代码")
    private String genderCode;

    @ApiModelProperty(value = "性别数量")
    private Long genderCount;

    @ApiModelProperty(value = "性别占比")
    private Double genderPercentage;

    @ApiModelProperty(value = "年龄段")
    private String ageGroup;

    @ApiModelProperty(value = "年龄数量")
    private Long ageCount;

    @ApiModelProperty(value = "年龄占比")
    private Double agePercentage;





}
