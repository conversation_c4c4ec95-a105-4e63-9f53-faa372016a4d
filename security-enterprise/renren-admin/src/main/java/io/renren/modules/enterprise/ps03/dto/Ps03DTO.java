package io.renren.modules.enterprise.ps03.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 建筑工人合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "建筑工人合同信息")
public class Ps03DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0301;

    @ApiModelProperty(value = "建筑工人ID")
    private Long ps0201;

    @ApiModelProperty(value = "合同编号", required = true)
    private String contractcode;

    @ApiModelProperty(value = "合同期限类型", required = true)
    @NotBlank(message = "合同期限类型不能为空")
    private String contractperiodtype;

    @ApiModelProperty(value = "签订日期", required = true)
    @NotNull(message = "签订日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signdate;

    @ApiModelProperty(value = "开始日期", required = true)
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startdate;

    @ApiModelProperty(value = "结束时期", required = true)
//    @NotNull(message = "结束时期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date enddate;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "计量单价")
    private BigDecimal unitprice;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "合同附件")
    @Valid
    private List<Ot01DTO> ot01DTOList;

}