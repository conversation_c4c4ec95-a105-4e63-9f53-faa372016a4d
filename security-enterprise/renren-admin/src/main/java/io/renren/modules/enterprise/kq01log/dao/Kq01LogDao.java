package io.renren.modules.enterprise.kq01log.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.kq01log.dto.Kq01LogPageDTO;
import io.renren.modules.enterprise.kq01log.entity.Kq01LogEntity;
import io.renren.modules.enterprise.kq04.dto.Kq04DTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 考勤设备信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface Kq01LogDao extends BaseDao<Kq01LogEntity> {
    /**
     * 查询列表数据
     * @param params
     * @return
     */
    List<Kq01LogPageDTO> getListData(Map<String, Object> params);

    List<Kq04DTO> getListInfo(Map<String, Object> params);
}