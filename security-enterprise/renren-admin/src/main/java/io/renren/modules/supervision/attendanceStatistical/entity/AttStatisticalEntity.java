package io.renren.modules.supervision.attendanceStatistical.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description:
 * @className: EquipmentStatisticalEntity
 * @author: lrl
 * @date: 2021-12-28 13:48
 **/
@Data
@EqualsAndHashCode(callSuper=false)
public class AttStatisticalEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 项目名
     */
    private String projectName;
    /**
     * 在场工人数
     */
    private String workerNum;
    /**
     * 出勤工人数
     */
    private String attWorkerNum;
    /**
     * 工人出勤率
     */
    private String workerRate;
    /**
     * 在场管理员数
     */
    private String managerNum;
    /**
     * 出勤管理员数
     */
    private String attManagerNum;
    /**
     * 管理员出勤率
     */
    private String managerRate;
    /**
     * 总出勤率
     */
    private String totalRate;

}
