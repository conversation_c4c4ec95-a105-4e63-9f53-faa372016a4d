package io.renren.modules.enterprise.pj06.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.common.annotation.iscreditcodevalidator.IsCreditCodeValidator;
import io.renren.common.annotation.ismobilevalidator.IsMobileValidator;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Data
@ApiModel(value = "项目注册信息")
public class Pj06DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pj0601;

    @ApiModelProperty(value = "项目名称", required = true)
    @NotBlank(message = "项目名称不能为空", groups = DefaultGroup.class)
    @Length(max = 100, message = "项目名称不能超过100个汉字", groups = DefaultGroup.class)
    @CacheParam(name = "name")
    private String name;

    @ApiModelProperty(value = "所属行业_Select选择器", required = true)
    @NotBlank(message = "所属行业不能为空", groups = DefaultGroup.class)
    private String industry;

    @ApiModelProperty(value = "项目所在地_Cascader级联选择器", required = true)
    @NotBlank(message = "项目所在地不能为空", groups = DefaultGroup.class)
    @Length(max = 12, message = "项目所在地不能超过12位", groups = DefaultGroup.class)
    private String areacode;

    @ApiModelProperty(value = "联系人姓名", required = true)
    @NotBlank(message = "联系人姓名不能为空", groups = DefaultGroup.class)
    @Length(max = 25, message = "联系人姓名过长，不能超过25个汉字", groups = DefaultGroup.class)
    private String linkman;

    @ApiModelProperty(value = "联系人电话", required = true)
    @NotBlank(message = "联系人电话不能为空", groups = DefaultGroup.class)
    @IsMobileValidator(message = "请输入正确的手机号码", groups = DefaultGroup.class)
    private String linkphone;

    @ApiModelProperty(value = "建设单位名称", required = true)
    @NotBlank(message = "建设单位名称不能为空", groups = DefaultGroup.class)
    @Length(max = 40, message = "建设单位名称不能超过40个汉字", groups = DefaultGroup.class)
    private String constructionname;

    @ApiModelProperty(value = "建设单位统一社会信用代码", required = true)
    @NotBlank(message = "建设单位统一社会信用代码不能为空", groups = DefaultGroup.class)
    @IsCreditCodeValidator(message = "建设单位统一社会信用代码格式不正确", groups = DefaultGroup.class)
    private String constructionnumber;

    @ApiModelProperty(value = "施工总承包单位名称", required = true)
    @NotBlank(message = "施工总承包单位名称不能为空", groups = DefaultGroup.class)
    @Length(max = 40, message = "建设单位名称不能超过40个汉字", groups = DefaultGroup.class)
    private String contractname;

    @ApiModelProperty(value = "施工总承包单位统一社会信用代码", required = true)
    @NotBlank(message = "施工总承包单位统一社会信用代码不能为空", groups = DefaultGroup.class)
    @IsCreditCodeValidator(message = "施工总承包单位统一社会信用代码格式不正确", groups = DefaultGroup.class)
    private String contractnumber;

    @ApiModelProperty(value = "施工许可证",required = true)
    @NotEmpty(message = "施工许可证不能为空")
    @Valid
    private List<Ot01DTO> permitList;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "审核状态(0待审核，1通过，2不通过)_Select选择器")
    private String status;

    @ApiModelProperty(value = "角色ID列表")
    private String roleIdList;

    @ApiModelProperty(value = "上报地")
    private String report;

    @ApiModelProperty(value = "所属银行")
    private String payBankCode;

    @ApiModelProperty(value = "注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createDate;

    @ApiModelProperty(value = "审核通过时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateDate;
}