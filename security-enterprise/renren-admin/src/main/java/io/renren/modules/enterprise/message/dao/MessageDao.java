package io.renren.modules.enterprise.message.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.kq02.dto.Kq02DTO;
import io.renren.modules.enterprise.message.dto.MessagePage;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/6 8:57
 */
@Mapper
public interface MessageDao extends BaseDao<MessagePage> {
    /**
     * 分页查询
     *
     * @param params Map<String, Object>
     * @return List<MessagePage>
     */
    List<MessagePage> getList(Map<String, Object> params);

    /**
     * 查询项目下边的参建单位
     *
     * @param pj0101 项目ID
     * @return List<Long>
     */
    List<Long> selectPartUnits(Long pj0101);

    /**
     * 查询项目下边的班组信息
     *
     * @param pj0101 项目ID
     * @return List<Long>
     */
    List<Long> selectTeam(Long pj0101);

    /**
     * 查询项目下边的专户信息
     *
     * @param pj0101 项目ID
     * @return Long
     */
    Long selectPa01(Long pj0101);

    /**
     * 查询项目下边的人员信息
     *
     * @param pj0101 项目ID
     * @return List<Long>
     */
    List<Long> selectPersonIds(Long pj0101);

    /**
     * 查询项目下边的管理人员信息
     *
     * @param pj0101 项目ID
     * @return List<Long>
     */
    List<Long> selectManagerIds(Long pj0101);

    /**
     * 查询考勤数据
     *
     * @param pj0101 pj0101
     * @return List<Kq02DTO>
     */
    List<Kq02DTO> selectAttInfo(Long pj0101);
}
