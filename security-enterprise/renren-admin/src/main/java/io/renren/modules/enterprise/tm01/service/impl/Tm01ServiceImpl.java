package io.renren.modules.enterprise.tm01.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.common.utils.SendDataUtil;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.tm01.dao.Tm01Dao;
import io.renren.modules.enterprise.tm01.dto.Tm01DTO;
import io.renren.modules.enterprise.tm01.dto.Tm01PageDTO;
import io.renren.modules.enterprise.tm01.entity.Tm01Entity;
import io.renren.modules.enterprise.tm01.service.Tm01Service;
import io.renren.modules.enterprise.tm02.dto.BTm02DTO;
import io.renren.modules.enterprise.tm02.service.BTm02Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Tm01ServiceImpl extends CrudServiceImpl<Tm01Dao, Tm01Entity, Tm01DTO> implements Tm01Service {

    @Autowired
    private Tm01Service tm01Service;

    @Autowired
    private BTm02Service tm02Service;
    @Autowired
    private SendDataUtil sendDataUtil;

    @Override
    public QueryWrapper<Tm01Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<Tm01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public void update(Tm01DTO dto) {
        //当班组名称发生变化时候,需要校验班组是否存在
        Tm01DTO tm01DTO = tm01Service.get(dto.getTm0101());
        if (!tm01DTO.getTeamname().equals(dto.getTeamname())) {
            tm01Service.checkTeamName(dto.getTeamname(), dto.getPj0101());
        }
        Tm01Entity tm01Entity = ConvertUtils.sourceToTarget(dto, Tm01Entity.class);
        baseDao.updateById(tm01Entity);
        sendDataUtil.sendData(tm01Entity);
    }

    @Override
    public void checkTeamName(String teamName, Long pj0101) {
        Integer teamNameCount = baseDao.queryByName(teamName, pj0101);
        if (teamNameCount > 0) {
            throw new RenException("该班组已存在,请勿重复添加");
        }
    }


    @Override
    public PageData<Tm01PageDTO> pageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Tm01Entity> page = getPage(params, "t.CREATE_DATE", false);
        List<Tm01PageDTO> list = baseDao.getList(params);
        return getPageData(list, page.getTotal(), Tm01PageDTO.class);
    }

    @Override
    public void saveTeamInfo(Tm01DTO dto) {
        //获取当前登录用户的项目ID
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        dto.setPj0101(pj0101);
        dto.setEntrytime(DateUtil.parse(DateUtil.today()));
        dto.setInOrOut("1");
        //校验当前项目下，班组是否存在
        tm01Service.checkTeamName(dto.getTeamname(), pj0101);
        Tm01Entity tm01Entity = ConvertUtils.sourceToTarget(dto, Tm01Entity.class);
        baseDao.insert(tm01Entity);
        //保存进退场信息，新增的时候默认为进场,tm02增加一条数据
        BTm02DTO bTm02DTO = new BTm02DTO();
        bTm02DTO.setTm0101(tm01Entity.getTm0101());
        bTm02DTO.setInOrOut("1");
        bTm02DTO.setEntryOrExitTime(DateUtil.parse(DateUtil.today()));
        tm02Service.save(bTm02DTO);
        sendDataUtil.sendData(tm01Entity);
    }

    @Override
    public void exitTeam(Long[] ids) {
        List<Long> asList = Arrays.asList(ids);
        //tm01设置退场时间和进退场状态
        baseDao.updateBatchExitTeam(asList);
        //tm02增加退场记录
        for (Long id : ids) {
            BTm02DTO bTm02DTO = new BTm02DTO();
            bTm02DTO.setTm0101(id);
            bTm02DTO.setEntryOrExitTime(DateUtil.parse(DateUtil.today()));
            bTm02DTO.setInOrOut("2");
            tm02Service.save(bTm02DTO);
        }
    }

    @Override
    public void entryTeam(Long[] ids) {
        List<Long> asList = Arrays.asList(ids);
        //tm01设置退场时间和进退场状态
        baseDao.updateBatchEntryTeam(asList);
        //tm02增加进场记录
        for (Long id : ids) {
            BTm02DTO bTm02DTO = new BTm02DTO();
            bTm02DTO.setTm0101(id);
            bTm02DTO.setEntryOrExitTime(DateUtil.parse(DateUtil.today()));
            bTm02DTO.setInOrOut("1");
            tm02Service.save(bTm02DTO);
        }
    }
}