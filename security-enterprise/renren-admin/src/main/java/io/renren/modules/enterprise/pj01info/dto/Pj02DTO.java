package io.renren.modules.enterprise.pj01info.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 项目施工许可证
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "项目施工许可证")
public class Pj02DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pj0201;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "工程名称", required = true)
    @NotBlank(message = "工程名称不能为空")
    private String prjname;

    @ApiModelProperty(value = "施工许可证号", required = true)
    @NotBlank(message = "施工许可证号不能为空")
    private String builderlicensenum;

    @ApiModelProperty(value = "发证机关", required = true)
    @NotBlank(message = "发证机关不能为空")
    private String organname;

    @ApiModelProperty(value = "发证日期", required = true)
    @NotNull(message = "发证日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date organdate;

}