package io.renren.modules.enterprise.pj07.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.pj07.dto.Pj07DTO;
import io.renren.modules.enterprise.pj07.dto.Pj07Page;
import io.renren.modules.enterprise.pj07.entity.Pj07Entity;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/7 9:40
 */
public interface Pj07Service extends CrudService<Pj07Entity, Pj07DTO> {
    /**
     * 分页查询
     *
     * @param params Map<String, Object>
     * @return PageData<Pj07DTO>
     */
    PageData<Pj07Page> pageList(Map<String, Object> params);

    /**
     * 保存或者修改
     * @param dto Pj07DTO
     */
    void saveOrUpdate(Pj07DTO dto);
}
