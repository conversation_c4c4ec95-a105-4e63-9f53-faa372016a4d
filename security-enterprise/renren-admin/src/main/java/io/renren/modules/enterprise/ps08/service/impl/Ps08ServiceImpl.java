package io.renren.modules.enterprise.ps08.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.ps08.dao.Ps08Dao;
import io.renren.modules.enterprise.ps08.dto.Ps08DTO;
import io.renren.modules.enterprise.ps08.dto.Ps08PageDTO;
import io.renren.modules.enterprise.ps08.entity.Ps08Entity;
import io.renren.modules.enterprise.ps08.service.Ps08Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 工人不良记录信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-01-05
 */
@Service
public class Ps08ServiceImpl extends CrudServiceImpl<Ps08Dao, Ps08Entity, Ps08DTO> implements Ps08Service {

    @Override
    public QueryWrapper<Ps08Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Ps08Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Ps08PageDTO> pageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Ps08Entity> page = getPage(params, "", false);
        List<Ps08PageDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), Ps08PageDTO.class);
    }

    @Override
    public void save(Ps08DTO dto) {
        Ps08Entity entity = ConvertUtils.sourceToTarget(dto, Ps08Entity.class);
        entity.setPj0101(CommonUtils.userProjectInfo().getPj0101());
        insert(entity);
    }
}