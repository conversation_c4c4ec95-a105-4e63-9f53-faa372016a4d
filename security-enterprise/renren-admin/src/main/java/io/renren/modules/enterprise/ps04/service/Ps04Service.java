package io.renren.modules.enterprise.ps04.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.ps04.dto.*;
import io.renren.modules.enterprise.ps04.entity.Ps04Entity;
import io.renren.modules.enterprise.ps04.excel.Ps04Excel;

import java.util.List;
import java.util.Map;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Ps04Service extends CrudService<Ps04Entity, Ps04DTO> {
    /**
     * 分页查询数据
     *
     * @param params
     * @return
     */
    PageData<Ps04PageDTO> ps04Page(Map<String, Object> params);

    /**
     * 查询人员信息
     *
     * @param id ps0401
     * @return Ps04DTO
     */
    Ps04DTO getPs04(Long id);

    /**
     * 新增保存
     *
     * @param dto
     */
    void savePs04Info(Ps04DTO dto);

    /**
     * 修改保存
     *
     * @param dto
     */
    void updatePs04Info(Ps04DTO dto);

    /**
     * 管理人员退场
     *
     * @param inOrOutList List<ManagerInOrOut>
     */
    void exitPerson(List<ManagerInOrOut> inOrOutList);

    /**
     * 进场
     *
     * @param list 进场参数
     */
    void enterPerson(List<ManagerInOrOut> list);

    void deviceAddPerson(Long[] ids);

    PageData<KeyJobDTO> getKeyJob(Map<String, Object> params);

    List<Map<String, Object>> getKeyStation();

    Result<List<Ot01DTO>> getAttachment(Long ps0401);

    /**
     * 校验管理人员是否关键岗位人员
     *
     * @param ps0401 ID
     * @return boolean
     */
    boolean checkKeyPositionsById(Long ps0401);

    /**
     * 校验项目经理和总监理工程师的唯一性
     *
     * @param cp0201  参建单位ID
     * @param jobType 岗位类型
     * @param pj0101  项目ID
     */
    void checkPartManager(Long cp0201, String jobType, Long pj0101);

    /**
     * 更新管理员头像
     *
     * @param dto ManagerPhoto
     */
    void updateManagerPhoto(ManagerPhoto dto);

    /**
     * 人员导出
     * @param params Map<String, Object>
     * @return List<Ps04Excel>
     */
    List<Ps04Excel> excelList(Map<String, Object> params);
}