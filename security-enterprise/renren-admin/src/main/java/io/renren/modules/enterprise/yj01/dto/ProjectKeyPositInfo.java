package io.renren.modules.enterprise.yj01.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/31 13:23
 */
@Data
@ApiModel(value = "项目关键岗位预警信息")
public class ProjectKeyPositInfo implements Serializable {

    private static final long serialVersionUID = 1953635376582126730L;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "行政区划")
    private String areaCode;

    @ApiModelProperty(value = "联系人")
    private String linkMan;

    @ApiModelProperty(value = "联系电话")
    private String linkPhone;

    @ApiModelProperty(value = "项目经理")
    private String pmName;

    @ApiModelProperty(value = "项目经理人员ID")
    private String pmNameUserId;

    @ApiModelProperty(value = "项目经理工时")
    private String pmNameUserHours;

    @ApiModelProperty(value = "总监理工程师")
    private String generalManager;

    @ApiModelProperty(value = "总监理工程师ID")
    private String generalManagerId;

    @ApiModelProperty(value = "总监理工程师工时")
    private String generalManagerHours;

    @ApiModelProperty(value = "预警内容")
    private String content;
}
