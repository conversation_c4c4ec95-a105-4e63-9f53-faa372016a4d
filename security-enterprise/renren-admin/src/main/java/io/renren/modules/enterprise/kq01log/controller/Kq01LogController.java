package io.renren.modules.enterprise.kq01log.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.kq01log.dto.Kq01LogPageDTO;
import io.renren.modules.enterprise.kq01log.service.Kq01LogService;
import io.renren.modules.enterprise.kq04.dto.Kq04DTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 考勤设备信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@RestController
@RequestMapping("enterprise/kq01Log")
@Api(tags = "考勤设备操作信息表")
public class Kq01LogController {
    @Autowired
    private Kq01LogService kq01LogService;


    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:kq01Log:page")
    public Result<PageData<Kq01LogPageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Kq01LogPageDTO> page = kq01LogService.pageList(params);

        return new Result<PageData<Kq01LogPageDTO>>().ok(page);
    }

    @GetMapping("/infoPage")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:kq01Log:info")
    public Result<PageData<Kq04DTO>> getLog(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Kq04DTO> page = kq01LogService.getLog(params);
        return new Result<PageData<Kq04DTO>>().ok(page);

    }



}