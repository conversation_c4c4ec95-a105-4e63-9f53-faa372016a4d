package io.renren.modules.enterprise.dataShare.facilitator.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.dataShare.facilitator.dto.FacilitatorDTO;
import io.renren.modules.enterprise.dataShare.facilitator.service.FacilitatorService;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dto.FacilitatorProjectDTO;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dto.FacilitatorProjectInfo;
import io.renren.modules.enterprise.dataShare.facilitatorProject.service.FacilitatorProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * 数据共享密钥信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-22
 */
@RestController
@RequestMapping("enterprise/facilitator")
@Api(tags = "企业信息")
public class FacilitatorController {
    @Resource
    private FacilitatorService facilitatorService;
    @Resource
    private FacilitatorProjectService facilitatorProjectService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "name", value = "企业名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "whether", value = "是否可用", paramType = "query", dataType = "String")
    })
    public Result<PageData<FacilitatorDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<FacilitatorDTO> page = facilitatorService.pageInfo(params);

        return new Result<PageData<FacilitatorDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    public Result<FacilitatorDTO> get(@PathVariable("id") Long id) {

        FacilitatorDTO data = facilitatorService.get(id);

        return new Result<FacilitatorDTO>().ok(data);
    }

    @PostMapping("saveInfo")
    @ApiOperation("保存")
    @LogOperation("保存")
    public Result<Object> save(@RequestBody FacilitatorDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        facilitatorService.saveInfo(dto);

        return new Result<>();
    }

    @PutMapping("updateInfo")
    @ApiOperation("修改")
    @LogOperation("修改")
    public Result<Object> update(@RequestBody FacilitatorDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        facilitatorService.update(dto);

        return new Result<>();
    }

    @PostMapping("enable")
    @ApiOperation("启用")
    @LogOperation("启用")
    public Result<Object> enable(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        facilitatorService.updateWhether(ids, 1);

        return new Result<>();
    }

    @PostMapping("disable")
    @ApiOperation("禁用")
    @LogOperation("禁用")
    public Result<Object> delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        facilitatorService.updateWhether(ids, 0);

        return new Result<>();
    }

    @PostMapping("relatedProject")
    @ApiOperation("企业绑定项目")
    @LogOperation("企业绑定项目")
    public Result<Object> relatedProject(@RequestBody FacilitatorProjectDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        facilitatorService.relatedProject(dto);

        return new Result<>();
    }

    @GetMapping("getRelatedProjectList")
    @ApiOperation("获取已绑定的项目信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "facilitatorId", value = "企业ID", paramType = "query", required = true, dataType = "String")
    })
    public Result<List<FacilitatorProjectInfo>> getRelatedProjectList(@ApiIgnore @RequestParam Map<String, Object> params) {

        List<FacilitatorProjectInfo> list = facilitatorProjectService.getRelatedProjectList(params);

        return new Result<List<FacilitatorProjectInfo>>().ok(list);
    }
}