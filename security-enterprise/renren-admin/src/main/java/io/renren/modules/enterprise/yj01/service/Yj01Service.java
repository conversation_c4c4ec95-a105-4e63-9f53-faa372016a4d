package io.renren.modules.enterprise.yj01.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.yj01.dto.ProjectKeyPositInfo;
import io.renren.modules.enterprise.yj01.dto.Yj01DTO;
import io.renren.modules.enterprise.yj01.entity.Yj01Entity;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/31 11:28
 */
public interface Yj01Service extends CrudService<Yj01Entity, Yj01DTO> {

    /**
     * 生成预警或者更新预警信息
     */
    void saveWarningInfo();

    /**
     * 分页列表数据
     * @param params Map<String, Object> 分页参数
     * @return 分页信息List
     */
    PageData<ProjectKeyPositInfo> pageList(Map<String, Object> params);

    /**
     * 查询预警内容
     * @return 预警内容
     */
    String getWarningInfo();
}
