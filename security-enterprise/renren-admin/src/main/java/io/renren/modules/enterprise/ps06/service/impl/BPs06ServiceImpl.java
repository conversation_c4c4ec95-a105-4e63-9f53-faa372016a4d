package io.renren.modules.enterprise.ps06.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.ps06.dao.BPs06Dao;
import io.renren.modules.enterprise.ps06.dto.BPs06DTO;
import io.renren.modules.enterprise.ps06.entity.BPs06Entity;
import io.renren.modules.enterprise.ps06.service.BPs06Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 工人进退场信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-04-27
 */
@Service
public class BPs06ServiceImpl extends CrudServiceImpl<BPs06Dao, BPs06Entity, BPs06DTO> implements BPs06Service {

    @Override
    public QueryWrapper<BPs06Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<BPs06Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}