package io.renren.modules.supervision.attendanceStatistical.service;


import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.supervision.attendanceStatistical.dto.AttendancePersonDTO;

import io.renren.modules.supervision.attendanceStatistical.entity.AttendancePersonEntity;

import java.util.List;
import java.util.Map;

/**
 * @description 考勤人员详情页
 *
 *  @className: AttendancePersonService
 *  @author: jh
 *  @date: 2022-3-1
 */
public interface AttendancePersonService extends CrudService<AttendancePersonEntity, AttendancePersonDTO> {


    PageData<AttendancePersonDTO> getPageDataByIsOut(Map<String, Object> params);

    PageData<AttendancePersonDTO> getPageDataByIsCheck(Map<String, Object> params);

    List<String> getKqByMonth(Map<String, Object> params);
}