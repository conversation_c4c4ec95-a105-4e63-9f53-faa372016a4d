package io.renren.modules.enterprise.pj01report.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目上报配置表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-12
 */
@Data
@ApiModel(value = "项目上报配置表")
public class Pj01ReportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long pj0101;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "所属区域")
    private String areaCode;

    @ApiModelProperty(value = "联系人")
    private String linkMan;

    @ApiModelProperty(value = "联系电话")
    private String linkPhone;

    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    @ApiModelProperty(value = "所属银行")
    private String code;

    @ApiModelProperty(value = "数据上报")
    private String reportType;

    @ApiModelProperty(value = "上报地")
    private String report;

}