package io.renren.modules.supervision.personneldataStatistical.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.supervision.personneldataStatistical.dao.PersonnelDataStatisticalDao;
import io.renren.modules.supervision.personneldataStatistical.dto.PersonnelDataStatisticalDTO;
import io.renren.modules.supervision.personneldataStatistical.entity.PersonnelDataStatisticalEntity;
import io.renren.modules.supervision.personneldataStatistical.service.PersonnelDataStatisticalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class PersonnelDataStatisticalServiceImpl extends CrudServiceImpl<PersonnelDataStatisticalDao, PersonnelDataStatisticalEntity, PersonnelDataStatisticalDTO> implements PersonnelDataStatisticalService {

    @Autowired
    private PersonnelDataStatisticalDao pdsDao;


    @Override
    public List<PersonnelDataStatisticalDTO> getWorkType(Map<String, Object> params) {
        //获取当前账号的deptID
        Long deptId = SecurityUser.getDeptId();
        params.put("deptId",deptId);
        //获取工种总数
        double allWorkType = pdsDao.workTypeCount(params);
        List<PersonnelDataStatisticalDTO> workTypeList = pdsDao.getWorkType(params);
        workTypeList.forEach(personnelDataStatisticalDTO -> {
            double workerTypePercentage = ((double) personnelDataStatisticalDTO.getWorkTypeCount()/ allWorkType) * 100;
            //取小数点后两位
            personnelDataStatisticalDTO.setWorkerTypePercentage((double) Math.round(workerTypePercentage * 100) / 100);
        });
        return workTypeList;
    }

    @Override
    public List<PersonnelDataStatisticalDTO> getNativeData(Map<String, Object> params) {
        //获取当前账号的deptID
        Long deptId = SecurityUser.getDeptId();
        params.put("deptId",deptId);
        //获取籍贯总数
        double allNativeData = pdsDao.allNativeData(params);
        List<PersonnelDataStatisticalDTO> nativeDataList = pdsDao.getNativeData(params);
        nativeDataList.forEach(personnelDataStatisticalDTO -> {
            double nativeDataPercentage = ((double) personnelDataStatisticalDTO.getNativeCount()/ allNativeData) * 100;
            //取小数点后两位
            personnelDataStatisticalDTO.setNativePercentage((double) Math.round(nativeDataPercentage * 100) / 100);
        });
        return nativeDataList;
    }

    @Override
    public List<PersonnelDataStatisticalDTO> getGenderData(Map<String, Object> params) {
        //获取当前账号的deptID
        Long deptId = SecurityUser.getDeptId();
        params.put("deptId",deptId);
        //获取性别总数
        double allGenderData = pdsDao.allGenderData(params);
        List<PersonnelDataStatisticalDTO> genderDataList = pdsDao.getGenderData(params);
        genderDataList.forEach(personnelDataStatisticalDTO -> {
            double genderDataPercentage = ((double) personnelDataStatisticalDTO.getGenderCount()/ allGenderData) * 100;
            //取小数点后两位
            personnelDataStatisticalDTO.setGenderPercentage((double) Math.round(genderDataPercentage * 100) / 100);
        });
        return genderDataList;
    }

    @Override
    public List<PersonnelDataStatisticalDTO> getAgeData(Map<String, Object> params) {
        //获取当前账号的deptID
        Long deptId = SecurityUser.getDeptId();
        params.put("deptId",deptId);
        //获取年龄总数
        double allAgeData = pdsDao.allAgeData(params);
        List<PersonnelDataStatisticalDTO> ageDataList = pdsDao.getAgeData(params);
        ageDataList.forEach(personnelDataStatisticalDTO -> {
            double ageDataPercentage = ((double) personnelDataStatisticalDTO.getAgeCount()/ allAgeData) * 100;
            //取小数点后两位
            personnelDataStatisticalDTO.setAgePercentage((double) Math.round(ageDataPercentage * 100) / 100);
        });
        return ageDataList;
    }

    @Override
    public QueryWrapper<PersonnelDataStatisticalEntity> getWrapper(Map<String, Object> params) {
        return null;
    }
}
