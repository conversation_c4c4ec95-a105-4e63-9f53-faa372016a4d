package io.renren.modules.enterprise.ps04.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/20 8:28
 */
@Data
@ApiModel(value = "管理人员进退场信息")
public class ManagerInOrOut implements Serializable {

    private static final long serialVersionUID = -611541584570062373L;

    @ApiModelProperty(value = "人员ID", required = true)
    @NotNull(message = "PS0401不能为空")
    private Long ps0401;

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull(message = "pj0101不能为空")
    private Long pj0101;
}
