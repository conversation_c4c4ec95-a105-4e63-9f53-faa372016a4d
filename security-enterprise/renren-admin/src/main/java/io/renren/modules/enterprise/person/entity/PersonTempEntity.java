package io.renren.modules.enterprise.person.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 门禁临时人员
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-03-12
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PERSON_TEMP")
public class PersonTempEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 人员ID
     */
	private Long id;
    /**
     * 项目ID
     */
	private Long pj0101;
    /**
     * 姓名
     */
	private String name;
    /**
     * 身份证号码
     */
	private String idCardNumber;
    /**
     * 手机号码
     */
	private String cellphone;
	/**
	 * 头像
	 */
	private String headImageUrl;
	/**
	 * 是否可用
	 */
	private String whether;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}