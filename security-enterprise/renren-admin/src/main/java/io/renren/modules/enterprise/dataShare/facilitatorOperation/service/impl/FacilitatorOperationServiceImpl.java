package io.renren.modules.enterprise.dataShare.facilitatorOperation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.dataShare.facilitatorOperation.dao.FacilitatorOperationDao;
import io.renren.modules.enterprise.dataShare.facilitatorOperation.dto.FacilitatorOperationDTO;
import io.renren.modules.enterprise.dataShare.facilitatorOperation.entity.FacilitatorOperationEntity;
import io.renren.modules.enterprise.dataShare.facilitatorOperation.service.FacilitatorOperationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 数据共享日志表
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-03-22
 */
@Service
public class FacilitatorOperationServiceImpl extends CrudServiceImpl<FacilitatorOperationDao, FacilitatorOperationEntity, FacilitatorOperationDTO> implements FacilitatorOperationService {

    @Override
    public QueryWrapper<FacilitatorOperationEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");
        QueryWrapper<FacilitatorOperationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }


    @Override
    public PageData<FacilitatorOperationDTO> pageInfo(Map<String, Object> params) {
        IPage<FacilitatorOperationEntity> page = getPage(params, "ID", false);
        List<FacilitatorOperationDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), FacilitatorOperationDTO.class);
    }
}