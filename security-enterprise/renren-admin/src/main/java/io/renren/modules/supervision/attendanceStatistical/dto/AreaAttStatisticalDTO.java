package io.renren.modules.supervision.attendanceStatistical.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description
 * @className: AreaAttStatisticalDTO
 * @author: lrl
 * @date: 2021-12-28 13:38
 **/
@Data
public class AreaAttStatisticalDTO implements Serializable {


    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划")
    private String areaCode;

    @ApiModelProperty(value = "行政区划名")
    private String areaName;

    @ApiModelProperty(value = "在建项目数")
    private String inProjectNum;

    @ApiModelProperty(value = "出勤项目数")
    private String attProjectNum;

    @ApiModelProperty(value = "未出勤项目数")
    private String noAttProjectNum;

    @ApiModelProperty(value = "出勤率")
    private String attProjectRate;


}
