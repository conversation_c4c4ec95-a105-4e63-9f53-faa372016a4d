package io.renren.modules.supervision.attendanceStatistical.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description
 * @className: AttendancePersonDTO
 * @author: jh
 * @date: 2022-3-1
 **/
@Data
public class AttendancePersonDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "工人id/管理人员id")
    private String userid;

    @ApiModelProperty(value = "身份证号")
    private String idcardnumber;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "电话号码")
    private String cellphone;

    @ApiModelProperty(value = "岗位类型")
    private String jobtype;

    @ApiModelProperty(value = "工种")
    private String worktypecode;




}
