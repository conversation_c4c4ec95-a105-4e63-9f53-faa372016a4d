package io.renren.modules.enterprise.yj01.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 预警信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Data
@ApiModel(value = "预警信息")
public class Yj01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long yj0101;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "预警内容")
    private String content;

    @ApiModelProperty(value = "预警类型")
    private String type;

}