package io.renren.modules.enterprise.projectDataCompare.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021-12-10 10:17
 */
@Data
@ApiModel(value = "参建单位差异")
public class PartUnitDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "参建单位名称")
    private String corpName;

    @ApiModelProperty(value = "统一社会信用代码")
    private String corpCode;

    @ApiModelProperty(value = "参建类型")
    private String corpType;
}
