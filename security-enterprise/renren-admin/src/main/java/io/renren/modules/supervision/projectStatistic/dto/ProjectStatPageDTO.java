package io.renren.modules.supervision.projectStatistic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目信息统计
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "项目信息分页")
public class ProjectStatPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目所在地")
    private String areaCode;

    @ApiModelProperty(value = "联系人")
    private String linkMan;

    @ApiModelProperty(value = "联系电话")
    private String linkPhone;

    @ApiModelProperty(value = "参建单位数量")
    private String partUnitCount;

    @ApiModelProperty(value = "班组数量")
    private String teamCount;

    @ApiModelProperty(value = "在场工人")
    private String workerCount;

    @ApiModelProperty(value = "在场管理人员")
    private String managerCount;

    @ApiModelProperty(value = "考勤人次")
    private String attendanceCount;
}