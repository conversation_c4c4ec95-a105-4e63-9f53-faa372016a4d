package io.renren.modules.enterprise.dataShare.facilitatorProject.service;

import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dto.FacilitatorProjectDTO;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dto.FacilitatorProjectInfo;
import io.renren.modules.enterprise.dataShare.facilitatorProject.entity.FacilitatorProjectEntity;

import java.util.List;
import java.util.Map;

/**
 * 第三方服务商和项目的关系
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-03-22
 */
public interface FacilitatorProjectService extends CrudService<FacilitatorProjectEntity, FacilitatorProjectDTO> {

    /**
     * 获取企业绑定的项目
     * @param params Map<String, Object>
     */
    List<FacilitatorProjectInfo> getRelatedProjectList(Map<String, Object> params);
}