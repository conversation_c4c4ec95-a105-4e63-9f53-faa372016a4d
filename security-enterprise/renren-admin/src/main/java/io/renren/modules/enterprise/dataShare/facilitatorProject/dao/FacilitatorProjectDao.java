package io.renren.modules.enterprise.dataShare.facilitatorProject.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dto.FacilitatorProjectInfo;
import io.renren.modules.enterprise.dataShare.facilitatorProject.entity.FacilitatorProjectEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 第三方服务商和项目的关系
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-03-22
 */
@Mapper
public interface FacilitatorProjectDao extends BaseDao<FacilitatorProjectEntity> {

    /**
     * 删除数据
     * @param id 企业ID
     */
    void deleteByFacilitatorId(Long id);

    /**
     * 查询企业已绑定的项目
     * @param params Map<String, Object>
     * @return  List<FacilitatorProjectInfo>
     */
    List<FacilitatorProjectInfo> selectByFacilitatorId(Map<String, Object> params);
}