package io.renren.modules.enterprise.ps08.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工人不良记录信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-01-05
 */
@Data
@ApiModel(value = "工人不良记录列表")
public class Ps08PageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID",hidden = true)
    private Long ps0801;

    @ApiModelProperty(value = "人员ID",hidden = true)
    private Long ps0201;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "姓名")
    private String workerName;

    @ApiModelProperty(value = "证件号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idCardNumber;

    @ApiModelProperty(value = "不良行为发生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date happenDate;

    @ApiModelProperty(value = "不良行为描述")
    private String content;

}