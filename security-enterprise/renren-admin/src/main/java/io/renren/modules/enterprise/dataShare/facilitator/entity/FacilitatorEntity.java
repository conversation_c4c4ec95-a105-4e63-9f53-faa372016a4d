package io.renren.modules.enterprise.dataShare.facilitator.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 数据共享密钥信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_FACILITATOR")
public class FacilitatorEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 唯一标识
     */
    private String appKey;
    /**
     * 密钥
     */
    private String appSecret;
    /**
     * 企业名称
     */
    private String name;
    /**
     * 统一社会信用代码
     */
    private String code;
    /**
     * 联系人
     */
    private String linkMan;
    /**
     * 联系电话
     */
    private String linkPhone;
    /**
     * 营业执照
     */
    private String businessLicense;
    /**
     * 是否可用(0否,1是)
     */
    private String whether;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}