package io.renren.modules.enterprise.ps02.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Data
public class Ps02Excel {
    @Excel(name = "姓名",width = 10)
    private String name;

    @Excel(name = "身份证号码",width = 20)
    private String idCardNumber;

    @Excel(name = "所属企业",width = 20)
    private String businessName;

    @Excel(name = "所属班组",width = 20)
    private String teamName;

    @Excel(name = "工种",width = 15)
    private String workType;

    @Excel(name = "民族",width = 10)
    private String nation;

    @Excel(name = "性别",width = 10)
    private String gender;

    @Excel(name = "电话",width = 15)
    private String cellphone;

    @Excel(name = "地址",width = 30)
    private String address;

    @Excel(name = "进场时间",width = 15)
    private String entryTime;

    @Excel(name = "退场时间",width = 15)
    private String exitTime;

}