package io.renren.modules.enterprise.pj07.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/8 9:08
 */
@Data
@ApiModel(value = "项目核查情况")
public class Pj07Page implements Serializable {

    private static final long serialVersionUID = 2241629465034435094L;

    @ApiModelProperty(value = "项目主键ID")
    private Long pj0101;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "所属区域")
    private String areaCode;

    @ApiModelProperty(value = "联系人")
    private String linkMan;

    @ApiModelProperty(value = "联系电话")
    private String linkPhone;

    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    @ApiModelProperty(value = "总包单位")
    private String turnkeyUnit;

    @ApiModelProperty(value = "监理单位")
    private String supervisionUnit;

    @ApiModelProperty(value = "建设单位")
    private String construction;

    @ApiModelProperty(value = "其它单位")
    private String otherUnits;

    @ApiModelProperty(value = "监理单位总监理工程师")
    private String supervisingEngineer;

    @ApiModelProperty(value = "总包单位项目经理")
    private String projectManager;

    @ApiModelProperty(value = "是否已核查")
    private String whether;

    @ApiModelProperty(value = "备注")
    private String remark;

}
