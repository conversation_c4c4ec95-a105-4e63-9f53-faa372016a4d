package io.renren.modules.reportData.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/4/27 10:07
 */
@Data
@ApiModel(value = "数据上报统计")
public class ReportData implements Serializable {

    private static final long serialVersionUID = 8005148349433904544L;

    @ApiModelProperty(value = "时间")
    private String dateTime;

    @ApiModelProperty(value = "所属地")
    private String areaCode;

    @ApiModelProperty(value = "总条数")
    private Integer dataTotal;

    @ApiModelProperty(value = "上报总数")
    private Integer actualTotal;

    @ApiModelProperty(value = "上报率")
    private String perTotal;

    @ApiModelProperty(value = "上报率")
    private BigDecimal perRate;
}
