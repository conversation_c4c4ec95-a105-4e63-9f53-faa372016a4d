package io.renren.modules.enterprise.ps01.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.renren.common.page.PageData;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;

import java.util.Map;

/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Ps01Service extends IService<Ps01Entity> {
    /**
     * 根据身份证号码查询人员基础信息
     *
     * @param idCardNumber 身份证号码
     * @return Ps01DTO
     */
    Ps01DTO selectByIdCardNumber(String idCardNumber);

    /**
     * 列表分页查询
     *
     * @param params
     * @return
     */
    PageData<Ps01DTO> pageList(Map<String, Object> params);

    /**
     * 根据主键加载数据
     *
     * @param id ps0101
     * @return Ps01DTO
     */
    Ps01DTO getPs01Info(Long id);

    /**
     * 保存信息
     *
     * @param dto
     */
    void savePs01Info(Ps01DTO dto);

    /**
     * 修改信息
     *
     * @param dto
     */
    void updatePs01Info(Ps01DTO dto);

    /**
     * 根据业务数据查询人员基础信息
     *
     * @param id PS0201或PS0401
     * @return Ps01DTO
     */
    Ps01DTO getPersonByBusinessId(Long id);

}