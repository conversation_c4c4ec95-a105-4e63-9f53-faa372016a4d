package io.renren.modules.enterprise.ps04.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.ps04.dto.*;
import io.renren.modules.enterprise.ps04.excel.Ps04Excel;
import io.renren.modules.enterprise.ps04.service.Ps04Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.formula.functions.T;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@RestController
@RequestMapping("enterprise/ps04")
@Api(tags = "项目管理人员信息")
public class Ps04Controller {
    @Autowired
    private Ps04Service ps04Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @LogOperation("管理人员分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:ps04:page")
    public Result<PageData<Ps04PageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Ps04PageDTO> page = ps04Service.ps04Page(params);
        return new Result<PageData<Ps04PageDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:ps04:info")
    public Result<Ps04DTO> get(@PathVariable("id") Long id) {
        Ps04DTO data = ps04Service.getPs04(id);
        return new Result<Ps04DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:ps04:save")
    public Result<T> save(@RequestBody Ps04DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps04Service.savePs04Info(dto);

        return new Result<>();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:ps04:update")
    public Result<T> update(@RequestBody Ps04DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps04Service.updatePs04Info(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:ps04:delete")
    public Result<T> delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        ps04Service.delete(ids);

        return new Result<>();
    }

    @PutMapping("updateManagerPhoto")
    @ApiOperation("管理人员头像更新")
    public Result<T> updateManagerPhoto(@RequestBody ManagerPhoto dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps04Service.updateManagerPhoto(dto);

        return new Result<>();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("管理人员导出")
    @RequiresPermissions("enterprise:ps04:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {

        List<Ps04Excel> list = ps04Service.excelList(params);

        ExcelUtils.exportExcel(response, "管理人员信息", list, Ps04Excel.class);
    }

    @PutMapping("exitPerson")
    @ApiOperation("退场")
    @LogOperation("退场")
    @RequiresPermissions("enterprise:ps04:exitPerson")
    public Result<T> exitTeam(@RequestBody List<ManagerInOrOut> inOrOutList) {
        //效验数据
        ValidatorUtils.validateEntity(inOrOutList);

        ps04Service.exitPerson(inOrOutList);

        return new Result<>();
    }

    @PutMapping("enterPerson")
    @ApiOperation("进场")
    @LogOperation("进场")
    @RequiresPermissions("enterprise:ps04:enterPerson")
    public Result<T> enterTeam(@RequestBody List<ManagerInOrOut> inOrOutList) {
        //效验数据
        ValidatorUtils.validateEntity(inOrOutList);

        ps04Service.enterPerson(inOrOutList);

        return new Result<>();
    }

    @PutMapping("personAuthor")
    @ApiOperation("人员下发")
    @RequiresPermissions("enterprise:ps04:personAuthor")
    public Result<T> personAuthor(@RequestBody Long[] ids) {
        ps04Service.deviceAddPerson(ids);
        return new Result<>();
    }

    @GetMapping("getKeyJob")
    @ApiOperation("获取关键岗位信息")
    @RequiresPermissions("enterprise:ps04:getKeyJob")
    public Result<PageData<KeyJobDTO>> getKeyJob(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<KeyJobDTO> keyJob = ps04Service.getKeyJob(params);
        return new Result<PageData<KeyJobDTO>>().ok(keyJob);
    }

    @GetMapping("getKeyStation")
    @ApiOperation("获取关键岗位")
    public Result<List<Map<String, Object>>> getKeyStation() {
        List<Map<String, Object>> keyStation = ps04Service.getKeyStation();
        return new Result<List<Map<String, Object>>>().ok(keyStation);
    }

    @GetMapping("getAttachment/{id}")
    @ApiOperation("获取管理人员档案")
    public Result<List<Ot01DTO>> getAttachment(@PathVariable("id") Long id) {

        return ps04Service.getAttachment(id);
    }

}