package io.renren.modules.enterprise.pj05.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.pj05.dao.Pj05Dao;
import io.renren.modules.enterprise.pj05.dto.Pj05DTO;
import io.renren.modules.enterprise.pj05.entity.Pj05Entity;
import io.renren.modules.enterprise.pj05.service.Pj05Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 项目竣工验收信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
public class Pj05ServiceImpl extends CrudServiceImpl<Pj05Dao, Pj05Entity, Pj05DTO> implements Pj05Service {

    @Override
    public QueryWrapper<Pj05Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Pj05Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}