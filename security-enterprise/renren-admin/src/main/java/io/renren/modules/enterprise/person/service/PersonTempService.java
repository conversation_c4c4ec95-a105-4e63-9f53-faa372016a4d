package io.renren.modules.enterprise.person.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.person.dto.PersonTempDTO;
import io.renren.modules.enterprise.person.entity.PersonTempEntity;

import java.util.List;
import java.util.Map;

/**
 * 门禁临时人员
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-03-12
 */
public interface PersonTempService extends CrudService<PersonTempEntity, PersonTempDTO> {

    /**
     * 保存
     * @param dto PersonTempDTO
     */
    void saveInfo(PersonTempDTO dto);

    /**
     * 修改
     * @param dto PersonTempDTO
     */
    void updateInfo(PersonTempDTO dto);

    /**
     * 删除人员
     * @param list List<PersonTempDTO>
     */
    void deleteInfo(List<PersonTempDTO> list);

    /**
     * 分页
     * @param params Map<String, Object>
     * @return PageData<PersonTempDTO>
     */
    PageData<PersonTempDTO> pageInfo(Map<String, Object> params);

    /**
     * 人员下发
     * @param ids 主键
     */
    void deviceAddPerson(Long[] ids);
}