package io.renren.modules.reportData.service;

import io.renren.modules.enterprise.tj01.dto.ReportWrite;
import io.renren.modules.reportData.dto.ReportData;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/27 10:03
 */
public interface ReportDataService {

    /**
     * 泸州数据上报查询
     *
     * @return ReportData
     */
    ReportData getLuZhouReportData();

    /**
     * 住建厅数据上报查询
     *
     * @return ReportData
     */
    ReportData getDourReportData();

    /**
     * 住建厅数据写入查询
     * @return List<ReportWrite>
     */
    List<ReportWrite> getReportWriteData();
}
