package io.renren.modules.supervision.attendanceStatistical.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.supervision.attendanceStatistical.dto.AttendancePersonDTO;
import io.renren.modules.supervision.attendanceStatistical.service.AttendancePersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;


/**
 * @description 考勤人员详情页
 *
 * @className: AttendancePersonController
 * @author: jh
 * @date: 2022-3-1
 **/
@RestController
@RequestMapping("AttendancePerson/AttPerson")
@Api(tags = "考勤人员详情页")
public class AttendancePersonController {

    @Autowired
    private AttendancePersonService attendancePersonService;

    /**
     * 项目考勤在场人员详情页
     * @param params 参数
     * @return 项目考勤在场人员详情页
     */
    @GetMapping("pageByIsOut")
    @ApiOperation("项目考勤在场人员详情页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "pj0101", value = "项目编码",required = true,  dataType = "String"),
            @ApiImplicitParam(name = "personType", value = "人员类型",required = true,  dataType = "String"),
    })
    public Result<PageData<AttendancePersonDTO>> pageByIsOut(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<AttendancePersonDTO> page = attendancePersonService.getPageDataByIsOut(params);

        return new Result<PageData<AttendancePersonDTO>>().ok(page);
    }


    /**
     * 项目考勤出勤人员详情页
     * @param params 参数
     * @return 项目考勤出勤人员详情页
     */
    @GetMapping("pageByIsCheck")
    @ApiOperation("项目考勤出勤人员详情页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "pj0101", value = "项目编码",required = true,  dataType = "String"),
            @ApiImplicitParam(name = "checkDate", value = "考勤时间", required = true, dataType = "String"),
            @ApiImplicitParam(name = "personType", value = "人员类型", required = true, dataType = "String"),
    })
    public Result<PageData<AttendancePersonDTO>> pageByIsCheck(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<AttendancePersonDTO> page = attendancePersonService.getPageDataByIsCheck(params);

        return new Result<PageData<AttendancePersonDTO>>().ok(page);
    }

    @GetMapping("KqByMonth")
    @ApiOperation("当月考勤天数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "人员id",required = true,  dataType = "String"),
            @ApiImplicitParam(name = "checkDate", value = "考勤时间", required = true, dataType = "String"),
    })
    public Result<List<String>> KqByMonth(@ApiIgnore @RequestParam Map<String, Object> params){
        List<String> kqList = attendancePersonService.getKqByMonth(params);

        return new Result<List<String>>().ok(kqList);
    }

}
