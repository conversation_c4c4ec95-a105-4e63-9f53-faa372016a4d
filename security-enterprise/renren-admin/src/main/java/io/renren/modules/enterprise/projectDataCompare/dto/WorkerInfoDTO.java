package io.renren.modules.enterprise.projectDataCompare.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021-12-10 10:17
 */
@Data
@ApiModel(value = "工人差异数据")
public class WorkerInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "证件号码")
    private String idCardNumber;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
}
