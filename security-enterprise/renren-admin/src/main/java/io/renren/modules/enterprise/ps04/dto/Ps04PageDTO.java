package io.renren.modules.enterprise.ps04.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 管理人员分页信息
 *
 * <AUTHOR>
 * @Date 2020-08-19 9:57
 */
@Data
@ApiModel(value = "项目管理人员信息")
public class Ps04PageDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0401;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "所属项目")
    private String projectName;

    @ApiModelProperty(value = "所属企业")
    private String cp0201;

    @ApiModelProperty(value = "参建类型")
    private String corpType;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "岗位类型")
    private String jobtype;

    @ApiModelProperty(value = "管理类型")
    private String manageType;

    @ApiModelProperty(value = "身份证号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idcardnumber;

    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    @ApiModelProperty(value = "手机号码")
    private String cellphone;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryTime;

    @ApiModelProperty(value = "退场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exitTime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

    @ApiModelProperty(value = "是否允关键岗位人员")
    private String hasKeyPositions;

}
