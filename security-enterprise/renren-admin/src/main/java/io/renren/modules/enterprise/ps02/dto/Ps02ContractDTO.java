package io.renren.modules.enterprise.ps02.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-7-8 08:37:20
 * 工人列表
 */
@Data
@ApiModel(value = "建筑合同信息")
public class Ps02ContractDTO {

    @ApiModelProperty(value = "主键ID")
    private Long ps0201;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证号码")
    private String idcardnumber;

    @ApiModelProperty(value = "法定代表人")
    private String legalman;

    @ApiModelProperty(value = "企业名称")
    private String corpname;

    @ApiModelProperty(value = "性别")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String gender;

    @ApiModelProperty(value = "家庭地址")
    private String address;

    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    @ApiModelProperty(value = "项目名")
    private String projectname;

    @ApiModelProperty(value = "企业联系电话")
    private String linkcellphone;

    @ApiModelProperty(value = "工人联系电话")
    private String cellphone;

    @ApiModelProperty(value = "信用代码")
    private String corpcode;

    @ApiModelProperty(value = "工种")
    private String worktypecode;

    @ApiModelProperty(value = "企业地址")
    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    private String corpaddress;

}
