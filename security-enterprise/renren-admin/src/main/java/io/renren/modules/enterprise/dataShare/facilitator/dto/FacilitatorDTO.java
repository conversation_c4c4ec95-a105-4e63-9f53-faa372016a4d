package io.renren.modules.enterprise.dataShare.facilitator.dto;

import io.renren.common.annotation.iscreditcodevalidator.IsCreditCodeValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 数据共享密钥信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-22
 */
@Data
@ApiModel(value = "数据共享密钥信息")
public class FacilitatorDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "唯一标识")
    private String appKey;

    @ApiModelProperty(value = "密钥")
    private String appSecret;

    @ApiModelProperty(value = "企业名称")
    @NotBlank(message = "企业名称不能为空")
    private String name;

    @ApiModelProperty(value = "统一社会信用代码")
    @NotBlank(message = "统一社会信用代码不能为空")
    @IsCreditCodeValidator(message = "社会统一信用代码格式不正确")
    private String code;

    @ApiModelProperty(value = "联系人")
    private String linkMan;

    @ApiModelProperty(value = "联系电话")
    private String linkPhone;

    @ApiModelProperty(value = "营业执照")
    @NotBlank(message = "营业执照不能为空")
    private String businessLicense;

    @ApiModelProperty(value = "是否可用(0否,1是)")
    private String whether;

}