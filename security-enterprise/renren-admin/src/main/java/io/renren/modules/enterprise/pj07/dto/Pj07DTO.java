package io.renren.modules.enterprise.pj07.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 项目核查情况
 *
 * <AUTHOR>
 * @since 1.0.0 2023-06-08
 */
@Data
@ApiModel(value = "项目核查情况")
public class Pj07DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目ID",required = true)
    @NotNull(message ="项目ID不能为空")
    private Long pj0101;

    @ApiModelProperty(value = "是否已核查")
    private String whether;

    @ApiModelProperty(value = "备注")
    private String remark;

}