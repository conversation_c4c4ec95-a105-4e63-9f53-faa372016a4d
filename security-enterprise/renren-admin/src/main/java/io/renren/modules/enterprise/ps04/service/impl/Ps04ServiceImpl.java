package io.renren.modules.enterprise.ps04.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.file.ImageUtil;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.*;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.admin.sys.enums.SuperAdminEnum;
import io.renren.modules.enterprise.kq05.dto.PersonDTO;
import io.renren.modules.enterprise.kq05.service.Kq05Service;
import io.renren.modules.enterprise.ot01.dao.Ot01Dao;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.ot01.service.Ot01Service;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import io.renren.modules.enterprise.ps01.service.Ps01Service;
import io.renren.modules.enterprise.ps02.dao.Ps02Dao;
import io.renren.modules.enterprise.ps04.dao.Ps04Dao;
import io.renren.modules.enterprise.ps04.dto.*;
import io.renren.modules.enterprise.ps04.entity.Ps04Entity;
import io.renren.modules.enterprise.ps04.excel.Ps04Excel;
import io.renren.modules.enterprise.ps04.service.Ps04Service;
import io.renren.modules.enterprise.ps07.dto.Ps07DTO;
import io.renren.modules.enterprise.ps07.service.Ps07Service;
import io.renren.modules.enterprise.ps09.dto.Ps09DTO;
import io.renren.modules.enterprise.ps09.service.Ps09Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Ps04ServiceImpl extends CrudServiceImpl<Ps04Dao, Ps04Entity, Ps04DTO> implements Ps04Service {

    @Autowired
    private Ps04Service ps04Service;
    @Autowired
    private Ps01Service ps01Service;
    @Autowired
    private Kq05Service kq05Service;
    @Autowired
    private Ps07Service ps07Service;
    @Autowired
    private SendDataUtil sendDataUtil;
    @Autowired
    private Ps02Dao ps02Dao;
    @Autowired
    private Ps04Dao ps04Dao;
    @Autowired
    private Ot01Dao ot01Dao;
    @Autowired
    private Ps09Service ps09Service;
    @Autowired
    private Ot01Service ot01Service;
    /**
     * 总包单位
     */
    private static final String GENERAL_CONTRACT = "9";
    /**
     * 监理单位
     */
    private static final String SUPERVISION_UNIT = "7";
    /**
     * 总包单位项目经理
     */
    private static final String PROJECT_MANAGER = "1009";
    /**
     * 总监理工程师
     */
    private static final String CHIEF_SUPERINTENDENT = "1001";
    /**
     * 人员类型
     */
    private final static String PERSON_TYPE = "2";
    /**
     * 图片header
     */
    private final static String IMAGE = "data:image";

    @Override
    public QueryWrapper<Ps04Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Ps04Entity> wrapper = new QueryWrapper<>();

        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Ps04PageDTO> ps04Page(Map<String, Object> params) {
        paramsToLike(params, "name");
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Ps04Entity> page = getPage(params, "", false);
        List<Ps04PageDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), Ps04PageDTO.class);
    }

    @Override
    public Ps04DTO getPs04(Long id) {
        Ps04DTO ps04Dto = ps04Service.get(id);
        Ps01DTO ps01Info = ps01Service.getPs01Info(ps04Dto.getPs0101());
        ps04Dto.setPs01DTO(ps01Info);
        //附件信息
        List<Ot01DTO> ot01DTOList = ot01Service.loadBusinessData(ps04Dto.getPs0401(), "21");
        ps04Dto.setOt01DTOList(ot01DTOList);
        return ps04Dto;
    }

    @Override
    public void savePs04Info(Ps04DTO dto) {
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        dto.setPj0101(pj0101);
        //校验项目经理和总监理工程师
        checkPartManager(dto.getCp0201(), dto.getJobtype(), pj0101);
        //保存或者修改PS01信息
        Ps01Entity ps01Entity = ConvertUtils.sourceToTarget(dto.getPs01DTO(), Ps01Entity.class);
        int genderByIdCard = IdcardUtil.getGenderByIdCard(ps01Entity.getIdcardnumber());
        ps01Entity.setGender(genderByIdCard > 0 ? "1" : "2");
        //身份证转成大写保存,主要处理最后一位是X的身份证
        ps01Entity.setIdcardnumber(ps01Entity.getIdcardnumber().toUpperCase(Locale.ROOT));
        dto.setPj0101(pj0101);
        if (StringUtils.isNotBlank(dto.getPhoto())) {
            dto.setPhoto(ImageUtil.base64ToImage(dto.getPhoto()));
        }
        // 如果是base64文件则处理
        if (StrUtil.startWith(Objects.requireNonNull(ps01Entity).getHeadimageurl(), IMAGE)) {
            ps01Entity.setHeadimageurl(ImageUtil.base64ToImage(ps01Entity.getHeadimageurl()));
        }
        ps01Service.saveOrUpdate(ps01Entity);
        dto.setPs0101(ps01Entity.getPs0101());
        //判断该项目下是否有工人
        Integer sum = ps02Dao.selectCountPs0101(pj0101, ps01Entity.getPs0101());
        if (sum > 0) {
            throw new RenException("项目中已存在该人员！请勿重复添加");
        }
        //判读岗位是否属于关键岗位
        boolean isKeyPositions = checkKeyPositions(dto.getCp0201(), dto.getJobtype());
        if (isKeyPositions) {
            //人证比对
            CompareFaceUtil.personIdentCheck("",
                    dto.getPhoto(),
                    ps01Entity.getIdcardnumber(),
                    ps01Entity.getName());
            //人员审核
            Ps09DTO ps09DTO = ConvertUtils.sourceToTarget(dto, Ps09DTO.class);
            ps09Service.saveInfo(ps09DTO);
            return;
        }
        //保存PS04信息
        dto.setPj0101(pj0101);
        dto.setEntrytime(DateUtil.parse(DateUtil.today()));
        dto.setInOrOut("1");
        ps04Service.save(dto);
        //进退场记录表增加数据
        Ps07DTO ps07DTO = new Ps07DTO();
        ps07DTO.setEntryOrExitTime(DateUtil.parse(DateUtil.today()));
        ps07DTO.setInOrOut("1");
        ps07DTO.setPs0401(dto.getPs0401());
        ps07Service.save(ps07DTO);
        // 附件资料
        List<Long> longList = dto.getOt01DTOList().stream()
                .map(Ot01DTO::getOt0101).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(longList)) {
            ot01Dao.updateByIdBusily(dto.getPs0401(), longList);
        }
        //人员注册、下发到设备
        kq05Service.saveCreatePerson(pj0101, dto.getPs0401(), ps01Entity.getName(), dto.getPhoto(), PERSON_TYPE);
        sendDataUtil.sendData(dto, Constant.ADD);
    }

    @Override
    public void updatePs04Info(Ps04DTO dto) {
        //判断用户否超级管理员
        Integer superAdmin = SecurityUser.getUser().getSuperAdmin();
        boolean isKeyPositions = checkKeyPositions(dto.getCp0201(), dto.getJobtype());
        if (superAdmin == SuperAdminEnum.NO.value()) {
            //判读岗位是否属于关键岗位
            if (isKeyPositions) {
                throw new RenException("关键岗位人员信息不允许修改");
            }
        }
        String photo = dto.getPhoto();
        // 更新照片下发设备
        if (StrUtil.startWith(photo, IMAGE)) {
            String headImage = ImageUtil.base64ToImage(dto.getPhoto());
            dto.setPhoto(headImage);
            if (isKeyPositions) {
                //人证比对
                CompareFaceUtil.personIdentCheck("",
                        headImage,
                        dto.getPs01DTO().getIdcardnumber(),
                        dto.getPs01DTO().getName());
            }
            //更新设备人员照片
            kq05Service.updatePersonImage(dto.getPj0101(), dto.getPs0401(), headImage, PERSON_TYPE);
        }
        //保存或者修改PS01信息
        Ps01Entity ps01Entity = ConvertUtils.sourceToTarget(dto.getPs01DTO(), Ps01Entity.class);
        ps01Service.saveOrUpdate(ps01Entity);
        //更新PS04
        ps04Service.update(dto);
        // 附件资料
        List<Long> longList = dto.getOt01DTOList().stream()
                .map(Ot01DTO::getOt0101).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(longList)) {
            ot01Dao.updateByIdBusily(dto.getPs0401(), longList);
        }
        sendDataUtil.sendData(dto, Constant.UPDATE);
    }

    @Override
    public void exitPerson(List<ManagerInOrOut> inOrOutList) {
        baseDao.updateInOrOutByIds(inOrOutList, "2");
        for (ManagerInOrOut managerInOrOut : inOrOutList) {
            //人员ID
            Long ps0401 = managerInOrOut.getPs0401();
            //项目ID
            Long pj0101 = managerInOrOut.getPj0101();
            if (SecurityUser.getUser().getSuperAdmin() != SuperAdminEnum.YES.value()) {
                //判读岗位是否属于关键岗位
                boolean isKeyPositions = checkKeyPositionsById(ps0401);
                if (isKeyPositions) {
                    throw new RenException("关键岗位人员不允许退场操作");
                }
            }
            //退场记录表增加数据
            Ps07DTO ps07DTO = new Ps07DTO();
            ps07DTO.setEntryOrExitTime(DateUtil.parse(DateUtil.today()));
            ps07DTO.setInOrOut("2");
            ps07DTO.setPs0401(ps0401);
            ps07Service.save(ps07DTO);
            //人员退场进行销权操作
            kq05Service.personSellRight(pj0101, ps0401, PERSON_TYPE);
            sendDataUtil.sendData(ps0401, pj0101);
        }

    }

    @Override
    public void enterPerson(List<ManagerInOrOut> list) {
        baseDao.updateInOrOutByIds(list, "1");
        for (ManagerInOrOut managerInOrOut : list) {
            //人员ID
            Long ps0401 = managerInOrOut.getPs0401();
            //项目ID
            Long pj0101 = managerInOrOut.getPj0101();
            if (SecurityUser.getUser().getSuperAdmin() == SuperAdminEnum.NO.value()) {
                //判断岗位是否属于关键岗位
                boolean isKeyPositions = checkKeyPositionsById(ps0401);
                if (isKeyPositions) {
                    throw new RenException("关键岗位人员不允许进场操作");
                }
            }
            Ps07DTO ps07DTO = new Ps07DTO();
            ps07DTO.setEntryOrExitTime(DateUtil.parse(DateUtil.today()));
            ps07DTO.setInOrOut("1");
            ps07DTO.setPs0401(ps0401);
            ps07Service.save(ps07DTO);
            //重新入场
            Ps04Entity ps04Entity = baseDao.selectById(ps0401);
            Ps04DTO ps04DTO = ConvertUtils.sourceToTarget(ps04Entity, Ps04DTO.class);
            //重新下发设备
            kq05Service.addDevicePerson(ps0401, pj0101, PERSON_TYPE);
            sendDataUtil.sendData(ps04DTO, Constant.ENTRY);
        }
    }

    @Override
    public void deviceAddPerson(Long[] ids) {
        List<Long> longs = Arrays.asList(ids);
        List<PersonDTO> person = baseDao.selectPersonByIds(longs);
        for (PersonDTO personDTO : person) {
            //人员授权到设备
            kq05Service.saveCreatePerson(personDTO.getPj0101(), personDTO.getUserId(), personDTO.getName(), personDTO.getImageUrl(), PERSON_TYPE);
        }
    }

    @Override
    public PageData<KeyJobDTO> getKeyJob(Map<String, Object> params) {
        Long deptId = SecurityUser.getDeptId();
        params.put("deptId", deptId);
        IPage<Ps04Entity> page = getPage(params, "", false);
        List<KeyJobDTO> keyJobData = baseDao.getKeyJobData(params);
        return getPageData(keyJobData, page.getTotal(), KeyJobDTO.class);
    }

    @Override
    public List<Map<String, Object>> getKeyStation() {
        List<Map<String, Object>> keyStation = baseDao.getKeyStation();
        return keyStation;
    }

    @Override
    public Result<List<Ot01DTO>> getAttachment(Long ps0401) {

        return new Result<List<Ot01DTO>>().ok(ps04Dao.getAttachments(ps0401));
    }

    @Override
    public boolean checkKeyPositionsById(Long ps0401) {
        int count = ps04Dao.selectKeyPositionsById(ps0401);
        return count > 0;
    }

    /**
     * 校验项目经理和总监理工程师的唯一性
     */
    @Override
    public void checkPartManager(Long cp0201, String jobType, Long pj0101) {
        //查询参建单位类型
        String partType = ps04Dao.selectTypeById(cp0201);
        //查询数量
        Integer count = ps04Dao.selectByCount(partType, jobType, pj0101);
        //总包单位项目经理
        if (GENERAL_CONTRACT.equals(partType) && PROJECT_MANAGER.equals(jobType)) {
            if (count > 0) {
                throw new RenException("项目经理已存在,请勿重复录入!");
            }
        }
        if (SUPERVISION_UNIT.equals(partType) && CHIEF_SUPERINTENDENT.equals(jobType)) {
            if (count > 0) {
                throw new RenException("总监理工程师已存在,请勿重复录入!");
            }

        }

    }

    @Override
    public void updateManagerPhoto(ManagerPhoto dto) {
        if (StrUtil.startWith(dto.getManagerPhoto(), IMAGE)) {
            String photo = ImageUtil.base64ToImage(dto.getManagerPhoto());
            //人证比对
            Ps01DTO ps01Info = ps01Service.getPersonByBusinessId(dto.getPs0401());
            CompareFaceUtil.personIdentCheck("",
                    photo,
                    ps01Info.getIdcardnumber(),
                    ps01Info.getName());
            //更新人员照片信息
            baseDao.updateManagerPhoto(dto.getPs0401(), photo);
            //更新设备人员照片
            kq05Service.updatePersonImage(dto.getPj0101(), dto.getPs0401(), photo, PERSON_TYPE);
            return;
        }
        throw new RenException("头像格式错误!");
    }

    @Override
    public List<Ps04Excel> excelList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        return baseDao.selectManagerExcelList(params);
    }

    /**
     * 校验岗位是否属于关键岗位
     *
     * @param cp0201  参建单位Id
     * @param jobType 岗位类型
     * @return boolean
     */
    private boolean checkKeyPositions(Long cp0201, String jobType) {
        int count = baseDao.getKeyPositions(cp0201, jobType);
        return count > 0;
    }
}