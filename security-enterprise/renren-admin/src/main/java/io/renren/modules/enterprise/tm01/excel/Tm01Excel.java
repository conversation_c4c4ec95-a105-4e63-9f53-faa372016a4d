package io.renren.modules.enterprise.tm01.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Data
public class Tm01Excel {
    @Excel(name = "主键ID")
    private BigDecimal tm0101;
    @Excel(name = "企业ID")
    private BigDecimal cp0101;
    @Excel(name = "项目ID")
    private BigDecimal pj0101;
    @Excel(name = "班组编号")
    private String teamsysno;
    @Excel(name = "班组名称")
    private String teamname;
    @Excel(name = "班组长姓名")
    private String teamleadername;
    @Excel(name = "班组长联系电话")
    private String teamleaderphone;
    @Excel(name = "身份证号码")
    private String teamleaderidnumber;
    @Excel(name = "进场时间")
    private Date entrytime;
    @Excel(name = "退场时间")
    private Date exittime;
    @Excel(name = "备注")
    private String memo;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}