package io.renren.modules.supervision.projectStatistic.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.supervision.projectStatistic.dto.ProjectStatDTO;
import io.renren.modules.supervision.projectStatistic.dto.ProjectStatPageDTO;
import io.renren.modules.supervision.projectStatistic.service.ProjectStatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-12-28 10:15
 */
@RestController
@RequestMapping("supervision/project")
@Api(tags = "项目统计")
public class ProjectStatController {
    @Autowired
    private ProjectStatService projectStatService;

    @GetMapping("info")
    @ApiOperation("项目统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaCode", value = "行政区划", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "treeLevel", value = "所属层级", paramType = "query", required = true, dataType = "String")
    })
    @RequiresPermissions("supervision:project:info")
    public Result<List<ProjectStatDTO>> info(@ApiIgnore @RequestParam Map<String, Object> params) {
        List<ProjectStatDTO> list = projectStatService.getList(params);
        return new Result<List<ProjectStatDTO>>().ok(list);
    }

    @GetMapping("pageList")
    @ApiOperation("项目列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "areaCode", value = "行政区划", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "projectName", value = "项目名称", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "projectStat", value = "项目状态", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "month", value = "月份(yyyy-MM)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("supervision:project:pageList")
    public Result<PageData<ProjectStatPageDTO>> pageList(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<ProjectStatPageDTO> list = projectStatService.getPageList(params);
        return new Result<PageData<ProjectStatPageDTO>>().ok(list);
    }

}
