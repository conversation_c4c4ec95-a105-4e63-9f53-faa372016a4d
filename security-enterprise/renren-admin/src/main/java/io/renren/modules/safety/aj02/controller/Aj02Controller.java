package io.renren.modules.safety.aj02.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.safety.aj02.dto.Aj02DTO;
import io.renren.modules.safety.aj02.dto.Aj02PageDTO;
import io.renren.modules.safety.aj02.service.Aj02Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 建筑起重机械使用登记信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-11
 */
@RestController
@RequestMapping("safety/aj02")
@Api(tags = "建筑起重机械使用登记信息")
public class Aj02Controller {
    @Autowired
    private Aj02Service aj02Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int")

    })
    @RequiresPermissions("safety:aj02:page")
    public Result<PageData<Aj02PageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Aj02PageDTO> page = aj02Service.pageList(params);

        return new Result<PageData<Aj02PageDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("safety:aj02:info")
    public Result<Aj02DTO> get(@PathVariable("id") Long id) {
        Aj02DTO data = aj02Service.getInfo(id);

        return new Result<Aj02DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("safety:aj02:save")
    public Result save(@RequestBody Aj02DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        aj02Service.saveInfo(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("safety:aj02:update")
    public Result update(@RequestBody Aj02DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        aj02Service.updateInfo(dto);

        return new Result();
    }

}