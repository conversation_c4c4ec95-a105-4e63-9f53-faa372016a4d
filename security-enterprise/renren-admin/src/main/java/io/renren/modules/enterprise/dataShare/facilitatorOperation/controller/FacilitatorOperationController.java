package io.renren.modules.enterprise.dataShare.facilitatorOperation.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.dataShare.facilitatorOperation.dto.FacilitatorOperationDTO;
import io.renren.modules.enterprise.dataShare.facilitatorOperation.service.FacilitatorOperationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 数据共享日志表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-22
 */
@RestController
@RequestMapping("enterprise/facilitatorOperation")
@Api(tags = "日志信息")
public class FacilitatorOperationController {
    @Autowired
    private FacilitatorOperationService facilitatorOperationService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "appKey", value = "企业名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:facilitatorOperation:page")
    public Result<PageData<FacilitatorOperationDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<FacilitatorOperationDTO> page = facilitatorOperationService.pageInfo(params);

        return new Result<PageData<FacilitatorOperationDTO>>().ok(page);
    }

}