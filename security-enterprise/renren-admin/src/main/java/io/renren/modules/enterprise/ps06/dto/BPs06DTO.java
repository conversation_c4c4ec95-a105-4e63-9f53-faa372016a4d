package io.renren.modules.enterprise.ps06.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工人进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-04-27
 */
@Data
@ApiModel(value = "工人进退场信息")
public class BPs06DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0601;

    @ApiModelProperty(value = "工人主键ID")
    private Long ps0201;

    @ApiModelProperty(value = "进退场时间")
    private Date entryOrExitTime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

    @ApiModelProperty(value = "备注")
    private String memo;

}