package io.renren.modules.supervision.attendanceStatistical.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.supervision.attendanceStatistical.dto.AreaAttStatisticalDTO;
import io.renren.modules.supervision.attendanceStatistical.dto.AttStatisticalDTO;
import io.renren.modules.supervision.attendanceStatistical.entity.AttStatisticalEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @description: 考勤统计页面
 * @className: EquipmentStatisticalDao
 * @author: lrl
 * @date: 2021-12-28 13:34
 **/
@Mapper
public interface AttStatisticalDao extends BaseDao<AttStatisticalEntity> {
    /**
     * 考勤统计分页数据
     * @param params 查询参数
     * @return 考勤统计分页数据
     */
    List<AttStatisticalDTO> getPageData(Map<String, Object> params);
    /**
     * 地区项目考勤统计分页数据
     * @param params 查询参数
     * @return 地区项目考勤统计分页数据
     */
    List<AreaAttStatisticalDTO> getAreaCodePageData( Map<String, Object> params);
}
