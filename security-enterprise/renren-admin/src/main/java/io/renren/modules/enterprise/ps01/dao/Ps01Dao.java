package io.renren.modules.enterprise.ps01.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface Ps01Dao extends BaseDao<Ps01Entity> {
    /**
     * 查询人员基础信息
     * @param idCardNumber 身份证号码
     * @return Ps01DTO
     */
    Ps01DTO loadPs01(String idCardNumber);

    /**
     * 查询列表数据
     * @param page page
     * @param params params
     * @return
     */
    List<Ps01DTO> getList(Page<Ps01DTO> page, Map<String, Object> params);

    /**
     * 查询人员基础信息
     * @param id PS0201或者PS0401
     * @return Ps01DTO
     */
    Ps01DTO selectPersonByBusinessId(Long id);
}