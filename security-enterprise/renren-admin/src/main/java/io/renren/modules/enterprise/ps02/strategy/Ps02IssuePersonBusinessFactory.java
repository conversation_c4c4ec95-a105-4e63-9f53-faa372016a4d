package io.renren.modules.enterprise.ps02.strategy;

import io.renren.common.constant.enums.DeviceTypeEnums;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 人员下发工厂
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2025/6/17 9:26
 **/
public class Ps02IssuePersonBusinessFactory {

    private static final Logger log = LoggerFactory.getLogger(Ps02IssuePersonBusinessFactory.class);

    /**
     * 保存策略集合
     */
    private static final Map<String, AbstractPs02IssuePersonHandleStrategy> STRATEGY_MAP = new HashMap<>();

    /**
     * 添加策略实例
     *
     * @param type     类型
     * @param strategy 策略
     */
    public static void registerStrategy(DeviceTypeEnums type, AbstractPs02IssuePersonHandleStrategy strategy) {
        STRATEGY_MAP.put(type.getType(), strategy);
    }

    /**
     * 获取策略实例
     *
     * @param type 类型
     * @return
     */
    public static AbstractPs02IssuePersonHandleStrategy getImpl(DeviceTypeEnums type) {
        return STRATEGY_MAP.get(type.getType());
    }

    /**
     * 获取策略实例
     *
     * @param type 类型
     * @return
     */
    public static AbstractPs02IssuePersonHandleStrategy getImpl(String type) {
        DeviceTypeEnums endpoints = DeviceTypeEnums.of(type);
        return STRATEGY_MAP.get(endpoints.getType());
    }
}
