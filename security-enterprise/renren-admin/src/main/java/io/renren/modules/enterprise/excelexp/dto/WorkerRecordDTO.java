package io.renren.modules.enterprise.excelexp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * DO 用工备案表对象
 *
 * @className: WorkerRecordDTO
 * @author: lrl
 * @date: 2021-05-18 10:36
 **/
@Data
@ApiModel(value = "用工备案表对象")
public class WorkerRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "班组名称")
    private String teamname;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "身份证号码")
    private String idcardnumber;

    @ApiModelProperty(value = "联系电话")
    private String cellphone;

    @ApiModelProperty(value = "从事工种")
    private String worktypecode;

    @ApiModelProperty(value = "标价")
    private String  unitprice;

    @ApiModelProperty(value = "用工开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String starttime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "用工结束时间")
    private String endtime;


}
