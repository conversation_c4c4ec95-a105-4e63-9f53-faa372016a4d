package io.renren.modules.supervision.personneldataStatistical.entity;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description:人员数据统计分析
 * @author: jinghai
 * @date: 2022/3/8 13:38
 **/

@Data
@EqualsAndHashCode(callSuper = false)
public class PersonnelDataStatisticalEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 工种代码
     */
    private String workTypeCode;

    /**
     * 工种数量
     */
    private Long workTypeCount;

    /**
     * 工种占比
     */
    private Double workTypePercentage;

    /**
     * 籍贯代码
     */
    private Long nativeCode;

    /**
     * 籍贯数量
     */
    private Long nativeCount;

    /**
     * 籍贯占比
     */
    private Double nativePercentage;


    /**
     * 性别代码
     */
    private String genderCode;

    /**
     * 性别数量
     */
    private Long genderCount;

    /**
     * 性别占比
     */
    private Double genderPercentage;

    /**
     * 年龄段
     */
    private String ageGroup;

    /**
     * 年龄数量
     */
    private Long ageCount;

    /**
     * 年龄占比
     */
    private Double agePercentage;
}
