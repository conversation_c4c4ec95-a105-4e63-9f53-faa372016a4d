package io.renren.modules.enterprise.tm01.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.annotation.cachelockvalidator.CacheLock;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.tm01.dto.Tm01DTO;
import io.renren.modules.enterprise.tm01.dto.Tm01PageDTO;
import io.renren.modules.enterprise.tm01.excel.Tm01Excel;
import io.renren.modules.enterprise.tm01.service.Tm01Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@RestController
@RequestMapping("enterprise/tm01")
@Api(tags = "班组基础信息")
public class Tm01Controller {
    @Autowired
    private Tm01Service tm01Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "inOrOut", value = "进退场状态", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:tm01:page")
    public Result<PageData<Tm01PageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Tm01PageDTO> page = tm01Service.pageList(params);
        return new Result<PageData<Tm01PageDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:tm01:info")
    public Result<Tm01DTO> get(@PathVariable("id") Long id) {

        Tm01DTO data = tm01Service.get(id);
        return new Result<Tm01DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @CacheLock(prefix = "tm01")
    @RequiresPermissions("enterprise:tm01:save")
    public Result save(@CacheParam @RequestBody Tm01DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        tm01Service.saveTeamInfo(dto);
        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @CacheLock(prefix = "tm0101")
    @RequiresPermissions("enterprise:tm01:update")
    public Result update(@RequestBody Tm01DTO dto) {

        //效验数据
        ValidatorUtils.validateEntity(dto);
        tm01Service.update(dto);
        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:tm01:delete")
    public Result delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");
        tm01Service.delete(ids);
        return new Result();
    }

    @PutMapping("entryTeam")
    @ApiOperation("进场")
    @LogOperation("进场")
    @RequiresPermissions("enterprise:tm01:entryTeam")
    public Result entryTeam(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");
        tm01Service.entryTeam(ids);
        return new Result();
    }

    @PutMapping("exitTeam")
    @ApiOperation("退场")
    @LogOperation("退场")
    @RequiresPermissions("enterprise:tm01:exitTeam")
    public Result exitTeam(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");
        tm01Service.exitTeam(ids);
        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("enterprise:tm01:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {

        List<Tm01DTO> list = tm01Service.list(params);
        ExcelUtils.exportExcelToTarget(response, null, list, Tm01Excel.class);
    }
}