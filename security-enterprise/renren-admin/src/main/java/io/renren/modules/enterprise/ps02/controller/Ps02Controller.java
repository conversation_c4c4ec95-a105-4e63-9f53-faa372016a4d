package io.renren.modules.enterprise.ps02.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.annotation.cachelockvalidator.CacheLock;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.ps02.dto.Ps02DTO;
import io.renren.modules.enterprise.ps02.dto.Ps02EmpRecordDTO;
import io.renren.modules.enterprise.ps02.dto.Ps02PageDTO;
import io.renren.modules.enterprise.ps02.excel.Ps02Excel;
import io.renren.modules.enterprise.ps02.service.Ps02Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.formula.functions.T;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@RestController
@RequestMapping("enterprise/ps02")
@Api(tags = "建筑工人信息")
public class Ps02Controller {
    @Autowired
    private Ps02Service ps02Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:ps02:page")
    @LogOperation("工人分页查询")
    public Result<PageData<Ps02PageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<Ps02PageDTO> page = ps02Service.pageList(params);

        return new Result<PageData<Ps02PageDTO>>().ok(page);
    }

    @GetMapping("empRecordPage")
    @ApiOperation("用工记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "ps0101", value = "人员ID", paramType = "query", required = true, dataType = "String")
    })
    public Result<PageData<Ps02EmpRecordDTO>> empRecordPage(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<Ps02EmpRecordDTO> page = ps02Service.empRecordPageList(params);

        return new Result<PageData<Ps02EmpRecordDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:ps02:info")
    public Result<Ps02DTO> get(@PathVariable("id") Long id) {

        Ps02DTO data = ps02Service.getPs02Info(id);

        return new Result<Ps02DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @CacheLock(prefix = "ps02")
    @RequiresPermissions("enterprise:ps02:save")
    public Result<T> save(@CacheParam @RequestBody Ps02DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps02Service.savePs02Info(dto);

        return new Result<>();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @CacheLock(prefix = "ps02")
    @RequiresPermissions("enterprise:ps02:update")
    public Result<T> update(@RequestBody Ps02DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps02Service.updatePs02Info(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:ps02:delete")
    public Result<T> delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        ps02Service.delete(ids);

        return new Result<>();
    }

    @PutMapping("exitPerson")
    @ApiOperation("退场")
    @LogOperation("退场")
    @RequiresPermissions("enterprise:ps02:exitPerson")
    public Result<T> exitTeam(@RequestBody List<Ps02DTO> list) {
        //效验数据
        ValidatorUtils.validateEntity(list);

        ps02Service.exitPerson(list);

        return new Result<>();
    }

    @PutMapping("enterPerson")
    @ApiOperation("进场")
    @LogOperation("进场")
    @RequiresPermissions("enterprise:ps02:enterPerson")
    public Result<T> enterTeam(@RequestBody List<Ps02DTO> list) {
        //效验数据
        ValidatorUtils.validateEntity(list);

        ps02Service.enterPerson(list);

        return new Result<>();
    }

    @PutMapping("personAuthor")
    @ApiOperation("人员下发")
    @RequiresPermissions("enterprise:ps02:personAuthor")
    public Result<T> personAuthor(@RequestBody Long[] ids) {

        ps02Service.deviceAddPerson(ids);

        return new Result<>();
    }

    @GetMapping("/exportContract")
    @ApiOperation("导出工人合同")
    @RequiresPermissions("enterprise:ps02:personAuthor")
    public void exportContract(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {

        ps02Service.exportContract(params, response);

    }

    @GetMapping("exportPersonInfo")
    @ApiOperation("导出人员信息")
    @LogOperation("导出人员信息")
    @RequiresPermissions("enterprise:ps02:exportPersonInfo")
    public void exportPersonInfo(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {

        List<Ps02Excel> list = ps02Service.getPersonList(params);

        ExcelUtils.exportExcel(response, "工人信息", list, Ps02Excel.class);
    }
}