package io.renren.modules.enterprise.dataShare.facilitatorOperation.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.dataShare.facilitatorOperation.dto.FacilitatorOperationDTO;
import io.renren.modules.enterprise.dataShare.facilitatorOperation.entity.FacilitatorOperationEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 数据共享日志表
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-03-22
 */
@Mapper
public interface FacilitatorOperationDao extends BaseDao<FacilitatorOperationEntity> {

    /**
     * 查询数据
     * @param params Map<String, Object>
     * @return List<FacilitatorOperationDTO>
     */
    List<FacilitatorOperationDTO> getListData(Map<String, Object> params);
}