package io.renren.modules.enterprise.ps04.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/28 13:06
 */
@Data
@ApiModel(value = "管理人员头像更新")
public class ManagerPhoto implements Serializable {

    private static final long serialVersionUID = -7388820694392407390L;

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "ps0401不能为空")
    private Long ps0401;

    @ApiModelProperty(value = "项目ID", required = true)
    @NotNull(message = "pj0101不能为空")
    private Long pj0101;

    @ApiModelProperty(value = "头像信息", required = true)
    @NotNull(message = "managerPhoto不能为空")
    private String managerPhoto;

}
