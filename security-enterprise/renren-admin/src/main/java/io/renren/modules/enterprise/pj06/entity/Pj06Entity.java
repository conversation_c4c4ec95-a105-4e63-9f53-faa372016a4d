package io.renren.modules.enterprise.pj06.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PJ06")
public class Pj06Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Long pj0601;
    /**
     * 项目名称
     */
    private String name;
    /**
     * 所属行业
     */
    private String industry;
    /**
     * 项目所在地
     */
    private String areacode;
    /**
     * 联系人姓名
     */
    private String linkman;
    /**
     * 联系人电话
     */
    private String linkphone;
    /**
     * 建设单位名称
     */
    private String constructionname;
    /**
     * 建设单位统一社会信用代码
     */
    private String constructionnumber;
    /**
     * 施工总承包单位名称
     */
    private String contractname;
    /**
     * 施工总承包单位统一社会信用代码
     */
    private String contractnumber;
    /**
     * 备注
     */
    private String memo;
    /**
     * 审核状态(0待审核，1通过，2不通过)
     */
    private String status;

    /**
     * 角色
     */
    private String roleIdList;

    /**
     * 上报地
     */
    private String report;

    /**
     * 上报银行
     */
    private String payBankCode;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;
}