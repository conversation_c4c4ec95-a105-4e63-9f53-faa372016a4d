package io.renren.modules.supervision.attendanceStatistical.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.supervision.attendanceStatistical.dto.AreaAttStatisticalDTO;
import io.renren.modules.supervision.attendanceStatistical.dto.AttStatisticalDTO;
import io.renren.modules.supervision.attendanceStatistical.service.AttStatisticalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * @description 考勤统计页面
 *
 * @className: AttStatisticalController
 * @author: lrl
 * @date: 2021-12-28 13:15
 **/
@RestController
@RequestMapping("attendanceStatistical/AttStatistical")
@Api(tags = "考勤统计页面")
public class AttStatisticalController {

    @Autowired
    private AttStatisticalService attStatisticalService;

    /**
     * 项目考勤统计
     * @param params 参数
     * @return 项目考勤统计
     */
    @GetMapping("page")
    @ApiOperation("项目考勤统计分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "projectName", value = "项目名称",  dataType = "String"),
            @ApiImplicitParam(name = "areaCode", value = "行政区划",  required = true, dataType = "String"),
            @ApiImplicitParam(name = "checkDate", value = "考勤时间", required = true, dataType = "String"),
    })
    @RequiresPermissions("attendanceStatistical:AttStatistical:page")
    public Result<PageData<AttStatisticalDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<AttStatisticalDTO> page = attStatisticalService.getPageData(params);

        return new Result<PageData<AttStatisticalDTO>>().ok(page);
    }

    /**
     * 地市州考勤统计
     * @param params 参数
     * @return 市州考勤统计分页数据
     */
    @GetMapping("areaCodePage")
    @ApiOperation("地区项目考勤统计分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "areaCode", value = "行政区划",  required = true, dataType = "String"),
            @ApiImplicitParam(name = "checkDate", value = "考勤时间", required = true, dataType = "String"),
    })
    @RequiresPermissions("attendanceStatistical:AttStatistical:areaCodePage")
    public Result<List<AreaAttStatisticalDTO>> areaCodePage(@ApiIgnore @RequestParam Map<String, Object> params) {

        List<AreaAttStatisticalDTO> page = attStatisticalService.getAreaCodePageData(params);

        return new Result<List<AreaAttStatisticalDTO>>().ok(page);
    }


}
