package io.renren.modules.enterprise.excelexp.service;

import com.google.gson.JsonObject;
import io.renren.common.page.PageData;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 三表导出
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-05-18
 */
public interface ExcelExpService  {
    /**
     * 三表导出 页面分页数据
     **/
    PageData<JsonObject> getPageList(Map<String, Object> params);

    /**
     *三表导出
     **/

    void excelExp(Map<String, Object> params, HttpServletResponse response) throws Exception ;


}