package io.renren.modules.enterprise.pj01info.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj01info.dto.*;
import io.renren.modules.enterprise.pj01info.entity.Pj01InfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Mapper
public interface Pj01InfoDao extends BaseDao<Pj01InfoEntity> {
    /**
     * 列表分页查询数据
     * @param params
     * @return
     */
    List<Pj01InfoDTO> getListData(Map<String, Object> params);
    /**
     * 项目查询获取参建单位
     * @param params
     * @return
     */
    List<Cp02PageDTO> getCp02PageList(@Param("page") Page<Cp02PageDTO> page, @Param("params") Map<String, Object> params);
    /**
     * 项目查询获取班组
     * @param params
     * @return
     */
    List<Tm01PageDTO> getTeamPageList(@Param("page") Page<Tm01PageDTO> page, @Param("params") Map<String, Object> params);
    /**
     * 项目查询获取工人
     * @param params
     * @return
     */
    List<Ps02PageDTO> getWorkerPageList(@Param("page") Page<Ps02PageDTO> page, @Param("params") Map<String, Object> params);
    /**
     * 项目查询获取考勤
     * @param params
     * @return
     */
    List<Kq02PageDTO> getAttendancePageList(@Param("page") Page<Kq02PageDTO> page, @Param("params") Map<String, Object> params);
    /**
     * 项目查询获取管理人员
     * @param params
     * @return
     */
    List<Ps04PageDTO> getManagerPageList(@Param("page") Page<Ps04PageDTO> page, @Param("params") Map<String, Object> params);
}