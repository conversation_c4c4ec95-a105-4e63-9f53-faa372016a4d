package io.renren.modules.supervision.projectStatistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import io.renren.common.page.PageData;
import io.renren.modules.supervision.projectStatistic.dto.ProjectStatDTO;
import io.renren.modules.supervision.projectStatistic.dto.ProjectStatPageDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-12-28 13:00
 */
public interface ProjectStatService extends IService<ProjectStatDTO> {
    /**
     * 项目统计查询
     *
     * @param params Map<String, Object> params
     * @return List<ProjectStatDTO>
     */
    List<ProjectStatDTO> getList(Map<String, Object> params);

    /**
     * 查询项目信息分页列表
     * @param params Map<String, Object> params
     * @return PageData<ProjectStatPageDTO>
     */
    PageData<ProjectStatPageDTO> getPageList(Map<String, Object> params);
}
