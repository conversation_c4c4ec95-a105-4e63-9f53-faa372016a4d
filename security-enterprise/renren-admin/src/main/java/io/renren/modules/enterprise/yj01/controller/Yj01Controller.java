package io.renren.modules.enterprise.yj01.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.yj01.dto.ProjectKeyPositInfo;
import io.renren.modules.enterprise.yj01.service.Yj01Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 预警信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-07-01
 */
@RestController
@RequestMapping("enterprise/yj01")
@Api(tags = "预警信息")
public class Yj01Controller {
    @Autowired
    private Yj01Service yj01Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int")
    })
    @RequiresPermissions("enterprise:yj01:page")
    public Result<PageData<ProjectKeyPositInfo>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<ProjectKeyPositInfo> page = yj01Service.pageList(params);
        return new Result<PageData<ProjectKeyPositInfo>>().ok(page);
    }

    @GetMapping("getInfoContent")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:yj01:getInfoContent")
    public Result<String> getInfoContent() {
        String data = yj01Service.getWarningInfo();
        return new Result<String>().ok(data);
    }

    @GetMapping("saveWarningInfo")
    @ApiOperation("生成预警信息")
    public void saveWarningInfo() {
        yj01Service.saveWarningInfo();
    }

}