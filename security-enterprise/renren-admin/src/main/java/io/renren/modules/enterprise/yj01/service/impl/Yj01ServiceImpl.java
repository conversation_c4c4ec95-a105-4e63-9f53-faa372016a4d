package io.renren.modules.enterprise.yj01.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.yj01.dao.Yj01Dao;
import io.renren.modules.enterprise.yj01.dto.ManagerDTO;
import io.renren.modules.enterprise.yj01.dto.ProjectKeyPositInfo;
import io.renren.modules.enterprise.yj01.dto.Yj01DTO;
import io.renren.modules.enterprise.yj01.entity.Yj01Entity;
import io.renren.modules.enterprise.yj01.service.Yj01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/31 11:38
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Yj01ServiceImpl extends CrudServiceImpl<Yj01Dao, Yj01Entity, Yj01DTO> implements Yj01Service {
    @Autowired
    private Yj01Dao yj01Dao;

    /**
     * 总包单位
     */
    private static final String GENERAL_CONTRACTOR = "9";
    /**
     * 总包单位项目经理
     */
    private static final String PROJECT_MANAGER = "1009";
    /**
     * 监理单位
     */
    private static final String SUPERVISION_UNIT = "7";
    /**
     * 监理单位总监理工程师
     */
    private static final String GENERAL_MANAGER = "1001";
    /**
     * 有效考勤时间
     */
    private static final BigDecimal EFFECTIVE_TIME = new BigDecimal("5.00");
    /**
     * 预警内容信息
     */
    private static final String WAR_CONTENT = "<p><strong style=\"color: red;\">预警解释说明:</strong></p><p><strong style=\"color: red;\">①&nbsp;</strong><span style=\"color: red;\">预警信息每天凌晨02:00统计前一天的数据;</span></p><p><strong style=\"color: red;\">②&nbsp;</strong><span style=\"color: red;\">有效考勤是第一次考勤时间和最后一次考勤时间的间隔大于5个小时;</span></p><p><strong style=\"color: red;\">③&nbsp;</strong><span style=\"color: red;\">有效的项目经理和总监理工程师必须是有证人员，而且该证书是在参建企业下边，证书信息在“四库一平台”可查询,查询地址:</span><a href=\"http://jzsc.mohurd.gov.cn/\"  target=\"_blank\" style=\"color: black;\">http://jzsc.mohurd.gov.cn/</a></p><p><strong>预警内容：</strong></p>";

    @Override
    public QueryWrapper<Yj01Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<Yj01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public void saveWarningInfo() {
        //删除数据重新生成预警信息
        QueryWrapper<Yj01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq("type", "1");
        yj01Dao.delete(wrapper);
        //查询系统的在建项目信息
        List<Long> pj0101 = yj01Dao.selectProjectInfo();
        for (Long id : pj0101) {
            //开始计算预警信息
            StringBuilder stringBuilder = new StringBuilder();
            //查询项目经理
            ManagerDTO projectManager = yj01Dao.selectManager(id, GENERAL_CONTRACTOR, PROJECT_MANAGER);
            //查询总监理工程师
            ManagerDTO generalManager = yj01Dao.selectManager(id, SUPERVISION_UNIT, GENERAL_MANAGER);
            //先判断项目经理或者总监理工程师是否存在
            if (projectManager.getManagerNum() == 0 || generalManager.getManagerNum() ==0) {
                stringBuilder.append("<p><strong style=\"color: red;\">*</strong><strong>该项目不存在项目经理或者总监理工程师，请在系统录入有效的项目经理或者总监理工程师；</strong></p");
            }
            //如果项目经理存在则判断数量和考勤信息
            if (BeanUtil.isNotEmpty(projectManager)) {
                //判断项目经理的数量
                if (projectManager.getManagerNum() > 1) {
                    stringBuilder.append("<p><strong style=\"color: red;\">*</strong><strong>总包单位项目经理存在").append(projectManager.getManagerNum()).append("个,请退场无效的项目经理,只保留一个有效的项目经理信息;</strong></p>");
                }
                if (projectManager.getManagerNum() > 0) {
                    //判断项目经理的考勤是否有效
                    String ids = projectManager.getManagerId();
                    String[] userId = ids.split(",");
                    for (String ps0401 : userId) {
                        BigDecimal hours = new BigDecimal(yj01Dao.selectByWorkHoursById(ps0401));
                        if (hours.compareTo(EFFECTIVE_TIME) < 0) {
                            stringBuilder.append("<p><strong style=\"color: red;\">*</strong><strong>总包单位项目经理存在无效考勤;</strong></p>");
                            break;
                        }

                    }
                }

            }
            //如果总监理工程师存在则判断数量和考勤信息
            if (BeanUtil.isNotEmpty(generalManager)) {
                //判断总监理工程师的数量
                if (generalManager.getManagerNum() > 1) {
                    stringBuilder.append("<p><strong style=\"color: red;\">*</strong><strong>监理单位总监理工程师存在").append(generalManager.getManagerNum()).append("个,请退场无效的总监理工程师，只保留一个有效的总监理工程师;</strong></p>");
                }
                if (generalManager.getManagerNum() > 0) {
                    //判断总监理工程师的考勤是否有效
                    String generalManagerIds = generalManager.getManagerId();
                    String[] generalManagerId = generalManagerIds.split(",");
                    for (String ps0401 : generalManagerId) {
                        BigDecimal hours = new BigDecimal(yj01Dao.selectByWorkHoursById(ps0401));
                        if (hours.compareTo(EFFECTIVE_TIME) < 0) {
                            stringBuilder.append("<p><strong style=\"color: red;\">*</strong><strong>监理单位总监理工程师存在无效考勤。</strong></p>");
                            break;
                        }
                    }
                }

            }
            //如果预警内容不为空，则存在预警信息
            if (StringUtils.isNotBlank(stringBuilder)) {
                Yj01Entity entity = new Yj01Entity();
                entity.setPj0101(id);
                entity.setContent(WAR_CONTENT + stringBuilder.toString());
                entity.setType("1");
                yj01Dao.insert(entity);
            }
        }
    }

    @Override
    public PageData<ProjectKeyPositInfo> pageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Yj01Entity> page = getPage(params, "", false);
        List<ProjectKeyPositInfo> list = baseDao.getList(params);
        return getPageData(list, page.getTotal(), ProjectKeyPositInfo.class);
    }

    @Override
    public String getWarningInfo() {
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        return yj01Dao.selectWarningContent(pj0101);
    }

}
