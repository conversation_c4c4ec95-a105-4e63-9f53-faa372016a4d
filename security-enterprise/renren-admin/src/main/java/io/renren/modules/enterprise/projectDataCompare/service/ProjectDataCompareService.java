package io.renren.modules.enterprise.projectDataCompare.service;

import io.renren.common.page.PageData;
import io.renren.common.service.BaseService;
import io.renren.modules.enterprise.projectDataCompare.dto.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-12-08 13:32
 */

public interface ProjectDataCompareService extends BaseService<ProjectDataCompareDTO> {
    /**
     * 列表数据查询
     *
     * @param params Map<String, Object>
     * @return List
     */
    PageData<ProjectDataCompareDTO> page(Map<String, Object> params);

    /**
     * 查询参建单位差异数据
     *
     * @param pj0101 项目ID
     * @return List
     */
    List<PartUnitDTO> partUnitList(String pj0101);

    /**
     * 查询班组差异数据
     *
     * @param pj0101 项目ID
     * @return List
     */
    List<TeamInfoDTO> teamInfoList(String pj0101);

    /**
     * 工人差异数据
     * @param pj0101 项目ID
     * @return List
     */
    List<WorkerInfoDTO> workerInfoList(String pj0101);

    /**
     * 管理人员查询数据
     * @param pj0101 项目ID
     * @return List
     */
    List<ManagerInfoDTO> managerInfoList(String pj0101);
}
