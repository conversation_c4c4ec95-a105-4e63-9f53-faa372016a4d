package io.renren.modules.enterprise.message.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.modules.enterprise.message.dto.MessagePage;
import io.renren.modules.enterprise.message.service.MessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.formula.functions.T;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/6 8:48
 */
@RestController
@RequestMapping("message/push")
@Api(tags = "消息推送")
public class MessageController {
    @Autowired
    private MessageService messageService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "projectName", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "areaCode", value = "所属区域", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "projectStatus", value = "项目状态", example = "默认设置为在建", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("message:push:page")
    public Result<PageData<MessagePage>> page(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<MessagePage> page = messageService.pageList(params);

        return new Result<PageData<MessagePage>>().ok(page);
    }

    @PostMapping("batch")
    @ApiOperation("消息推送")
    @LogOperation("消息推送")
    @RequiresPermissions("message:push:batch")
    public Result<T> batch(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        messageService.batchPush(Arrays.asList(ids));

        return new Result<>();
    }
}
