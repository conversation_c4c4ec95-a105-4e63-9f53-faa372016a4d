package io.renren.modules.enterprise.ps02.strategy.handler;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.renren.common.constant.enums.DeviceTypeEnums;
import io.renren.modules.enterprise.kq01.dto.Kq01DTO;
import io.renren.modules.enterprise.kq01.entity.Kq01Entity;
import io.renren.modules.enterprise.kq05.dto.PersonDTO;
import io.renren.modules.enterprise.kq05.entity.Kq05Entity;
import io.renren.modules.enterprise.ps02.strategy.AbstractPs02IssuePersonHandleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 第三方设备（宜宾对接模式）
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2025/6/17 9:44
 **/
@Slf4j
@Component
public class ThirdProcessHandler extends AbstractPs02IssuePersonHandleStrategy {

    /**
     * 删除人员
     */
    private String PERSON_DELETE = "1";
    /**
     * 新增人员
     */
    private String PERSON_ADD = "2";

    /**
     * 人员下发，设备类型
     *
     * @return
     */
    @Override
    public DeviceTypeEnums businessType() {
        return DeviceTypeEnums.THIRD_PARTY;
    }

    /**
     * 销权
     *
     * @param pj0101     项目Id
     * @param userId     人员Id
     * @param personType 人员类型
     */
    @Override
    public void personSellRight(Long pj0101, Long userId, String personType,
                                Kq01DTO kq01DTO) {
        Kq05Entity kq05Entity = new Kq05Entity();
        kq05Entity.setTaskType(PERSON_DELETE);
        kq05Entity.setUserId(userId);
        kq05Entity.setPersonType(personType);
        kq05Entity.setUserId(userId);
        kq05Entity.setTerminaltype(this.businessType().getType());
//        kq05Entity.setTerminaltype(DeviceTypeEnums.THIRD_PARTY.getType());
        kq05Entity.setDeviceserialno(kq01DTO.getDeviceserialno());
        kq05Entity.setCmdCode("REMOVE_PERSON");
        kq05Dao.insert(kq05Entity);
    }

    /**
     * 下发人员数据
     */
    @Override
    public void issuedDeviceAddPerson(Long userId, Long pj0101, String personType, String terminaltype, String cmdCode, String deviceserialno) {
        Kq05Entity kq05DeviceDTO = new Kq05Entity();
        kq05DeviceDTO.setUserId(userId);
        kq05DeviceDTO.setPersonType(personType);
        kq05DeviceDTO.setTerminaltype(terminaltype);
        kq05DeviceDTO.setDeviceserialno(deviceserialno);
        kq05DeviceDTO.setCmdCode("DEVICE_ADD_PERSON");
        kq05DeviceDTO.setTaskType(PERSON_ADD);
        kq05Dao.insert(kq05DeviceDTO);
    }

    /**
     * 下发人员（重写）
     *
     * @param pj0101         项目id
     * @param userId         人员Id
     * @param personType     人员类型
     * @param terminaltype   设备类型
     * @param deviceserialno 设备序列号
     */
    public void personSellRight(Long pj0101,
                                Long userId,
                                String personType,
                                String terminaltype,
                                String deviceserialno) {
        Kq05Entity kq05Entity = new Kq05Entity();
        kq05Entity.setUserId(userId);
        kq05Entity.setTaskType(PERSON_DELETE);
        kq05Entity.setDeviceserialno(deviceserialno);
        kq05Entity.setCmdCode("REMOVE_PERSON");
        kq05Entity.setTerminaltype(terminaltype);
        kq05Entity.setPersonType(personType);
        kq05Dao.insert(kq05Entity);
    }

    /**
     * 下发人员（工人）
     */
    @Override
    public void issuedWorkerPersonnel(List<Long> userIds, String personType) {

        List<PersonDTO> workerList = getWorkerList(userIds);
        if (CollUtil.isEmpty(workerList)) {
            return;
        }

        workerList.forEach(personDTO -> {
            Kq05Entity kq05Entity = new Kq05Entity();
            kq05Entity.setTaskType(PERSON_ADD);
            kq05Entity.setParams(personDTO.getImageUrl());
            kq05Entity.setPersonType(personType);
            kq05Entity.setUserId(personDTO.getUserId());
            kq05Entity.setTerminaltype(this.businessType().getType());
//            kq05Entity.setTerminaltype(DeviceTypeEnums.THIRD_PARTY.getType());
            kq05Entity.setDeviceserialno(personDTO.getDevicemotion());
            kq05Entity.setCmdCode("WORKER DEVICE_ADD_PERSON");
            kq05Dao.insert(kq05Entity);
        });
    }

    /**
     * 下发人员
     *
     * @param pj0101     项目id
     * @param userId     人员Id
     * @param name       姓名
     * @param imageUrl   人员图片
     * @param personType 人员类型
     */
    @Override
    public void issuedWorkerPersonnel(Long pj0101, Long userId, String name, String imageUrl, String personType, String deviceType) {
        log.info("ThirdProcessHandler issuedWorkerPersonnel");
//        List<Kq01DTO> kq01DTOList = getKq01DTOList(pj0101);
        List<Kq01Entity> kq01Entities = kq01Dao.selectList(new LambdaQueryWrapper<Kq01Entity>()
                .eq(Kq01Entity::getPj0101, pj0101)
                .eq(Kq01Entity::getWhether, "1")
        );
        if (CollUtil.isEmpty(kq01Entities)) {
            log.info("ThirdProcessHandler issuedWorkerPersonnel fail, kq01Entities result is empty");
            return;
        }
        for (Kq01Entity kq01Entity : kq01Entities) {
            Kq05Entity kq05Entity = new Kq05Entity();
            kq05Entity.setTaskType(PERSON_ADD);
            kq05Entity.setUserId(userId);
            kq05Entity.setParams(imageUrl);
            kq05Entity.setPersonType(personType);
            kq05Entity.setTerminaltype(this.businessType().getType());
//            kq05Entity.setTerminaltype(DeviceTypeEnums.THIRD_PARTY.getType());
            kq05Entity.setDeviceserialno(kq01Entity.getDeviceserialno());
            kq05Entity.setCmdCode("MANAGER DEVICE_ADD_PERSON");
            kq05Dao.insert(kq05Entity);
        }
    }

    /**
     * 下发人员（管理人员）
     *
     * @param userIds
     * @param personType
     */
    @Override
    public void issuedManagerPersonnel(List<Long> userIds, String personType) {
        List<PersonDTO> managerList = getManagerList(userIds);
        if (CollUtil.isEmpty(managerList)) {
            return;
        }
        managerList.forEach(personDTO -> {
            Kq05Entity kq05Entity = new Kq05Entity();
            kq05Entity.setTaskType(PERSON_ADD);
            kq05Entity.setUserId(personDTO.getUserId());
            kq05Entity.setParams(personDTO.getImageUrl());
            kq05Entity.setPersonType(personType);
//            kq05Entity.setTerminaltype(this.businessType().getType());
            kq05Entity.setTerminaltype(DeviceTypeEnums.THIRD_PARTY.getType());
            kq05Entity.setDeviceserialno(personDTO.getDevicemotion());
            kq05Entity.setCmdCode("MANAGER DEVICE_ADD_PERSON");
            kq05Dao.insert(kq05Entity);
        });
    }
}
