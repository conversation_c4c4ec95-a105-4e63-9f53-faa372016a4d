package io.renren.modules.enterprise.ps04.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "项目管理人员信息")
public class Ps04DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0401;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "企业ID")
    private Long cp0201;

    @ApiModelProperty(value = "人员ID")
    private Long ps0101;

    @ApiModelProperty(value = "岗位类型", required = true)
    @NotBlank(message = "岗位类型不能为空")
    private String jobtype;

    @ApiModelProperty(value = "管理类型")
    private String managetype;

    @ApiModelProperty(value = "头像采集照片", required = true)
    @NotBlank(message = "人员照片不能为空")
    private String photo;

    @ApiModelProperty(value = "是否购买工伤或意外伤害保险", required = true)
    @NotBlank(message = "是否购买工伤或意外伤害保险不能为空")
    private String hasbuyinsurance;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;

    @ApiModelProperty(value = "退场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exittime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

    @ApiModelProperty(value = "人员基础信息")
    @Valid
    private Ps01DTO ps01DTO;

    @ApiModelProperty(value = "证书信息")
    @Valid
    private List<Ot01DTO> ot01DTOList;
}