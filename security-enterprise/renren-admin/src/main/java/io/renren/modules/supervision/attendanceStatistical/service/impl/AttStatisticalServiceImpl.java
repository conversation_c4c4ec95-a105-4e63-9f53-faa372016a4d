package io.renren.modules.supervision.attendanceStatistical.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.admin.sys.dao.SysRegionDao;
import io.renren.modules.supervision.attendanceStatistical.dao.AttStatisticalDao;
import io.renren.modules.supervision.attendanceStatistical.dto.AreaAttStatisticalDTO;
import io.renren.modules.supervision.attendanceStatistical.dto.AttStatisticalDTO;
import io.renren.modules.supervision.attendanceStatistical.entity.AttStatisticalEntity;
import io.renren.modules.supervision.attendanceStatistical.service.AttStatisticalService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 考勤统计页面
 *
 * <AUTHOR>
 * @since 1.0.0 2021-04-26
 */
@Service
public class AttStatisticalServiceImpl extends CrudServiceImpl<AttStatisticalDao, AttStatisticalEntity, AttStatisticalDTO> implements AttStatisticalService {

    @Autowired
    private AttStatisticalDao attStatisticalDao;

    @Autowired
    private SysRegionDao sysRegionDao;

    @Override
    public QueryWrapper<AttStatisticalEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<AttStatisticalEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<AttStatisticalDTO> getPageData(Map<String, Object> params) {
        params.put("areaCode",CommonUtils.coverAreaCode((String) params.get("areaCode")));
        params.put("deptId", SecurityUser.getDeptId());
        //转换模糊搜索查询条件  checkDate
        paramsToLike(params,"projectName");
        //根据总考勤率排序
        IPage<AttStatisticalEntity> page = getPage(params,"",false);
        //获取统计数据
        if(params.get("startDate") != null && params.get("endDate") != null){
            if(DateUtil.parse(params.get("startDate").toString()).isAfter(DateUtil.parse(params.get("endDate").toString()))){
                throw new RenException("开始时间不能大于结束时间！");
            }
        }
        List<AttStatisticalDTO> pageData = attStatisticalDao.getPageData(params);
        return getPageData(pageData, page.getTotal(), AttStatisticalDTO.class);
    }

    @Override
    public List<AreaAttStatisticalDTO> getAreaCodePageData(Map<String, Object> params) {
        //获取当前查询地区的层级并添加到查询条件中
        params.put("level",sysRegionDao.getTreeLevelByValue((String) params.get("areaCode")));
        return attStatisticalDao.getAreaCodePageData(params);
    }
}