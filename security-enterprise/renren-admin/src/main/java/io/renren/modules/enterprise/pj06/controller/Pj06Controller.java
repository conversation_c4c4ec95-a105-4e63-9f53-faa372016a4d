package io.renren.modules.enterprise.pj06.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.annotation.cachelockvalidator.CacheLock;
import io.renren.common.common.dto.CommonDto;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.modules.enterprise.pj06.dto.Pj06DTO;
import io.renren.modules.enterprise.pj06.service.Pj06Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.formula.functions.T;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;


/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@RestController
@RequestMapping("enterprise/pj06")
@Api(tags = "项目注册信息")
public class Pj06Controller {
    @Autowired
    private Pj06Service pj06Service;


    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:pj06:page")
    public Result<PageData<Pj06DTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Pj06DTO> page = pj06Service.getPageData(params);
        return new Result<PageData<Pj06DTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:pj06:info")
    public Result<Pj06DTO> get(@PathVariable("id") Long id) {
        Pj06DTO data = pj06Service.getInfo(id);
        return new Result<Pj06DTO>().ok(data);
    }

    @PostMapping("register")
    @ApiOperation("注册")
    @LogOperation("注册")
    @CacheLock(prefix = "pj06")
    public Result save(@RequestBody Pj06DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        pj06Service.saveProjectInfo(dto);
        return new Result();
    }

    @GetMapping("getRoleList")
    @ApiOperation("获取角色")
    @LogOperation("获取角色")
    public Result getRoleLists() {
        List<CommonDto> roleList = pj06Service.getRoleList();
        return new Result().ok(roleList);
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:pj06:delete")
    public Result<T> delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        pj06Service.delete(ids);

        return new Result<>();
    }

    @PostMapping("checkProjectInfo")
    @ApiOperation("审核")
    @LogOperation("审核")
    @RequiresPermissions("enterprise:pj06:checkProjectInfo")
    public Result<T> checkProjectInfo(@RequestBody Pj06DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);
        pj06Service.saveCheckProjectInfo(dto);
        return new Result<>();
    }
}