package io.renren.modules.supervision.personneldataStatistical.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.supervision.personneldataStatistical.dto.PersonnelDataStatisticalDTO;
import io.renren.modules.supervision.personneldataStatistical.entity.PersonnelDataStatisticalEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @description:人员数据统计分析
 * @author: jinghai
 * @date: 2022/3/8 13:42
 **/
@Mapper
public interface PersonnelDataStatisticalDao extends BaseDao<PersonnelDataStatisticalEntity> {

    List<PersonnelDataStatisticalDTO> getWorkType(Map<String, Object> params);

    List<PersonnelDataStatisticalDTO> getNativeData(Map<String, Object> params);

    List<PersonnelDataStatisticalDTO> getGenderData(Map<String, Object> params);

    List<PersonnelDataStatisticalDTO> getAgeData(Map<String, Object> params);

    double workTypeCount(Map<String, Object> params);

    double allNativeData(Map<String, Object> params);

    double allGenderData(Map<String, Object> params);

    double allAgeData(Map<String, Object> params);
}
