package io.renren.modules.enterprise.ps03.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 建筑工人合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
public class Ps03Excel {
    @Excel(name = "主键ID")
    private BigDecimal ps0301;
    @Excel(name = "建筑工人ID")
    private BigDecimal ps0201;
    @Excel(name = "合同编号")
    private String contractcode;
    @Excel(name = "合同期限类型")
    private String contractperiodtype;
    @Excel(name = "签订日期")
    private Date signdate;
    @Excel(name = "开始日期")
    private Date startdate;
    @Excel(name = "结束时期")
    private Date enddate;
    @Excel(name = "计量单位")
    private String unit;
    @Excel(name = "计量单价")
    private BigDecimal unitprice;
    @Excel(name = "备注")
    private String memo;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}