package io.renren.modules.supervision.attendanceStatistical.dao;

import io.renren.common.dao.BaseDao;

import io.renren.modules.supervision.attendanceStatistical.dto.AttendancePersonDTO;
import io.renren.modules.supervision.attendanceStatistical.entity.AttendancePersonEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @description: 考勤人员详情页
 * @className: AttendancePersonDao
 * @author: jh
 * @date: 2022-3-1
 **/
@Mapper
public interface AttendancePersonDao extends BaseDao<AttendancePersonEntity> {

    List<AttendancePersonDTO> getPageDataByIsOut(Map<String, Object> params);

    List<AttendancePersonDTO> getPageDataByIsCheck(Map<String, Object> params);

    List<AttendancePersonDTO> getManagePageDataByIsOut(Map<String, Object> params);

    List<AttendancePersonDTO> getManagePageDataByIsCheck(Map<String, Object> params);

    List<String> getKqByMonth(Map<String, Object> params);
}
