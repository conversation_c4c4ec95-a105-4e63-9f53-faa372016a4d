package io.renren.modules.enterprise.person.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.person.dto.PersonTempDTO;
import io.renren.modules.enterprise.person.entity.PersonTempEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 门禁临时人员
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-12
 */
@Mapper
public interface PersonTempDao extends BaseDao<PersonTempEntity> {

    /**
     * 更新状态
     *
     * @param ids id
     * @param state 状态
     */
    void updateStateByIds(@Param("list") List<Long> ids, @Param("state") String state);

    /**
     * 分页查询
     * @param params Map<String, Object>
     * @return List<PersonTempDTO>
     */
    List<PersonTempDTO> getListData(Map<String, Object> params);

    /**
     * 查询数据
     *
     * @param ids List<Long>
     * @return List<PersonTempDTO>
     */
    List<PersonTempDTO> selectBatchByIds(@Param("ids") List<Long> ids);
}