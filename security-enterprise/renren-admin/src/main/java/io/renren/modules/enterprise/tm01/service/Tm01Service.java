package io.renren.modules.enterprise.tm01.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.tm01.dto.Tm01DTO;
import io.renren.modules.enterprise.tm01.dto.Tm01PageDTO;
import io.renren.modules.enterprise.tm01.entity.Tm01Entity;

import java.util.Map;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
public interface Tm01Service extends CrudService<Tm01Entity, Tm01DTO> {

    /**
     * 校验当前项目下，班组名称是否重复
     *
     * @param teamName 班组名称
     * @param pj0101   项目ID
     */
    void checkTeamName(String teamName, Long pj0101);

    /**
     * 分页查询
     * @param params
     * @return
     */
    PageData<Tm01PageDTO> pageList(Map<String, Object> params);

    /**
     * 保存班组信息
     * @param dto
     */
    void saveTeamInfo(Tm01DTO dto);

    /**
     * 班组退场
     * @param ids 班组ID
     */
    void exitTeam(Long[] ids);

    /**
     * 班组进场
     * @param ids 班组ID
     */
    void entryTeam(Long[] ids);
}