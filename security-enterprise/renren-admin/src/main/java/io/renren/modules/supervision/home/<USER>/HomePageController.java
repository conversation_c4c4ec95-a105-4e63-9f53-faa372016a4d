package io.renren.modules.supervision.home.controller;

import io.renren.common.utils.Result;
import io.renren.modules.supervision.home.dto.StatisticsAttendance;
import io.renren.modules.supervision.home.service.HomePageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/*
 * @Author(作者) 王崇人
 * @Description(类的描述): 监管PC端首页控制器
 *：@version（开发的版本）：1.0
 * @Date(创建时间) 2022-7-12 14:35:37
 */
@RestController
@RequestMapping("supervision/home")
@Api(tags = "监管首页")
public class HomePageController {

    @Autowired
    private HomePageService homeService;

    @GetMapping("getCurrentArea")
    @ApiOperation("获取当前用户所属区域")
    private Result getCurrentArea(){
    String currentArea = homeService.getCurrentArea();
    return new Result().ok(currentArea);
    }

    @GetMapping("getProjectSum")
    @ApiOperation("获取当前登录用户所属行政区划下的项目总数")
    private Result getProjectSum(){
        Long projectSum = homeService.getProjectSum();
        return new Result().ok(projectSum);
    }
    @GetMapping("getConstructionProjects")
    @ApiOperation("获取当前登录用户所属行政区划下的在建项目总数")
    private Result getConstructionProjects(){
        Long constructionProjects = homeService.getConstructionProjects();
        return new Result().ok(constructionProjects);
    }
    @GetMapping("getManagerSum")
    @ApiOperation("获取当前登录用户所属行政区划下的项目管理人员总数")
    private Result getManagerSum(){
        Long managerSum = homeService.getManagerSum();
        return new Result().ok(managerSum);
    }

    @GetMapping("getWorkerSum")
    @ApiOperation("获取当前登录用户所属行政区划下的建筑工人总数")
    private Result getWorkerSum(){
        Long workerSum = homeService.getWorkerSum();
        return new Result().ok(workerSum);
    }

    @GetMapping("getPersonAttendance")
    @ApiOperation("获取当前登录用户所属行政区划下的今日考勤")
    private Result getPersonAttendance(){
        Long personAttendance = homeService.getPersonAttendance();
        return new Result().ok(personAttendance);
    }

    @GetMapping("statisticsAttendance")
    @ApiOperation("获取当前登录用户所在行政区划下的最近七天考勤统计")
    private Result<List<StatisticsAttendance>> statisticsAttendance(){
        return new Result().ok(homeService.statisticsAttendance());
    }

}
