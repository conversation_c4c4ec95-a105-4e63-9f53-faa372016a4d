package io.renren.modules.enterprise.pj01report.service;


import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.pj01report.dto.Pj01ReportDTO;
import io.renren.modules.enterprise.pj01report.entity.Pj01ReportEntity;

import java.util.Map;

/**
 * 项目上报配置表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-12
 */
public interface Pj01ReportService extends CrudService<Pj01ReportEntity, Pj01ReportDTO> {


    /**
     * 查询列表数据
     *
     * @param params
     * @return
     */
    PageData<Pj01ReportDTO> selectListData(Map<String, Object> params);


    /**
     * 根据项目id获取配置
     *
     * @param pj0101
     * @return
     */
    Pj01ReportDTO getData(Long pj0101);

    /**
     * 修改
     *
     * @param dto
     * @return
     */
    void updateData(Pj01ReportDTO dto);

}