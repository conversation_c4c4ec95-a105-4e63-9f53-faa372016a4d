package io.renren.modules.enterprise.person.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.kq05.dto.PersonDTO;
import io.renren.modules.enterprise.kq05.service.Kq05Service;
import io.renren.modules.enterprise.person.dao.PersonTempDao;
import io.renren.modules.enterprise.person.dto.PersonTempDTO;
import io.renren.modules.enterprise.person.entity.PersonTempEntity;
import io.renren.modules.enterprise.person.service.PersonTempService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 门禁临时人员
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-12
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PersonTempServiceImpl extends CrudServiceImpl<PersonTempDao, PersonTempEntity, PersonTempDTO> implements PersonTempService {
    @Resource
    private Kq05Service kq05Service;
    /**
     * 人员类型
     */
    private final static String PERSON_TYPE = "3";

    @Override
    public QueryWrapper<PersonTempEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<PersonTempEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }


    @Override
    public void saveInfo(PersonTempDTO dto) {
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        PersonTempEntity personTempEntity = ConvertUtils.sourceToTarget(dto, PersonTempEntity.class);
        personTempEntity.setPj0101(pj0101);
        personTempEntity.setWhether("1");
        //保存数据
        baseDao.insert(personTempEntity);
        //人员注册、下发到设备
        kq05Service.saveCreatePerson(pj0101, personTempEntity.getId(), dto.getName(), dto.getHeadImageUrl(), PERSON_TYPE);
    }

    @Override
    public void updateInfo(PersonTempDTO dto) {
        PersonTempEntity entity = baseDao.selectById(dto.getId());
        PersonTempEntity personTempEntity = ConvertUtils.sourceToTarget(dto, PersonTempEntity.class);
        baseDao.updateById(personTempEntity);
        //判断头像是否发生变化
        if (!personTempEntity.getHeadImageUrl().equals(entity.getHeadImageUrl())) {
            kq05Service.updatePersonImage(dto.getPj0101(), dto.getId(), dto.getHeadImageUrl(), PERSON_TYPE);
        }
    }

    @Override
    public void deleteInfo(List<PersonTempDTO> list) {
        List<Long> longs = list.stream().map(PersonTempDTO::getId).collect(Collectors.toList());
        baseDao.updateStateByIds(longs, "0");
        for (PersonTempDTO personTempDTO : list) {
            Long id = personTempDTO.getId();
            Long pj0101 = personTempDTO.getPj0101();
            //人员退场进行销权操作
            kq05Service.personSellRight(pj0101, id, PERSON_TYPE);
        }
    }

    @Override
    public PageData<PersonTempDTO> pageInfo(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<PersonTempEntity> page = getPage(params, "", false);
        List<PersonTempDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), PersonTempDTO.class);
    }

    @Override
    public void deviceAddPerson(Long[] ids) {
        List<Long> longs = Arrays.asList(ids);
        List<PersonTempDTO> person = baseDao.selectBatchByIds(longs);
        for (PersonTempDTO personTempDTO : person) {
            //人员授权到设备
            kq05Service.saveCreatePerson(personTempDTO.getPj0101(), personTempDTO.getId(), personTempDTO.getName(), personTempDTO.getHeadImageUrl(), PERSON_TYPE);
        }
    }
}