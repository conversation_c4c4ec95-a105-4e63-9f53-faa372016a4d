package io.renren.modules.enterprise.dataShare.facilitator.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.ConvertUtils;
import io.renren.modules.enterprise.dataShare.facilitator.dao.FacilitatorDao;
import io.renren.modules.enterprise.dataShare.facilitator.dto.FacilitatorDTO;
import io.renren.modules.enterprise.dataShare.facilitator.entity.FacilitatorEntity;
import io.renren.modules.enterprise.dataShare.facilitator.service.FacilitatorService;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dao.FacilitatorProjectDao;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dto.FacilitatorProjectDTO;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dto.FacilitatorProjectInfo;
import io.renren.modules.enterprise.dataShare.facilitatorProject.entity.FacilitatorProjectEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 数据共享密钥信息
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class FacilitatorServiceImpl extends CrudServiceImpl<FacilitatorDao, FacilitatorEntity, FacilitatorDTO> implements FacilitatorService {
    @Resource
    private FacilitatorProjectDao facilitatorProjectDao;

    @Override
    public QueryWrapper<FacilitatorEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<FacilitatorEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }


    @Override
    public void saveInfo(FacilitatorDTO dto) {
        //判断是否存在企业
        Integer count = baseDao.selectCountByCode(dto.getCode());
        if (count > 0) {
            throw new RenException("企业已存在,请勿重复录入");
        }
        //生成密钥
        dto.setAppSecret(IdUtil.simpleUUID());
        dto.setAppKey(IdUtil.simpleUUID());
        FacilitatorEntity entity = ConvertUtils.sourceToTarget(dto, FacilitatorEntity.class);
        baseDao.insert(entity);
    }

    @Override
    public PageData<FacilitatorDTO> pageInfo(Map<String, Object> params) {
        IPage<FacilitatorEntity> page = getPage(params, "ID", false);
        List<FacilitatorDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), FacilitatorDTO.class);
    }

    @Override
    public void updateWhether(Long[] ids, int state) {
        baseDao.updateWhether(Arrays.asList(ids), state);
    }

    @Override
    public void relatedProject(FacilitatorProjectDTO dto) {
        //操作频率不高,直接删除之后,重新写入
        facilitatorProjectDao.deleteByFacilitatorId(dto.getFacilitatorId());
        List<FacilitatorProjectInfo> list = dto.getList();
        for (FacilitatorProjectInfo facilitatorProjectInfo : list) {
            FacilitatorProjectEntity facilitatorProjectEntity = new FacilitatorProjectEntity();
            facilitatorProjectEntity.setPj0101(facilitatorProjectInfo.getPj0101());
            facilitatorProjectEntity.setFacilitatorId(dto.getFacilitatorId());
            facilitatorProjectEntity.setWhether("1");
            facilitatorProjectDao.insert(facilitatorProjectEntity);
        }
    }
}