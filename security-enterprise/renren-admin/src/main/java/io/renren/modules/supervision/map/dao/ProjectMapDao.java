package io.renren.modules.supervision.map.dao;

import io.renren.modules.supervision.map.dto.ProjectMapDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/20 9:03
 */
@Mapper
public interface ProjectMapDao {
    /**
     * 查询项目地图信息
     * @param params Map<String, Object> params
     * @return List<ProjectMapDTO>
     */
    List<ProjectMapDTO> selectProjectInfo(Map<String, Object> params);
}
