package io.renren.modules.enterprise.pj01info.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 建筑工人考勤信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "考勤信息分页")
public class Kq02PageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long kq0201;

    @ApiModelProperty(value = "人员ID")
    private Long userId;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "人员姓名")
    private String personName;

    @ApiModelProperty(value = "人员类型")
    private String personType;

    @ApiModelProperty(value = "进出方向")
    private String direction;

    @ApiModelProperty(value = "通行方式")
    private String attendType;

    @ApiModelProperty(value = "考勤时间")
    private Date checkDate;

    @ApiModelProperty(value = "刷卡近照")
    private String imageUrl;


}