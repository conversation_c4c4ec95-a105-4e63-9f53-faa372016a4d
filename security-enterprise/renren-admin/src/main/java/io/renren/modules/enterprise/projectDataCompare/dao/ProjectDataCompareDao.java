package io.renren.modules.enterprise.projectDataCompare.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.projectDataCompare.dto.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-12-08 13:34
 */
@Mapper
public interface ProjectDataCompareDao extends BaseDao<ProjectDataCompareDTO> {
    /**
     * 列表查询数据
     *
     * @param params Map<String, Object>
     * @return List
     */
    List<ProjectDataCompareDTO> getListData(Map<String, Object> params);

    /**
     * 查询差异参建单位
     *
     * @param pj0101 项目ID
     * @param name   住建厅上报库用户名
     * @return List
     */
    List<PartUnitDTO> getPartUnitList(String pj0101, String name);

    /**
     * 查询班组差异数据
     *
     * @param pj0101 项目ID
     * @param name   住建厅上报库用户名
     * @return List
     */
    List<TeamInfoDTO> getTeamInfoList(String pj0101, String name);

    /**
     * 工人差异数据
     *
     * @param pj0101 项目ID
     * @param name   住建厅上报库用户名
     * @return List
     */
    List<WorkerInfoDTO> getWorkerInfoList(String pj0101, String name);

    /**
     * 管理人员差异数据
     *
     * @param pj0101 项目ID
     * @param name   住建厅上报库用户名
     * @return List
     */
    List<ManagerInfoDTO> getManagerInfoList(String pj0101, String name);
}
