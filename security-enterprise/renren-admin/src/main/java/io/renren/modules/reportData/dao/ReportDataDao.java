package io.renren.modules.reportData.dao;

import io.renren.modules.enterprise.tj01.dto.ReportWrite;
import io.renren.modules.reportData.dto.ReportData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/27 10:05
 */
@Mapper
public interface ReportDataDao {
    /**
     * 查询泸州数据上报情况
     *
     * @return ReportData
     */
    ReportData selectLuZhouReportData();

    /**
     * 查询住建厅数据上报情况
     *
     * @return ReportData
     */
    ReportData selectDourReportData();

    /**
     * 查询住建厅数据写入情况
     *
     * @return List<ReportWrite>
     */
    List<ReportWrite> selectReportWriteData();
}
