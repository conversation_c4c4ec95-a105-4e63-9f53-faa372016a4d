package io.renren.modules.enterprise.ps09.service;

import io.renren.common.page.PageData;
import io.renren.common.service.BaseService;
import io.renren.modules.enterprise.ps09.dto.Ps09DTO;
import io.renren.modules.enterprise.ps09.dto.Ps09Page;
import io.renren.modules.enterprise.ps09.entity.Ps09Entity;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/18 15:48
 */
public interface Ps09Service extends BaseService<Ps09Entity> {
    /**
     * 分页查询
     *
     * @param params Map<String, Object> 分页参数
     * @return PageData<Ps09Page>
     */
    PageData<Ps09Page> pageList(Map<String, Object> params);

    /**
     * 详细信息
     *
     * @param id ps0901
     * @return Ps09DTO
     */
    Ps09DTO getInfo(Long id);

    /**
     * 新增保存
     *
     * @param dto Ps09DTO
     */
    void saveInfo(Ps09DTO dto);

    /**
     * 修改保存
     *
     * @param dto Ps09DTO
     */
    void updateInfo(Ps09DTO dto);

    /**
     * 审核保存
     * @param dto Ps09DTO
     */
    void auditInfo(Ps09DTO dto);
}
