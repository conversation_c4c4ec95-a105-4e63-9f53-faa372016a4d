package io.renren.modules.enterprise.tj01.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 住建厅数据写入情况
 *
 * <AUTHOR>
 * @since 1.0.0 2023-10-31
 */
@Data
@ApiModel(value = "住建厅数据写入情况")
public class Tj01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "序号")
    private String serialNumber;

    @ApiModelProperty(value = "统计日期")
    private Integer statisticalDay;

    @ApiModelProperty(value = "项目")
    private Integer projectInfo;

    @ApiModelProperty(value = "参建单位")
    private Integer partUnitsInfo;

    @ApiModelProperty(value = "班组")
    private Integer teamInfo;

    @ApiModelProperty(value = "人员")
    private Integer personInfo;

    @ApiModelProperty(value = "考勤")
    private Integer attendanceInfo;

}