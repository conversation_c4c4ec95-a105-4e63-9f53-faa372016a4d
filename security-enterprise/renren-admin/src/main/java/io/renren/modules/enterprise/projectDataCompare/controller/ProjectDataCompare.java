package io.renren.modules.enterprise.projectDataCompare.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.projectDataCompare.dto.*;
import io.renren.modules.enterprise.projectDataCompare.service.ProjectDataCompareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-12-08 13:18
 */
@RestController
@RequestMapping("enterprise/projectDataCompare")
@Api(tags = "住建厅项目对比")
public class ProjectDataCompare {
    @Autowired
    private ProjectDataCompareService compareService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "projectStatus", value = "项目状态", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "projectName", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "areaCode", value = "行政区划", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:projectDataCompare:page")
    public Result<PageData<ProjectDataCompareDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<ProjectDataCompareDTO> page = compareService.page(params);
        return new Result<PageData<ProjectDataCompareDTO>>().ok(page);
    }

    @GetMapping("getPartUnitList/{pj0101}")
    @ApiOperation("参建单位差异数据")
    @RequiresPermissions("enterprise:projectDataCompare:getPartUnitList")
    public Result<List<PartUnitDTO>> getPartUnitList(@PathVariable("pj0101") String pj0101) {
        List<PartUnitDTO> list = compareService.partUnitList(pj0101);
        return new Result<List<PartUnitDTO>>().ok(list);
    }

    @GetMapping("getTeamInfoList/{pj0101}")
    @ApiOperation("班组差异数据")
    @RequiresPermissions("enterprise:projectDataCompare:getTeamInfoList")
    public Result<List<TeamInfoDTO>> getTeamInfoList(@PathVariable("pj0101") String pj0101) {
        List<TeamInfoDTO> list = compareService.teamInfoList(pj0101);
        return new Result<List<TeamInfoDTO>>().ok(list);
    }

    @GetMapping("getWorkerInfoList/{pj0101}")
    @ApiOperation("工人差异数据")
    @RequiresPermissions("enterprise:projectDataCompare:getWorkerInfoList")
    public Result<List<WorkerInfoDTO>> getWorkerInfoList(@PathVariable("pj0101") String pj0101) {
        List<WorkerInfoDTO> list = compareService.workerInfoList(pj0101);
        return new Result<List<WorkerInfoDTO>>().ok(list);
    }

    @GetMapping("getManagerInfoList/{pj0101}")
    @ApiOperation("管理人员差异数据")
    @RequiresPermissions("enterprise:projectDataCompare:getManagerInfoList")
    public Result<List<ManagerInfoDTO>> getManagerInfoList(@PathVariable("pj0101") String pj0101) {
        List<ManagerInfoDTO> list = compareService.managerInfoList(pj0101);
        return new Result<List<ManagerInfoDTO>>().ok(list);
    }
}
