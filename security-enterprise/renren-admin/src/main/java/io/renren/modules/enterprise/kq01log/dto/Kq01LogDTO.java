package io.renren.modules.enterprise.kq01log.dto;

import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.common.validator.group.DefaultGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 考勤设备信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "考勤设备信息表")
public class Kq01LogDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @CacheParam(name = "kq0101")
    private Long kq0101;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "设备类型_Select选择器",required = true)
    @NotBlank(message = "设备类型不能为空",groups = DefaultGroup.class)
    private String terminaltype;

    @ApiModelProperty(value = "设备序列号",required = true)
    @NotBlank(message = "设备序列号不能为空",groups = DefaultGroup.class)
    @Length(max = 50,message = "设备序列号长度不能超过50位",groups = DefaultGroup.class)
    private String deviceserialno;

    @ApiModelProperty(value = "设备注册码",required = true)
    @NotBlank(message = "设备注册码不能为空",groups = DefaultGroup.class)
    @Length(max = 50,message = "设备注册码长度不能超过50",groups = DefaultGroup.class)
    private String devicekey;

    @ApiModelProperty(value = "进出状态_Select选择器",required = true)
    @NotBlank(message = "进出状态不能为空",groups = DefaultGroup.class)
    private String note;

    @ApiModelProperty(value = "名称",required = true)
    private String name;

    @ApiModelProperty(value = "是否可用")
    private String whether;

    @ApiModelProperty(value = "网络状态")
    private String networkStatus;


}