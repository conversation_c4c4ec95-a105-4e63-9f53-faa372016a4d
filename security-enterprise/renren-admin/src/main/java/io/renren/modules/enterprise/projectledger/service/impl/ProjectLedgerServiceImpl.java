/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.modules.enterprise.projectledger.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ExcelUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.projectledger.dao.ProjectLedgerDao;
import io.renren.modules.enterprise.projectledger.dto.ProjectLedgerDTO;
import io.renren.modules.enterprise.projectledger.entity.ProjectLedgerEntity;
import io.renren.modules.enterprise.projectledger.service.ProjectLedgerService;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class ProjectLedgerServiceImpl extends CrudServiceImpl<ProjectLedgerDao,ProjectLedgerEntity,ProjectLedgerDTO> implements ProjectLedgerService {

    @Autowired
    private ProjectLedgerDao projectLedgerDao;
    /**
     * 行政区划后2位
     */
    private final static String END_STR = "00";
    /**
     * 导出模板文件路径
     */
    @Value("${expFile.projectLedger}")
    private  String projectLedger;

    @Override
    public PageData<ProjectLedgerDTO> selectProjectLedgerEntityList(Map<String, Object> params) {
        //转换成like
        paramsToLike(params, "projectName");
        //当行政区划最后2位是00的时候，截取前4位作为行政区划进行查询
        String areaCode = (String) params.get("areaCode");
        params.put("areaCode", CommonUtils.coverAreaCode(areaCode));
        Long deptId = SecurityUser.getDeptId();
        params.put("deptId", deptId);
        IPage<ProjectLedgerEntity> page = getPage(params, "", false);
        List<ProjectLedgerEntity> list = projectLedgerDao.selectProjectLedgerEntityList(params);
        return getPageData(list, page.getTotal(), ProjectLedgerDTO.class);
    }

    @Override
    public void excelExp(Map<String, Object> params, HttpServletResponse response) throws Exception {
        File file = new File(projectLedger);
        if (!file.exists()) {
            throw new IOException("配置的项目台账模板文件不存在");
        }
        String areaCode = (String)params.get("areaCode");
        params.put("areaCode", CommonUtils.coverAreaCode(areaCode));
        //获取当前用户的机构id
        Long deptId = SecurityUser.getDeptId();
        params.put("deptId", deptId);
        paramsToLike(params, "projectName");
        List<ProjectLedgerEntity> list = projectLedgerDao.selectProjectLedgerEntityList(params);
        TemplateExportParams exportParams = new TemplateExportParams(projectLedger);
        Map<String, Object> data = new HashMap<>();
        data.put("list", list);
        data.put("date", params.get("dayDate"));
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, data);
        String exportFileName = "项目台账";
        ExcelUtils.export(response, workbook, exportFileName);
    }


    @Override
    public QueryWrapper<ProjectLedgerEntity> getWrapper(Map<String, Object> params) {
        return null;
    }
}
