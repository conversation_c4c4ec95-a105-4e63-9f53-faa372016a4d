package io.renren.modules.enterprise.tj01.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 住建厅数据写入情况
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-10-31
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_TJ01")
public class Tj01Entity {
	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	private Long id;
    /**
     * 统计日期
     */
	private Integer statisticalDay;
    /**
     * 项目
     */
	private Integer projectInfo;
    /**
     * 参建单位
     */
	private Integer partUnitsInfo;
    /**
     * 班组
     */
	private Integer teamInfo;
    /**
     * 人员
     */
	private Integer personInfo;
    /**
     * 考勤
     */
	private Integer attendanceInfo;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}