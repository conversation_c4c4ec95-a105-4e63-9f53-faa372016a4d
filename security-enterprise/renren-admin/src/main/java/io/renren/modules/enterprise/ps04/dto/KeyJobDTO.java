package io.renren.modules.enterprise.ps04.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> Chris
 * @Date : 2021-06-16
 **/
@Data
public class KeyJobDTO {

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "人员ID")
    private Long ps0401;

    @ApiModelProperty(value = "参建单位ID")
    private Long cp0201;

    @ApiModelProperty(value = "项目名称")
    private String projectname;

    @ApiModelProperty(value = "参见类型")
    private String corptype;

    @ApiModelProperty(value = "岗位类型")
    private String jobtype;

    @ApiModelProperty(value = "岗位名称")
    private String jobtypename;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证号码")
    private String idcardnumber;

    @ApiModelProperty(value = "公司名称")
    private String companyname;

    @ApiModelProperty(value = "进退场")
    private String inorout;

    @ApiModelProperty(value = "进场时间")
    private String entertime;


}
