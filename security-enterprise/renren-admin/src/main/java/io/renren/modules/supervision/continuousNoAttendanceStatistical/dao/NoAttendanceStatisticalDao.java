package io.renren.modules.supervision.continuousNoAttendanceStatistical.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.modules.enterprise.kq02.dto.Kq02AttendanceDTO;
import io.renren.modules.supervision.continuousNoAttendanceStatistical.dto.NoAttendanceStatisticalDTO;
import io.renren.modules.supervision.continuousNoAttendanceStatistical.entity.NoAttendanceStatisticalEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chris
 * @Date : 2022-04-20
 **/
@Mapper
public interface NoAttendanceStatisticalDao extends BaseMapper<NoAttendanceStatisticalEntity> {

    /**
     * 查询连续缺考勤天数
     * @param params
     * @return
     */
    IPage<NoAttendanceStatisticalDTO> getAbsenceDays(@Param("page")IPage page, @Param("params") Map<String, Object> params);


    List<NoAttendanceStatisticalDTO> exportAbsenceDays(@Param("params") Map<String, Object> params);

}
