package io.renren.modules.supervision.equipmentStatistical.service;


import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.supervision.equipmentStatistical.dto.EquipmentStatisticalDTO;
import io.renren.modules.supervision.equipmentStatistical.dto.OutEquipmentDTO;
import io.renren.modules.supervision.equipmentStatistical.entity.EquipmentStatisticalEntity;

import java.util.List;
import java.util.Map;

/**
 * 设备统计
 *
 * <AUTHOR>
 * @since 1.0.0 2021-04-26
 */
public interface EquipmentStatisticalService extends CrudService<EquipmentStatisticalEntity, EquipmentStatisticalDTO> {


    /**
     * 地区设备统计分页数据
     * @param params 查询参数
     * @return 地区设备统计分页数据
     */
    List<EquipmentStatisticalDTO> getAreaCodePageData(Map<String, Object> params);

    /**
     * 离线设备分页数据
     * @param params 查询参数
     * @return 离线设备分页数据
     */
    PageData<OutEquipmentDTO> outEquipmentPage(Map<String, Object> params);
}