package io.renren.modules.enterprise.ps02.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-7-8 08:37:20
 * 工人列表
 */
@Data
@ApiModel(value = "建筑工人分页列表信息")
public class Ps02PageDTO {

    @ApiModelProperty(value = "主键ID")
    private Long ps0201;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "所属项目")
    private String projectName;

    @ApiModelProperty(value = "所属企业")
    private String corpName;

    @ApiModelProperty(value = "所属班组")
    private String tm0101;

    @ApiModelProperty(value = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "姓名")
    private String personName;

    @ApiModelProperty(value = "身份证号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idCardNumber;

    @ApiModelProperty(value = "是否班组长")
    private String isTeamLeader;

    @ApiModelProperty(value = "手机号码")
    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    private String cellPhone;

    @ApiModelProperty(value = "工种")
    private String workTypeCode;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entryTime;

    @ApiModelProperty(value = "退场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exitTime;
}
