package io.renren.modules.enterprise.pj06.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import io.renren.common.exception.RenException;
import io.renren.common.utils.NumberUtil;
import io.renren.common.utils.aliyun.AilynUtils;
import io.renren.common.utils.aliyun.CompanyDTO;
import io.renren.modules.enterprise.cp01.dao.Cp01Dao;
import io.renren.modules.enterprise.cp01.entity.Cp01Entity;
import io.renren.modules.enterprise.pj06.service.Pj06AsyncService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020-08-21 11:46
 */
@Service
public class Pj06AsyncServiceImpl implements Pj06AsyncService {
    @Autowired
    private Cp01Dao cp01Dao;

    @Override
    public void saveEnterpriseBusinessInfo(Long cp0101, String queryParam) {
        try {
            Thread.sleep(5000);
            CompanyDTO companyDTO = AilynUtils.getApiStore(queryParam);
            if (BeanUtil.isNotEmpty(companyDTO)) {
                Cp01Entity cp01Entity = new Cp01Entity();
                cp01Entity.setCp0101(cp0101);
                //统一社会信用代码
                cp01Entity.setCorpcode(companyDTO.getCreditCode());
                //公司名称
                cp01Entity.setCorpname(companyDTO.getCompanyName());
                //企业性质
                cp01Entity.setOrganizationtype("");
                //注册地区
                cp01Entity.setAreacode("");
                //企业营业地址
                cp01Entity.setAddress(companyDTO.getAddress());
                //邮政编码
                cp01Entity.setZipcode("");
                //法人
                cp01Entity.setLegalman(companyDTO.getFaRen());
                //注册资本
                cp01Entity.setRegcapital(new BigDecimal(NumberUtil.getNumber(companyDTO.getRegMoney())));
                //注册资本币种
                cp01Entity.setCapitalcurrencytype("CNY");
                //成立日期
                String chkDate = companyDTO.getIssueTime();
                if (StringUtils.isNotBlank(chkDate)) {
                    cp01Entity.setEstablishdate(DateUtil.parse(chkDate, "yyyy-MM-dd"));
                }
                //办公电话
                cp01Entity.setOfficephone("");
                //传真号码
                cp01Entity.setFaxnumber("");
                //联系人
                cp01Entity.setLinkman("");
                //联系人办公电话
                cp01Entity.setLinkphone(companyDTO.getPhone());
                //联系人手机号码
                cp01Entity.setLinkcellphone("");
                //邮箱
                cp01Entity.setEmail(companyDTO.getEmail());
                //网址
                cp01Entity.setWebsite(companyDTO.getWebSite());
                //经营状态
                cp01Entity.setBusinessstatus("1");
                //经营范围
                cp01Entity.setEntscope(companyDTO.getBussinessDes());
                //登记机关
                cp01Entity.setRegdept(companyDTO.getRegOrgName());
                cp01Dao.updateById(cp01Entity);
            }
        } catch (Exception e) {
            throw new RenException("异步获取企业信息出错");
        }
    }
}
