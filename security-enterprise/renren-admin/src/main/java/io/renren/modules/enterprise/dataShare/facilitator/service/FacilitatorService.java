package io.renren.modules.enterprise.dataShare.facilitator.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.dataShare.facilitator.dto.FacilitatorDTO;
import io.renren.modules.enterprise.dataShare.facilitator.entity.FacilitatorEntity;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dto.FacilitatorProjectDTO;

import java.util.Map;

/**
 * 数据共享密钥信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-03-22
 */
public interface FacilitatorService extends CrudService<FacilitatorEntity, FacilitatorDTO> {

    /**
     * 保存
     * @param dto FacilitatorDTO
     */
    void saveInfo(FacilitatorDTO dto);

    /**
     * 分页查询
     * @param params Map<String, Object>
     * @return PageData<FacilitatorDTO>
     */
    PageData<FacilitatorDTO> pageInfo(Map<String, Object> params);

    /**
     * 更新是否可用状态
     * @param ids 主键
     * @param state 状态
     */
    void updateWhether(Long[] ids, int state);

    /**
     * 企业关联项目
     * @param dto FacilitatorProjectDTO
     */
    void relatedProject(FacilitatorProjectDTO dto);
}