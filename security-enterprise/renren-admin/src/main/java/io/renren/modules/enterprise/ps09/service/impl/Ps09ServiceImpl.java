package io.renren.modules.enterprise.ps09.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.file.ImageUtil;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.BaseServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.CompareFaceUtil;
import io.renren.common.utils.ConvertUtils;
import io.renren.common.utils.SendDataUtil;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.kq05.service.Kq05Service;
import io.renren.modules.enterprise.ot01.dao.Ot01Dao;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.ot01.entity.Ot01Entity;
import io.renren.modules.enterprise.ot01.service.Ot01Service;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import io.renren.modules.enterprise.ps01.service.Ps01Service;
import io.renren.modules.enterprise.ps02.dao.Ps02Dao;
import io.renren.modules.enterprise.ps04.dao.Ps04Dao;
import io.renren.modules.enterprise.ps04.dto.Ps04DTO;
import io.renren.modules.enterprise.ps04.entity.Ps04Entity;
import io.renren.modules.enterprise.ps04.service.Ps04Service;
import io.renren.modules.enterprise.ps07.dto.Ps07DTO;
import io.renren.modules.enterprise.ps07.service.Ps07Service;
import io.renren.modules.enterprise.ps09.dao.Ps09Dao;
import io.renren.modules.enterprise.ps09.dto.Ps09DTO;
import io.renren.modules.enterprise.ps09.dto.Ps09Page;
import io.renren.modules.enterprise.ps09.entity.Ps09Entity;
import io.renren.modules.enterprise.ps09.service.Ps09Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/18 15:48
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Ps09ServiceImpl extends BaseServiceImpl<Ps09Dao, Ps09Entity> implements Ps09Service {
    @Autowired
    private Ot01Dao ot01Dao;
    @Autowired
    private Ps01Service ps01Service;
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private Ps04Dao ps04Dao;
    @Autowired
    private Ps07Service ps07Service;
    @Autowired
    private Kq05Service kq05Service;
    @Autowired
    private SendDataUtil sendDataUtil;
    @Autowired
    private Ps04Service ps04Service;
    @Autowired
    private Ps02Dao ps02Dao;
    /**
     * 图片header
     */
    private final static String IMAGE = "data:image";
    /**
     * 待审核状态
     */
    private final static String AWAIT_AUDIT = "0";
    /**
     * 审核成功
     */
    private final static String AUDIT_SUCCESS = "1";
    /**
     * 人员类型
     */
    private final static String PERSON_TYPE = "2";

    @Override
    public PageData<Ps09Page> pageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        String areaCode = (String) params.get("areaCode");
        params.put("areaCode", CommonUtils.coverAreaCode(areaCode));
        IPage<Ps09Entity> page = getPage(params, "", false);
        List<Ps09Page> list = baseDao.selectPageList(params);
        return getPageData(list, page.getTotal(), Ps09Page.class);
    }

    @Override
    public Ps09DTO getInfo(Long id) {
        //PS09信息
        Ps09Entity ps09Entity = baseDao.selectById(id);
        Ps09DTO ps09DTO = ConvertUtils.sourceToTarget(ps09Entity, Ps09DTO.class);
        //PS01信息
        Ps01DTO ps01Info = ps01Service.getPs01Info(ps09Entity.getPs0101());
        ps09DTO.setPs01DTO(ps01Info);
        //证书信息
        List<Ot01DTO> ot01DTOList = ot01Service.loadBusinessData(id, "22");
        ps09DTO.setOt01DTOList(ot01DTOList);
        return ps09DTO;
    }

    @Override
    public void saveInfo(Ps09DTO dto) {
        Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
        Long ps0101 = dto.getPs0101();
        //判断人员是否存在
        int personCount = baseDao.selectPersonIsExist(pj0101, dto.getCp0201(), dto.getJobtype());
        if (personCount > 0) {
            throw new RenException("人员审核中,请勿重复录入");
        }
        Ps09Entity ps09Entity = ConvertUtils.sourceToTarget(dto, Ps09Entity.class);
        //保存PS09信息
        ps09Entity.setPj0101(pj0101);
        ps09Entity.setPs0101(ps0101);
        ps09Entity.setEntrytime(DateUtil.parse(DateUtil.today()));
        ps09Entity.setInOrOut("1");
        baseDao.insert(ps09Entity);
        //附件资料
        List<Long> longList = dto.getOt01DTOList().stream()
                .map(Ot01DTO::getOt0101).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(longList)) {
            ot01Dao.updateTypeById(ps09Entity.getPs0901(), longList, "22");
        }
    }

    @Override
    public void updateInfo(Ps09DTO dto) {
        String state = dto.getState();
        //已审核通过的不允许修改
        if (!AWAIT_AUDIT.equals(state)) {
            throw new RenException("已审核信息不能修改");
        }
        String photo = dto.getPhoto();
        Ps01DTO ps01DTO = dto.getPs01DTO();
        // 更新照片
        if (StrUtil.startWith(photo, IMAGE)) {
            String headImage = ImageUtil.base64ToImage(dto.getPhoto());
            dto.setPhoto(headImage);
            //认证比对
            CompareFaceUtil.personIdentCheck("",
                    headImage,
                    ps01DTO.getIdcardnumber(),
                    ps01DTO.getName());
        }
        //保存或者修改PS01信息
        Ps01Entity ps01Entity = ConvertUtils.sourceToTarget(ps01DTO, Ps01Entity.class);
        ps01Service.saveOrUpdate(ps01Entity);
        //更新PS09
        Ps09Entity ps09Entity = ConvertUtils.sourceToTarget(dto, Ps09Entity.class);
        baseDao.updateById(ps09Entity);
        // 附件资料
        List<Long> longList = dto.getOt01DTOList().stream()
                .map(Ot01DTO::getOt0101).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(longList)) {
            ot01Dao.updateByIdBusily(dto.getPs0901(), longList);
        }
    }

    @Override
    public void auditInfo(Ps09DTO dto) {
        String state = dto.getState();
        Ps09Entity ps09Entity = ConvertUtils.sourceToTarget(dto, Ps09Entity.class);
        if (AUDIT_SUCCESS.equals(state)) {
            //校验项目经理和总监理工程师
            ps04Service.checkPartManager(dto.getCp0201(), dto.getJobtype(), dto.getPj0101());
            //判断该项目下是否有工人
            Integer sum = ps02Dao.selectCountPs0101(dto.getPj0101(), dto.getPs0101());
            if (sum > 0) {
                throw new RenException("项目中已存在该人员！请勿重复添加");
            }
            //更新PS09
            ps09Entity.setAuditDate(new Date());
            baseDao.updateById(ps09Entity);
            //数据写入PS04
            Ps04Entity ps04Entity = ConvertUtils.sourceToTarget(dto, Ps04Entity.class);
            ps04Dao.insert(ps04Entity);
            //进退场记录表增加数据
            Ps07DTO ps07DTO = new Ps07DTO();
            ps07DTO.setEntryOrExitTime(DateUtil.parse(DateUtil.today()));
            ps07DTO.setInOrOut("1");
            ps07DTO.setPs0401(ps04Entity.getPs0401());
            ps07Service.save(ps07DTO);
            // 附件资料
            List<Ot01DTO> ot01DTOList = dto.getOt01DTOList();
            for (Ot01DTO ot01DTO : ot01DTOList) {
                Ot01Entity contract = new Ot01Entity();
                contract.setName(ot01DTO.getName());
                contract.setBusitype("21");
                contract.setOriginalName(ot01DTO.getOriginalName());
                contract.setUrl(ot01DTO.getUrl());
                contract.setWhether("1");
                contract.setViewType(ot01DTO.getViewType());
                contract.setBusisysno(ps04Entity.getPs0401());
                ot01Dao.insert(contract);
            }
            //人员注册、下发到设备
            kq05Service.saveCreatePerson(dto.getPj0101(), ps04Entity.getPs0401(), dto.getPs01DTO().getName(), dto.getPhoto(), PERSON_TYPE);
            Ps04DTO ps04DTO = ConvertUtils.sourceToTarget(ps04Entity, Ps04DTO.class);
            sendDataUtil.sendData(ps04DTO, Constant.ADD);
        } else {
            //更新PS09
            ps09Entity.setAuditDate(new Date());
            baseDao.updateById(ps09Entity);
        }

    }
}
