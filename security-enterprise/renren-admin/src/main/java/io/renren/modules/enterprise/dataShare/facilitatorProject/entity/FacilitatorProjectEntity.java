package io.renren.modules.enterprise.dataShare.facilitatorProject.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 第三方服务商和项目的关系
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_FACILITATOR_PROJECT")
public class FacilitatorProjectEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 服务商ID
     */
    private Long facilitatorId;
    /**
     * 项目ID
     */
    private Long pj0101;
    /**
     * 是否可用(0否,1是)
     */
    private String whether;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}