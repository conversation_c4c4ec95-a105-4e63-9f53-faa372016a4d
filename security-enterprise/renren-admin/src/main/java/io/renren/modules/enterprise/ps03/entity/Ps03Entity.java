package io.renren.modules.enterprise.ps03.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 建筑工人合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PS03")
public class Ps03Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long ps0301;
    /**
     * 建筑工人ID
     */
    private Long ps0201;
    /**
     * 合同编号
     */
    private String contractcode;
    /**
     * 合同期限类型
     */
    private String contractperiodtype;
    /**
     * 签订日期
     */
    private Date signdate;
    /**
     * 开始日期
     */
    private Date startdate;
    /**
     * 结束时期
     */
    private Date enddate;
    /**
     * 计量单位
     */
    private String unit;
    /**
     * 计量单价
     */
    private BigDecimal unitprice;
    /**
     * 备注
     */
    private String memo;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}