package io.renren.modules.enterprise.projectDataCompare.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021-12-10 10:17
 */
@Data
@ApiModel(value = "班组差异数据")
public class TeamInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "责任人")
    private String responsiblePersonName;

    @ApiModelProperty(value = "联系电话")
    private String responsiblePersonPhone;
}
