package io.renren.modules.supervision.equipmentStatistical.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.admin.sys.dao.SysRegionDao;
import io.renren.modules.supervision.equipmentStatistical.dao.EquipmentStatisticalDao;
import io.renren.modules.supervision.equipmentStatistical.dto.EquipmentStatisticalDTO;
import io.renren.modules.supervision.equipmentStatistical.dto.OutEquipmentDTO;
import io.renren.modules.supervision.equipmentStatistical.entity.EquipmentStatisticalEntity;
import io.renren.modules.supervision.equipmentStatistical.service.EquipmentStatisticalService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 地区设备统计分页数据
 *
 * <AUTHOR>
 * @since 1.0.0 2021-04-26
 */
@Service
public class EquipmentStatisticalServiceImpl extends CrudServiceImpl<EquipmentStatisticalDao, EquipmentStatisticalEntity, EquipmentStatisticalDTO> implements EquipmentStatisticalService {

    @Autowired
    private EquipmentStatisticalDao equipmentStatisticalDao;

    @Autowired
    private SysRegionDao sysRegionDao;

    @Override
    public QueryWrapper<EquipmentStatisticalEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<EquipmentStatisticalEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public List<EquipmentStatisticalDTO> getAreaCodePageData(Map<String, Object> params) {
        //获取当前查询地区的层级并添加到查询条件中
        params.put("level",sysRegionDao.getTreeLevelByValue((String) params.get("areaCode")));
        params.put("deptId", SecurityUser.getDeptId());
        return equipmentStatisticalDao.getAreaCodePageData(params);
    }

    @Override
    public PageData<OutEquipmentDTO> outEquipmentPage(Map<String, Object> params) {
        params.put("areaCode", CommonUtils.coverAreaCode((String) params.get("areaCode")));
        paramsToLike(params,"projectName");
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        Page<OutEquipmentDTO> page = new Page<>(curPage, limit);
        List<OutEquipmentDTO> list = equipmentStatisticalDao.getOutEquipmentPageData(page, params);
        return getPageData(list, page.getTotal(), OutEquipmentDTO.class);
    }


}