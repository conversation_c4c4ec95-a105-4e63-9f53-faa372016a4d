package io.renren.modules.enterprise.rpj01dept.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.rpj01dept.dao.RPj01DeptDao;
import io.renren.modules.enterprise.rpj01dept.dto.InsertRpdDTO;
import io.renren.modules.enterprise.rpj01dept.dto.Pj0101NameDTO;
import io.renren.modules.enterprise.rpj01dept.dto.RPj01DeptDTO;
import io.renren.modules.enterprise.rpj01dept.entity.RPj01DeptEntity;
import io.renren.modules.enterprise.rpj01dept.service.RPj01DeptService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

/**
 * 项目和机构的关系表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-05-11
 */
@Service
public class RPj01DeptServiceImpl extends CrudServiceImpl<RPj01DeptDao, RPj01DeptEntity, RPj01DeptDTO> implements RPj01DeptService {
    @Autowired
    private RPj01DeptDao rPj01DeptDao;

    @Override
    public QueryWrapper<RPj01DeptEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<RPj01DeptEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public Result insertByInsertRpdDTO(InsertRpdDTO dto) {
        int delete = rPj01DeptDao.deleteByInsertRpdDTO(dto);
        List<Long> pj0101s = dto.getPj0101s();
        Result result = new Result();
        int failure = 0;
        RPj01DeptEntity rPj01DeptEntity = new RPj01DeptEntity();
        for (Long pj0101 : pj0101s) {
            rPj01DeptEntity.setDeptId(dto.getDeptId());
            rPj01DeptEntity.setPj0101(pj0101);
            if (rPj01DeptDao.getCountByRPj01DeptDTO(rPj01DeptEntity)>0){
                failure++;
                continue;
            }
            rPj01DeptDao.insert(rPj01DeptEntity);
            rPj01DeptEntity.setId(null);
        }
        result.setCode(0);
        result.setMsg("一共添加" + (pj0101s.size()-failure) + "条，删除" + delete +"条");
        return result;
    }

    @Override
    public List<Pj0101NameDTO> getPj0101List() {
        return rPj01DeptDao.getPj0101List();
    }

    @Override
    public List<String> getOwnPj0101List(Long deptId) {
        return rPj01DeptDao.getOwnPj0101List(deptId);
    }
}