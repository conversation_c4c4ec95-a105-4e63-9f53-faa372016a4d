package io.renren.modules.enterprise.dataShare.facilitator.dao;

import io.renren.common.common.dto.CommonDto;
import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.dataShare.facilitator.dto.FacilitatorDTO;
import io.renren.modules.enterprise.dataShare.facilitator.entity.FacilitatorEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 数据共享密钥信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-03-22
 */
@Mapper
public interface FacilitatorDao extends BaseDao<FacilitatorEntity> {

    /**
     * 分页查询
     * @param params Map<String, Object>
     * @return List<FacilitatorDTO>
     */
    List<FacilitatorDTO> getListData(Map<String, Object> params);

    /**
     * 更新状态
     *
     * @param ids   主键
     * @param state 状态
     */
    void updateWhether(@Param("ids") List<Long> ids, @Param("state") int state);

    /**
     * 查询企业信息
     * @return List<CommonDto>
     */
    List<CommonDto> selectListAll();

    /**
     * 查询企业信息是否存在
     * @param code 统一社会信用代码
     * @return Integer
     */
    Integer selectCountByCode(String code);
}