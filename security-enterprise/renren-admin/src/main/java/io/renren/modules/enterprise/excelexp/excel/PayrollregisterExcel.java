package io.renren.modules.enterprise.excelexp.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 工资发放花名册导出对象
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-05-18
 */
@Data
public class PayrollregisterExcel {
    @Excel(name = "班组名称", width = 12 )
    private String teamname;

    @Excel(name = "姓名", width = 10)
    private String name;

    @Excel(name = "性别", width = 10)
    private String gender;

    @Excel(name = "身份证号码", width = 25)
    private String idcardnumber;

    @Excel(name = "联系电话", width = 25)
    private String cellphone;

    @Excel(name = "银行卡卡号", width = 26)
    private String payrollbankcardnumber;

    @Excel(name = "标价")
    private String  unitprice;

    @Excel(name = "出勤天数", width = 10)
    private Integer count;

    @Excel(name = "个人确认签字", width = 25)
    private String selfsignature;

    @Excel(name = "备注", width = 26)
    private String remarks;

}