package io.renren.modules.enterprise.message.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.BaseServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.common.utils.SendDataUtil;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.cp02.dto.Cp02DTO;
import io.renren.modules.enterprise.cp02.service.Cp02Service;
import io.renren.modules.enterprise.kq02.dto.Kq02DTO;
import io.renren.modules.enterprise.message.dao.MessageDao;
import io.renren.modules.enterprise.message.dto.MessagePage;
import io.renren.modules.enterprise.message.service.MessageService;
import io.renren.modules.enterprise.pa01.dto.Pa01DTO;
import io.renren.modules.enterprise.pa01.service.Pa01Service;
import io.renren.modules.enterprise.pj01.dto.Pj01DTO;
import io.renren.modules.enterprise.pj01.service.Pj01Service;
import io.renren.modules.enterprise.ps02.dto.Ps02DTO;
import io.renren.modules.enterprise.ps02.service.impl.Ps02ServiceImpl;
import io.renren.modules.enterprise.ps04.dto.Ps04DTO;
import io.renren.modules.enterprise.ps04.service.Ps04Service;
import io.renren.modules.enterprise.tm01.dto.Tm01DTO;
import io.renren.modules.enterprise.tm01.entity.Tm01Entity;
import io.renren.modules.enterprise.tm01.service.Tm01Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/6 8:56
 */
@Service
public class MessageServiceImpl extends BaseServiceImpl<MessageDao, MessagePage> implements MessageService {
    @Autowired
    private SendDataUtil sendDataUtil;
    @Autowired
    private Pj01Service pj01Service;
    @Autowired
    private Cp02Service cp02Service;
    @Autowired
    private Tm01Service tm01Service;
    @Autowired
    private Pa01Service pa01Service;
    @Autowired
    private Ps02ServiceImpl ps02Service;
    @Autowired
    private Ps04Service ps04Service;


    @Override
    public PageData<MessagePage> pageList(Map<String, Object> params) {
        //分页
        IPage<MessagePage> page = getPage(params, "", false);
        String areaCode = (String) params.get("areaCode");
        params.put("areaCode", CommonUtils.coverAreaCode(areaCode));
        //查询
        List<MessagePage> list = baseDao.getList(params);

        return getPageData(list, page.getTotal(), MessagePage.class);
    }

    @Override
    public void batchPush(List<Long> asList) {
        for (Long pj0101 : asList) {
            //项目信息推送
            Pj01DTO projectInfo = pj01Service.getProjectInfo(pj0101);
            boolean project = ValidatorUtils.validateSyncEntity(projectInfo);
            if (!project) {
                throw new RenException("项目【" + projectInfo.getName() + "】信息未完善");
            }
            sendDataUtil.sendData(projectInfo);
            //专户信息推送
            Long pa0101 = baseDao.selectPa01(pj0101);
            Pa01DTO pa01DTO = pa01Service.get(pa0101);
            boolean dedicatedAccount = ValidatorUtils.validateSyncEntity(pa01DTO);
            if (!dedicatedAccount) {
                throw new RenException("专户【" + projectInfo.getName() + "】信息未完善");
            }
            sendDataUtil.sendData(pa01DTO);
            //参建单位信息推送
            List<Long> partUnitsIds = baseDao.selectPartUnits(pj0101);
            for (Long partUnitsId : partUnitsIds) {
                Cp02DTO cp02DTO = cp02Service.get(partUnitsId);
                boolean partUnit = ValidatorUtils.validateSyncEntity(cp02DTO);
                if (!partUnit) {
                    throw new RenException("参建单位【" + cp02DTO.getCp01DTO().getCorpname() + "】信息未完善");
                }
                sendDataUtil.sendData(cp02DTO);
            }
            //班组信息推送
            List<Long> teamIds = baseDao.selectTeam(pj0101);
            for (Long teamId : teamIds) {
                Tm01DTO tm01DTO = tm01Service.get(teamId);
                Tm01Entity tm01Entity = ConvertUtils.sourceToTarget(tm01DTO, Tm01Entity.class);
                sendDataUtil.sendData(tm01Entity);
            }
            //工人信息推送
            List<Long> personIds = baseDao.selectPersonIds(pj0101);
            for (Long personId : personIds) {
                Ps02DTO ps02Info = ps02Service.getPs02Info(personId);
                sendDataUtil.sendData(ps02Info);
            }
            //管理人员信息推送
            List<Long> managerIds = baseDao.selectManagerIds(pj0101);
            for (Long managerId : managerIds) {
                Ps04DTO ps04 = ps04Service.getPs04(managerId);
                sendDataUtil.sendData(ps04, Constant.ADD);
            }
            //考勤推送,只推送当天的数据
            List<Kq02DTO> kq02DTOList = baseDao.selectAttInfo(pj0101);
            for (Kq02DTO kq02DTO : kq02DTOList) {
                sendDataUtil.sendData(kq02DTO);
            }
        }
    }
}
