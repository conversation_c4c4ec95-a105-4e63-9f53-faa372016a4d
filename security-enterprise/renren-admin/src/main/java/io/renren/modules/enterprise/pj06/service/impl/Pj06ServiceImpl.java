package io.renren.modules.enterprise.pj06.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.common.dto.CommonDto;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.ConvertUtils;
import io.renren.modules.admin.message.service.SysSmsService;
import io.renren.modules.admin.sys.dao.SysDeptDao;
import io.renren.modules.admin.sys.dao.SysRoleDao;
import io.renren.modules.admin.sys.dto.SysUserDTO;
import io.renren.modules.admin.sys.entity.SysDeptEntity;
import io.renren.modules.admin.sys.service.SysParamsService;
import io.renren.modules.admin.sys.service.SysUserService;
import io.renren.modules.enterprise.cp01.dao.Cp01Dao;
import io.renren.modules.enterprise.cp01.dto.Cp01DTO;
import io.renren.modules.enterprise.cp01.entity.Cp01Entity;
import io.renren.modules.enterprise.cp02.dao.Cp02Dao;
import io.renren.modules.enterprise.cp02.entity.Cp02Entity;
import io.renren.modules.enterprise.cp03.dto.BCp03DTO;
import io.renren.modules.enterprise.cp03.service.BCp03Service;
import io.renren.modules.enterprise.ot01.dao.Ot01Dao;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.ot01.entity.Ot01Entity;
import io.renren.modules.enterprise.ot01.service.Ot01Service;
import io.renren.modules.enterprise.pj01.dao.Pj01Dao;
import io.renren.modules.enterprise.pj01.entity.Pj01Entity;
import io.renren.modules.enterprise.pj01report.dao.Pj01ReportDao;
import io.renren.modules.enterprise.pj01report.entity.Pj01ReportEntity;
import io.renren.modules.enterprise.pj06.dao.Pj06Dao;
import io.renren.modules.enterprise.pj06.dao.Rpj01DeptDao;
import io.renren.modules.enterprise.pj06.dto.Pj06DTO;
import io.renren.modules.enterprise.pj06.entity.Pj06Entity;
import io.renren.modules.enterprise.pj06.entity.Rpj01DeptEntity;
import io.renren.modules.enterprise.pj06.service.Pj06AsyncService;
import io.renren.modules.enterprise.pj06.service.Pj06Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Pj06ServiceImpl extends CrudServiceImpl<Pj06Dao, Pj06Entity, Pj06DTO> implements Pj06Service {
    @Autowired
    private Pj06Service pj06Service;
    @Autowired
    private Pj06Dao pj06Dao;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private SysDeptDao sysDeptDao;
    @Autowired
    private Cp01Dao cp01Dao;
    @Autowired
    private Cp02Dao cp02Dao;
    @Autowired
    private Rpj01DeptDao rpj01DeptDao;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysSmsService sysSmsService;
    @Autowired
    private SysParamsService sysParamsService;
    @Autowired
    private BCp03Service cp03Service;
    @Autowired
    private Pj01ReportDao pj01ReportDao;
    @Autowired
    private SysRoleDao sysRoleDao;
    @Autowired
    private Pj06AsyncService pj06AsyncService;
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private Ot01Dao ot01Dao;
    private static final String REVIEW_SUCCESS = "1";
    private static final String REVIEW_ERROR = "2";
    private static final String ATTACHMENT_TYPE = "04";

    @Override
    public QueryWrapper<Pj06Entity> getWrapper(Map<String, Object> params) {
        String name = (String) params.get("name");
        QueryWrapper<Pj06Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(name), "name", name);
        wrapper.orderByDesc("CREATE_DATE");
        return wrapper;
    }

    @Override
    public PageData<Pj06DTO> getPageData(Map<String, Object> params) {
        paramsToLike(params, "name");
        IPage<Pj06Entity> page = getPage(params, "", false);
        List<Pj06Entity> pageData = pj06Dao.getPageData(params);
        return getPageData(pageData, page.getTotal(), Pj06DTO.class);
    }

    @Override
    public void saveCheckProjectInfo(Pj06DTO dto) {
        if (REVIEW_SUCCESS.equals(dto.getStatus())) {
            dto.setUpdateDate(new Date());
            //数据写入到SYS_DEPT,创建机构
            Long deptId = pj06Service.saveDeptInfo(dto);
            //数据写入到PJ01,新增项目基础信息
            Long pj0101 = pj06Service.savePj01Info(dto, deptId);
            //保存建设单位信息
            pj06Service.saveConstruction(dto, pj0101);
            //保存总包单位信息
            pj06Service.saveContractUnit(dto, pj0101);
            //数据写入到SYS_USER,创建用户
            SysUserDTO sysUserDTO = pj06Service.saveUserInfo(dto, deptId);
            //写入到项目和机构的关系表R_PJ01_DEPT
            pj06Service.savePj01DeptInfo(pj0101, deptId);
            //写入项目上报配置表
            // 上报地
            String report = dto.getReport();
            // 获取上报银行
            String payBankCode = "";
            if (StringUtils.isNotBlank(dto.getPayBankCode())) {
                payBankCode = dto.getPayBankCode();
            }
            //保存项目和银行的关系，主要是泸州的项目需要区分是哪个银行的
            pj06Service.saveProjectBankRelation(pj0101, payBankCode);
            //保存项目上报配置信息
            pj06Service.savePj01Report(pj0101, report, payBankCode);
            //发送用户名和密码到注册项目的手机号码
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("username", sysUserDTO.getUsername());
            jsonObject.put("password", sysUserDTO.getPassword());
            sysSmsService.send("1001", dto.getLinkphone(), jsonObject.toJSONString());

        } else if (REVIEW_ERROR.equals(dto.getStatus())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("notPassMsg", dto.getMemo());
            sysSmsService.send("1002", dto.getLinkphone(), jsonObject.toJSONString());
        }
        //更新项目审核数据表PJ06
        pj06Service.update(dto);
        //存储过程调用
        pj06Dao.callProjectRelation();
    }

    @Override
    public void saveProjectInfo(Pj06DTO dto) {
        //先判断PJ06是否存在项目
        Long projectNumber = pj06Dao.getProjectNumber(dto.getName(), dto.getAreacode());
        if (projectNumber > 0) {
            throw new RenException("项目已注册,请您耐心等待审核！");
        }
        //在判断PJ01是否存在项目
        Long pj01Number = pj06Dao.getPj01Number(dto.getName(), dto.getAreacode());
        if (pj01Number > 0) {
            throw new RenException("项目已存在,请勿重复注册！");
        }
        //设置默认审核状态
        dto.setStatus("0");
        pj06Service.save(dto);
        //保存施工许可证
        List<Ot01DTO> list = dto.getPermitList();
        if (list.size() > 0) {
            //更新OT01的数据关联业务数据
            List<Long> ot0101List = list.stream()
                    .filter(ot01DTO -> StrUtil.isBlank(ot01DTO.getBusisysno()))
                    .map(Ot01DTO::getOt0101)
                    .collect(Collectors.toList());
            if (ot0101List.size() > 0) {
                ot01Service.updateBusily(dto.getPj0601(), ot0101List);
            }
        }
    }

    @Override
    public Long saveDeptInfo(Pj06DTO dto) {
        SysDeptEntity sysDeptEntity = new SysDeptEntity();
        sysDeptEntity.setAreacode(dto.getAreacode());
        sysDeptEntity.setPid(1067246875800000063L);
        sysDeptEntity.setPids("1067246875800000063,1067246875800000066");
        sysDeptEntity.setName(dto.getName());
        sysDeptDao.insert(sysDeptEntity);
        return sysDeptEntity.getId();
    }

    @Override
    public Long savePj01Info(Pj06DTO dto, Long deptId) {
        Pj01Entity pj01Entity = new Pj01Entity();
        pj01Entity.setName(dto.getName());
        pj01Entity.setIndustry(dto.getIndustry());
        pj01Entity.setAreacode(dto.getAreacode());
        pj01Entity.setLinkman(dto.getLinkman());
        pj01Entity.setLinkphone(dto.getLinkphone());
        pj01Entity.setDeptId(deptId);
        //新注册的项目默认设置为在建项目
        pj01Entity.setPrjstatus("1");
        pj01Dao.insert(pj01Entity);
        //保存施工许可证
        List<Ot01DTO> list = dto.getPermitList();
        for (Ot01DTO ot01DTO : list) {
            Ot01Entity contract = new Ot01Entity();
            contract.setName(ot01DTO.getName());
            contract.setBusitype("03");
            contract.setOriginalName(ot01DTO.getOriginalName());
            contract.setUrl(ot01DTO.getUrl());
            contract.setWhether("1");
            contract.setViewType(ot01DTO.getViewType());
            contract.setBusisysno(pj01Entity.getPj0101());
            ot01Dao.insert(contract);
        }
        return pj01Entity.getPj0101();
    }

    @Override
    public void saveConstruction(Pj06DTO dto, Long pj0101) {
        Cp01DTO cp01DTO = cp01Dao.loadCp01(dto.getConstructionnumber());
        Cp02Entity cp02Entity = new Cp02Entity();
        //如果参建不存在就新增信息到CP01
        if (ObjectUtil.isNull(cp01DTO)) {
            Cp01Entity construction = new Cp01Entity();
            construction.setCorpname(dto.getConstructionname());
            construction.setCorpcode(dto.getConstructionnumber());
            cp01Dao.insert(construction);
            cp02Entity.setCp0101(construction.getCp0101());
            //获取企业工商信息
            pj06AsyncService.saveEnterpriseBusinessInfo(construction.getCp0101(), dto.getConstructionnumber());
        } else {
            cp02Entity.setCp0101(cp01DTO.getCp0101());
        }
        cp02Entity.setCorptype("8");
        cp02Entity.setPj0101(pj0101);
        cp02Entity.setEntrytime(DateUtil.parse(DateUtil.today()));
        cp02Entity.setInOrOut("1");
        cp02Dao.insert(cp02Entity);
        //保存进退场记录表
        BCp03DTO cp03DTO = new BCp03DTO();
        cp03DTO.setCp0201(cp02Entity.getCp0201());
        cp03DTO.setEntryOrExitTime(cp02Entity.getEntrytime());
        cp03DTO.setInOrOut("1");
        cp03Service.save(cp03DTO);
    }

    @Override
    public void saveContractUnit(Pj06DTO dto, Long pj0101) {
        Cp01DTO cp01DTO = cp01Dao.loadCp01(dto.getContractnumber());
        Cp02Entity cp02Entity = new Cp02Entity();
        if (ObjectUtil.isNull(cp01DTO)) {
            Cp01Entity cp01Entity = new Cp01Entity();
            cp01Entity.setCorpname(dto.getContractname());
            cp01Entity.setCorpcode(dto.getContractnumber());
            cp01Dao.insert(cp01Entity);
            cp02Entity.setCp0101(cp01Entity.getCp0101());
            //获取企业工商信息
            pj06AsyncService.saveEnterpriseBusinessInfo(cp01Entity.getCp0101(), dto.getContractnumber());
        } else {
            cp02Entity.setCp0101(cp01DTO.getCp0101());
        }
        cp02Entity.setCorptype("9");
        cp02Entity.setPj0101(pj0101);
        cp02Entity.setEntrytime(DateUtil.parse(DateUtil.today()));
        cp02Entity.setInOrOut("1");
        cp02Dao.insert(cp02Entity);
        //保存进退场记录表
        BCp03DTO cp03DTO = new BCp03DTO();
        cp03DTO.setCp0201(cp02Entity.getCp0201());
        cp03DTO.setEntryOrExitTime(cp02Entity.getEntrytime());
        cp03DTO.setInOrOut("1");
        cp03Service.save(cp03DTO);
    }

    @Override
    public SysUserDTO saveUserInfo(Pj06DTO dto, Long deptId) {
        SysUserDTO sysUserDTO = new SysUserDTO();
        sysUserDTO.setPassword(sysParamsService.getValue(Constant.PASSWORD));
        sysUserDTO.setDeptId(deptId);
        sysUserDTO.setRealName(dto.getLinkman());
        sysUserDTO.setMobile(dto.getLinkphone());
        sysUserDTO.setSuperAdmin(0);
        sysUserDTO.setStatus(1);
        sysUserDTO.setUserType("1");
        //生成用户名
        sysUserDTO.setUsername(pj06Service.generateUserName(dto.getAreacode()));
        // 用户角色
        List<Long> collect = new ArrayList<>();
        String[] split = dto.getRoleIdList().split(",");
        for (String s : split) {
            collect.add(Long.valueOf(s));
        }
        sysUserDTO.setRoleIdList(collect);
        sysUserService.save(sysUserDTO);
        return sysUserDTO;
    }

    @Override
    public void savePj01DeptInfo(Long pj0101, Long deptId) {
        Rpj01DeptEntity rpj01DeptEntity = new Rpj01DeptEntity();
        rpj01DeptEntity.setDeptId(deptId);
        rpj01DeptEntity.setPj0101(pj0101);
        rpj01DeptDao.insert(rpj01DeptEntity);
        //保存管理员，如果管理员的机构ID发生改变要修改此处的值
        Rpj01DeptEntity rpj01DeptSuperAdmin = new Rpj01DeptEntity();
        rpj01DeptSuperAdmin.setDeptId(1067246875800000066L);
        rpj01DeptSuperAdmin.setPj0101(pj0101);
        rpj01DeptDao.insert(rpj01DeptSuperAdmin);
    }

    @Override
    public String generateUserName(String areaCode) {
        String userName;
        //先查询当前行政区划的账号使用到哪个数字
        String maxNum = pj06Dao.selectUserNameMax(areaCode);
        //判断字符串是否为空
        if (StringUtils.isNotBlank(maxNum)) {
            //获取行政区划后边的数字转成int,进行计算
            Integer after = Integer.parseInt(StringUtils.substringAfter(maxNum, areaCode)) + 1;
            //把计算之后的数字转成字符串，拼接行政区划组成用户名
            userName = areaCode.concat(StringUtils.leftPad(String.valueOf(after), 5, "0"));
            return userName;
        }
        userName = StringUtils.rightPad(String.valueOf(areaCode), 11, "0");
        return userName;
    }

    @Override
    public void savePj01Report(Long pj0101, String report, String payBankCode) {
        Pj01ReportEntity pj01ReportEntity = new Pj01ReportEntity();
        pj01ReportEntity.setPj0101(pj0101);
        pj01ReportEntity.setReportType(report);
        pj01ReportEntity.setPayBankCode(payBankCode);
        pj01ReportDao.insert(pj01ReportEntity);
    }

    @Override
    public List<CommonDto> getRoleList() {

        return sysRoleDao.getRoleList();
    }

    @Override
    public void saveProjectBankRelation(Long pj0101, String payBankCode) {
        pj06Dao.saveProjectBankRelation(pj0101, payBankCode);
    }

    @Override
    public Pj06DTO getInfo(Long id) {
        //项目注册信息
        Pj06Entity pj06Entity = pj06Dao.selectById(id);
        Pj06DTO pj06DTO = ConvertUtils.sourceToTarget(pj06Entity, Pj06DTO.class);
        //施工许可证信息
        List<Ot01DTO> ot01DTOList = ot01Service.loadBusinessData(pj06Entity.getPj0601(), ATTACHMENT_TYPE);
        pj06DTO.setPermitList(ot01DTOList);
        return pj06DTO;
    }

}