package io.renren.modules.enterprise.ps04.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.kq05.dto.PersonDTO;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.ps04.dto.KeyJobDTO;
import io.renren.modules.enterprise.ps04.dto.ManagerInOrOut;
import io.renren.modules.enterprise.ps04.dto.Ps04PageDTO;
import io.renren.modules.enterprise.ps04.entity.Ps04Entity;
import io.renren.modules.enterprise.ps04.excel.Ps04Excel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface Ps04Dao extends BaseDao<Ps04Entity> {
    /**
     * 列表分页查询
     *
     * @param params
     * @return
     */
    List<Ps04PageDTO> getListData(Map<String, Object> params);

    void updateInOrOutByIds(@Param("list") List<ManagerInOrOut> list, @Param("type") String type);

    List<PersonDTO> selectPersonByIds(List<Long> longs);

    List<KeyJobDTO> getKeyJobData(Map<String, Object> params);

    List<Map<String, Object>> getKeyStation();

    /**
     * 查询关键岗位的数量
     *
     * @param partType 参建单位类型
     * @param jobType  岗位类型
     * @param pj0101   项目ID
     * @return Integer
     */
    Integer selectByCount(@Param("partType") String partType, @Param("jobType") String jobType, @Param("pj0101") Long pj0101);

    /**
     * 查询参建单位类型
     *
     * @param cp0201 cp0201
     * @return String
     */
    String selectTypeById(Long cp0201);

    /**
     * 获取管理人员附件信息
     *
     * @param ps0401
     * @return
     */
    List<Ot01DTO> getAttachments(Long ps0401);

    List<Ps04Entity> selectKeys(Long pj0101);

    /**
     * 查询关键岗位信息
     *
     * @param cp0201  参建单位ID
     * @param jobType 岗位类型
     * @return Integer
     */
    int getKeyPositions(@Param("cp0201") Long cp0201, @Param("jobType") String jobType);

    /**
     * 查询管理人员是否关键岗位人员
     *
     * @param ps0401 ID
     * @return int
     */
    int selectKeyPositionsById(Long ps0401);

    /**
     * 更新管理人员头像
     *
     * @param ps0401 人员ID
     * @param photo  头像url
     */
    void updateManagerPhoto(@Param("ps0401") Long ps0401, @Param("photo") String photo);

    /**
     * 管理人员信息导出
     *
     * @param params Map<String, Object>
     * @return List<Ps04Excel>
     */
    List<Ps04Excel> selectManagerExcelList(Map<String, Object> params);
}