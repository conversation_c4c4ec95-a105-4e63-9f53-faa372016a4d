package io.renren.modules.enterprise.excelexp.dao;

import io.renren.modules.enterprise.excelexp.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 三表导出
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-18
 */
@Mapper
public interface ExcelExpDao {

    /**
     * 工资发放花名册
     */
    List<PayrollRegisterDTO> getPayrollRegisterDTOList(Map<String, Object> params);

    /**
     * 用工备案表
     */
    List<WorkerRecordDTO> getWorkerRecordDTOList(Map<String, Object> params);

    /**
     * 考勤统计表
     */
    List<AttendanceRecordDTO> getAttendanceRecordDTOList(Map<String, Object> params);


    /**
     * 月份的每一天
     *
     * @param month 月份
     * @return List<MonthDayDTO>
     */
    List<MonthDayDTO> getMonthDay(String month);

    /**
     * 导出查询
     *
     * @param params       Map<String, Object> params
     * @param monthDayList List<MonthDayDTO> monthDayList
     * @return List<AttendanceRecordDTO>
     */
    List<AttendanceRecordDTO> getExportAttendanceList(@Param("params") Map<String, Object> params, @Param("monthDayList") List<MonthDayDTO> monthDayList);

    /**
     * 考勤表导出查询
     *
     * @param params       Map<String, Object>
     * @param monthDayList List<MonthDayDTO> monthDayList
     * @return List<AttendanceExcel>
     */
    List<AttendanceExcel> selectAttExcel(@Param("params") Map<String, Object> params, @Param("monthDayList") List<MonthDayDTO> monthDayList);

    /**
     * 查询人员信息
     *
     * @param params params
     * @return List<AttendanceExcel>
     */
    List<AttendanceExcel> selectPersonInfo(Map<String, Object> params);

    /**
     * 查询人员考勤情况
     *
     * @param params Map<String, Object>
     * @return List<PersonAttendance>
     */
    List<PersonAttendance> selectPersonAttendance(Map<String, Object> params);
}