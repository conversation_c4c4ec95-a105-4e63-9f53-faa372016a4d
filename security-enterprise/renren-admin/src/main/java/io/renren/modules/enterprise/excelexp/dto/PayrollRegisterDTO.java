package io.renren.modules.enterprise.excelexp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 工资发放花名册对象
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-18
 */
@Data
@ApiModel(value = "工资发放花名册对象")
public class PayrollRegisterDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "班组名称")
    private String teamname;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "身份证号码")
    private String idcardnumber;

    @ApiModelProperty(value = "联系电话")
    private String cellphone;

    @ApiModelProperty(value = "银行账号")
    private String payrollbankcardnumber;

    @ApiModelProperty(value = "开户行名称")
    private String payRollBankName;

    @ApiModelProperty(value = "标价")
    private String  unitprice;

    @ApiModelProperty(value = "考勤天数")
    private Integer count;


}