package io.renren.modules.supervision.continuousNoAttendanceStatistical.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.kq02.dto.Kq02AttendanceDTO;
import io.renren.modules.supervision.continuousNoAttendanceStatistical.dto.NoAttendanceStatisticalDTO;
import io.renren.modules.supervision.continuousNoAttendanceStatistical.entity.NoAttendanceStatisticalEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR> chris
 * @Date : 2022-04-20
 **/
public interface NoAttendanceStatisticalService extends CrudService<NoAttendanceStatisticalEntity, NoAttendanceStatisticalDTO> {

    PageData<NoAttendanceStatisticalDTO> getAbsenceDays(Map<String, Object> params);


    void exportAbsenceDays(HttpServletResponse response, Map<String, Object> params);
}
