package io.renren.modules.enterprise.pj06.service;

import org.springframework.scheduling.annotation.Async;

/**
 * 异步任务接口
 * <AUTHOR>
 * @Date 2020-08-21 11:46
 */
public interface Pj06AsyncService {
    /**
     * 根据接口获取企业的工商信息，更新
     *
     * @param cp0101     cp0101
     * @param queryParam 完整的公司名称、注册号、信用代码、组织机构代码、税务登记号
     */
    @Async("pool")
    void saveEnterpriseBusinessInfo(Long cp0101, String queryParam);
}
