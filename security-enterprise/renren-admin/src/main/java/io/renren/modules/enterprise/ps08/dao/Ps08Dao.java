package io.renren.modules.enterprise.ps08.dao;


import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.ps08.dto.Ps08PageDTO;
import io.renren.modules.enterprise.ps08.entity.Ps08Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 工人不良记录信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-01-05
 */
@Mapper
public interface Ps08Dao extends BaseDao<Ps08Entity> {

    /**
     * 查询列表数据
     *
     * @param params Map<String, Object>
     * @return PageData<T>
     */
    List<Ps08PageDTO> getListData(Map<String, Object> params);
}