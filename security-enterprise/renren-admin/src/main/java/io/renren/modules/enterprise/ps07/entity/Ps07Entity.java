package io.renren.modules.enterprise.ps07.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目管理人员进退场信息
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PS07")
public class Ps07Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long ps0701;
    /**
     * 管理人员主键ID
     */
    private Long ps0401;
    /**
     * 进退场时间
     */
    private Date entryOrExitTime;
    /**
     * 进退场状态
     */
    private String inOrOut;
    /**
     * 备注
     */
    private String memo;
    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updater;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}