package io.renren.modules.enterprise.tm01.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.common.annotation.iscitizenidvalidator.IsCitizenIdValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 班组基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-27
 */
@Data
@ApiModel(value = "班组基础信息")
public class Tm01DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @CacheParam(name = "tm0101")
    private Long tm0101;

    @ApiModelProperty(value = "参建单位ID")
    private Long cp0201;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "班组长ID")
    private Long ps0201;

    @ApiModelProperty(value = "班组编号", hidden = true)
    private String teamsysno;

    @ApiModelProperty(value = "班组名称", required = true)
    @NotBlank(message = "班组名称不能为空")
    @Length(max = 25, message = "班组名称过长，不能超过25个汉字")
    private String teamname;

    @ApiModelProperty(value = "责任人",required = true)
    @NotBlank(message = "责任人不能为空")
    private String responsiblepersonname;

    @ApiModelProperty(value = "责任人联系电话",required = true)
    @NotBlank(message = "责任人联系电话不能为空")
    private String responsiblepersonphone;

    @ApiModelProperty(value = "证件类型",required = true)
    @NotBlank(message = "证件类型不能为空")
    private String idcardtype;

    @ApiModelProperty(value = "证件号码",required = true)
    @NotBlank(message = "证件号码不能为空")
    @IsCitizenIdValidator
    private String responsiblepersonidnumber;

    @ApiModelProperty(value = "备注")
    @Length(max = 1000, message = "备注不能超过1000个汉字")
    private String memo;

    @ApiModelProperty(value = "进场时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;

    @ApiModelProperty(value = "退场时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date exittime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;
}