/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package io.renren.modules.enterprise.projectledger.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.projectledger.dto.ProjectLedgerDTO;
import io.renren.modules.enterprise.projectledger.service.ProjectLedgerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 项目台账
 *
 * @<NAME_EMAIL>
 */
@RestController
@RequestMapping("/sys/projectLedger")
@Api(tags = "项目台账")
public class ProjectLedgerController {
    @Autowired
    private ProjectLedgerService projectLedgerService;

    @GetMapping("page")
    @ApiOperation("列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "areaCode", value = "行政区划", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "projectName", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "dayDate", value = "日期", example = "yyyy-MM-dd", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "projectStatus", value = "项目状态",  paramType = "query", dataType = "String",required = true)
    })
    @RequiresPermissions("sys:projectLedger:page")
    public Result<PageData<ProjectLedgerDTO>> list(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<ProjectLedgerDTO> pageData = projectLedgerService.selectProjectLedgerEntityList(params);
        return new Result<PageData<ProjectLedgerDTO>>().ok(pageData);
    }

    @GetMapping("export")
    @ApiOperation("导出项目台账")
    @LogOperation("导出项目台账")
    @RequiresPermissions("sys:projectLedger:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {

        projectLedgerService.excelExp(params, response);

    }

}