package io.renren.modules.enterprise.pj01info.dto;

import io.renren.common.annotation.desensitized.Desensitized;
import io.renren.common.enums.DesenTypeEum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 管理人员分页信息
 *
 * <AUTHOR>
 * @Date 2020-08-19 9:57
 */
@Data
@ApiModel(value = "项目管理人员信息")
public class Ps04PageDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0401;

    @ApiModelProperty(value = "所属项目")
    private String projectName;

    @ApiModelProperty(value = "所属企业")
    private String cp0201;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "岗位类型")
    private String jobtype;

    @ApiModelProperty(value = "管理类型")
    private String manageType;

    @ApiModelProperty(value = "身份证号码")
    @Desensitized(type = DesenTypeEum.ID_CARD)
    private String idcardnumber;

    @Desensitized(type = DesenTypeEum.MOBILE_PHONE)
    @ApiModelProperty(value = "手机号码")
    private String cellphone;

}
