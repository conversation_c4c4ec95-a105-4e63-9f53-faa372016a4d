package io.renren.modules.enterprise.excelexp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/14 9:45
 */
@Data
@ApiModel(value = "考勤表明细")
public class AttendanceDetail implements Serializable {
    private static final long serialVersionUID = 1412707016165171689L;

    @ApiModelProperty(value = "出勤日期")
    private String dayName;

    @ApiModelProperty(value = "是否有考勤")
    private String hasAtt;

    @ApiModelProperty(value = "工时")
    private String hoursName;

    @ApiModelProperty(value = "工时时长")
    private String hours;

}
