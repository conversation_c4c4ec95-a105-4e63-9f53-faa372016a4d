package io.renren.modules.supervision.attendanceStatistical.entity;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description:
 * @className: AttendancePersonEntity
 * @author: jh
 * @date: 2022-3-1
 **/
@Data
@EqualsAndHashCode(callSuper=false)
public class AttendancePersonEntity {

    private static final long serialVersionUID = 1L;


    /**
     * 姓名
     */
    private String name;

    /**
     * 工人id/管理人员id
     */
    private String userid;

    /**
     * 身份证号
     */
    private String idcardnumber;

    /**
     * 民族
     */
    private String nation;


    /**
     * 性别
     */
    private String gender;

    /**
     * 电话号码
     */
    private String cellphone;

    /**
     * 岗位类型
     */
    private String jobtype;

    /**
     * 工种
     */
    private String worktypecode;

}
