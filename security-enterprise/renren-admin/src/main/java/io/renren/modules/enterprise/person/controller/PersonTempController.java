package io.renren.modules.enterprise.person.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.person.dto.PersonTempDTO;
import io.renren.modules.enterprise.person.service.PersonTempService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.formula.functions.T;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * 门禁临时人员
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-12
 */
@RestController
@RequestMapping("/enterprise/personTemp")
@Api(tags = "门禁临时人员")
public class PersonTempController {
    @Resource
    private PersonTempService personTempService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:personTemp:page")
    public Result<PageData<PersonTempDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<PersonTempDTO> page = personTempService.pageInfo(params);

        return new Result<PageData<PersonTempDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:personTemp:info")
    public Result<PersonTempDTO> get(@PathVariable("id") Long id) {

        PersonTempDTO data = personTempService.get(id);

        return new Result<PersonTempDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:personTemp:save")
    public Result<Object> save(@RequestBody PersonTempDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        personTempService.saveInfo(dto);

        return new Result<>();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:personTemp:update")
    public Result<Object> update(@RequestBody PersonTempDTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        personTempService.updateInfo(dto);

        return new Result<>();
    }

    @PutMapping("personAuthor")
    @ApiOperation("人员下发")
    @RequiresPermissions("enterprise:personTemp:personAuthor")
    public Result<T> personAuthor(@RequestBody Long[] ids) {

        personTempService.deviceAddPerson(ids);

        return new Result<>();
    }

    @PostMapping("deleteInfo")
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:personTemp:delete")
    public Result<Object> delete(@RequestBody List<PersonTempDTO> list) {
        //效验数据
        ValidatorUtils.validateEntity(list);

        personTempService.deleteInfo(list);

        return new Result<>();
    }

}