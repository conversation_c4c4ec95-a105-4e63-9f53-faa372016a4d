package io.renren.modules.enterprise.ps03.service;

import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.ps03.dto.Ps03DTO;
import io.renren.modules.enterprise.ps03.entity.Ps03Entity;

/**
 * 建筑工人合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Ps03Service extends CrudService<Ps03Entity, Ps03DTO> {
    /**
     * 查询工人合同
     * @param ps0201 建筑工人ID
     * @return Ps03DTO
     */
    Ps03DTO getWorkerContract(Long ps0201);
}