package io.renren.modules.supervision.projectStatistic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目信息统计
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "项目信息统计")
public class ProjectStatDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "行政区划名称")
    private String areaName;

    @ApiModelProperty(value = "行政区划值")
    private String areaCode;

    @ApiModelProperty(value = "项目总数")
    private String projectCount;

    @ApiModelProperty(value = "在建项目")
    private String projectUnderCount;

    @ApiModelProperty(value = "本月新增项目")
    private String projectMonthCount;
}