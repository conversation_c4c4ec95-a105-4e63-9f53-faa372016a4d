package io.renren.modules.enterprise.ps08.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.ps08.dto.Ps08DTO;
import io.renren.modules.enterprise.ps08.dto.Ps08PageDTO;
import io.renren.modules.enterprise.ps08.service.Ps08Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 工人不良记录信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-01-05
 */
@RestController
@RequestMapping("enterprise/ps08")
@Api(tags = "工人不良记录信息")
public class Ps08Controller {
    @Autowired
    private Ps08Service ps08Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:ps08:page")
    public Result<PageData<Ps08PageDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params) {
        PageData<Ps08PageDTO> page = ps08Service.pageList(params);
        return new Result<PageData<Ps08PageDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:ps08:info")
    public Result<Ps08DTO> get(@PathVariable("id") Long id) {
        Ps08DTO data = ps08Service.get(id);
        return new Result<Ps08DTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:ps08:save")
    public Result save(@RequestBody Ps08DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps08Service.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:ps08:update")
    public Result update(@RequestBody Ps08DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps08Service.update(dto);

        return new Result();
    }

}