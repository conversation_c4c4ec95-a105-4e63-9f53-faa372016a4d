package io.renren.modules.enterprise.pj06.service;

import io.renren.common.common.dto.CommonDto;
import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.admin.sys.dto.SysUserDTO;
import io.renren.modules.enterprise.pj06.dto.Pj06DTO;
import io.renren.modules.enterprise.pj06.entity.Pj06Entity;

import java.util.List;
import java.util.Map;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
public interface Pj06Service extends CrudService<Pj06Entity, Pj06DTO> {


    PageData<Pj06DTO> getPageData(Map<String, Object> params);

    /**
     * 项目审核保存
     *
     * @param dto
     */
    void saveCheckProjectInfo(Pj06DTO dto);

    /**
     * 保存注册的项目信息
     *
     * @param dto
     * @return
     */
    void saveProjectInfo(Pj06DTO dto);

    /**
     * 保存机构表信息
     *
     * @param dto dto
     * @return id
     */
    Long saveDeptInfo(Pj06DTO dto);

    /**
     * 保存pj01信息
     *
     * @param dto dto
     * @return pj0101
     */
    Long savePj01Info(Pj06DTO dto, Long deptId);

    /**
     * 保存建设单位信息
     *
     * @param dto
     * @param pj0101
     */
    void saveConstruction(Pj06DTO dto, Long pj0101);

    /**
     * 保存总包单位信息
     *
     * @param dto
     * @param pj0101
     */
    void saveContractUnit(Pj06DTO dto, Long pj0101);

    /**
     * 保存用户信息
     *
     * @param dto
     * @param deptId
     * @return
     */
    SysUserDTO saveUserInfo(Pj06DTO dto, Long deptId);

    /**
     * 保存项目和机构的关系表信息
     *
     * @param pj0101 项目id
     * @param deptId 机构ID
     */
    void savePj01DeptInfo(Long pj0101, Long deptId);

    /**
     * 生成用户名
     * @param areaCode
     * @return
     */
    String generateUserName(String areaCode);

    /**
     * 保存数据上报配置
     * @param pj0101 项目ID
     * @param report 上报参数
     * @param payBankCode 银行代码
     */
    void savePj01Report(Long pj0101, String report, String payBankCode);

    /**
     * 加载角色信息
     * @return
     */
    List<CommonDto> getRoleList();

    /**
     * 保存项目和银行的关系
     * @param pj0101 项目ID
     * @param payBankCode 银行代码
     */
    void saveProjectBankRelation(Long pj0101, String payBankCode);

    /**
     * 获取项目注册信息
     * @param id ID
     * @return Pj06DTO
     */
    Pj06DTO getInfo(Long id);
}