package io.renren.modules.safety.aj08.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 见证送样信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-08-19
 */
@Data
@ApiModel(value = "见证送样信息")
public class Aj08DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long aj0801;
    @ApiModelProperty(value = "项目ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pj0101;
    @ApiModelProperty(value = "送检编号")
    private String inspectionCode;
    @ApiModelProperty(value = "送检单类型")
    private String inspectionFormType;
    @ApiModelProperty(value = "送检单类型编码")
    private String inspectionFormTypeCode;
    @ApiModelProperty(value = "检测性质")
    private String qualityType;
    @ApiModelProperty(value = "组数")
    private Long numberOfGroups;
    @ApiModelProperty(value = "是否复检")
    private Long recheck;
    @ApiModelProperty(value = "见证人")
    private String witnesser;
    @ApiModelProperty(value = "见证编号")
    private String witnessCode;
    @ApiModelProperty(value = "见证单位")
    private String witnessCompany;
    @ApiModelProperty(value = "进场数量")
    private Long inNum;
    @ApiModelProperty(value = "施工单位")
    private String constructionCompany;
    @ApiModelProperty(value = "送检单位")
    private String inspectionCompany;
    @ApiModelProperty(value = "检测机构")
    private String testingOrganization;
    @ApiModelProperty(value = "委托编号")
    private String commissionNumber;
    @ApiModelProperty(value = "委托日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date commissionDate;
    @ApiModelProperty(value = "检测费用")
    private BigDecimal testCost;
    @ApiModelProperty(value = "依据标准")
    private String accordingStandard;
    @ApiModelProperty(value = "送样方式")
    private String deliveryMethod;
    @ApiModelProperty(value = "报告领取方式")
    private String reportReceivingMethod;
    @ApiModelProperty(value = "监测参数")
    private String monitoringParams;
    @ApiModelProperty(value = "送检状态")
    private Long inspectionStatus;
    @ApiModelProperty(value = "送样人")
    private String inspectioner;
    @ApiModelProperty(value = "养护温度")
    private String curingTemperature;
    @ApiModelProperty(value = "自控检测")
    private String automaticDetection;
    @ApiModelProperty(value = "收样人")
    private String sampleReceiver;
    @ApiModelProperty(value = "收样时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date sampleDate;

}