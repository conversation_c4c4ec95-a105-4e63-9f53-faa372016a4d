package io.renren.modules.enterprise.pj01GeneralParams.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.pj01GeneralParams.dao.BPj01GeneralParamsDao;
import io.renren.modules.enterprise.pj01GeneralParams.dto.BPj01GeneralParamsDTO;
import io.renren.modules.enterprise.pj01GeneralParams.entity.BPj01GeneralParamsEntity;
import io.renren.modules.enterprise.pj01GeneralParams.service.BPj01GeneralParamsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 项目通用配置
 *
 * <AUTHOR>
 * @since 1.0.0 2025-04-22
 */
@Service
public class BPj01GeneralParamsServiceImpl extends CrudServiceImpl<BPj01GeneralParamsDao, BPj01GeneralParamsEntity, BPj01GeneralParamsDTO> implements BPj01GeneralParamsService {

    @Override
    public QueryWrapper<BPj01GeneralParamsEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<BPj01GeneralParamsEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public PageData<BPj01GeneralParamsDTO> page(Map<String, Object> params) {
        //判断是否是实名制用户，实名制用户检查是否存在项目通用配置信息，不存在则生成
        List<Long> userRole = SecurityUser.getUserRole();
        if(userRole.contains(1254300243697975297L)){
            Long pj0101 = CommonUtils.userProjectInfo().getPj0101();
            BPj01GeneralParamsDTO bPj01GeneralParamsEntity = baseDao.getGeneralParamsInfo(pj0101, SecurityUser.getDeptId());
            if(bPj01GeneralParamsEntity == null){
                //生成数据保存
                BPj01GeneralParamsDTO bPj01GeneralParamsDTO = new BPj01GeneralParamsDTO();
                bPj01GeneralParamsDTO.setPj0101(pj0101);
                bPj01GeneralParamsDTO.setExitdayswitch("0");
                bPj01GeneralParamsDTO.setExitdayparam(7);
                saveInfo(bPj01GeneralParamsDTO);
            }
        }
        //查询分页数据
        params.put("deptId", SecurityUser.getDeptId());
        IPage<BPj01GeneralParamsEntity> page = getPage(params, "", false);
        List<BPj01GeneralParamsDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), BPj01GeneralParamsDTO.class);
    }

    @Override
    public BPj01GeneralParamsDTO getGeneralParamsInfo(Long id) {
        return baseDao.getGeneralParamsInfo(id, SecurityUser.getDeptId());
    }

    @Override
    public void saveInfo(BPj01GeneralParamsDTO dto) {
        BPj01GeneralParamsEntity bPj01GeneralParamsEntity = BeanUtil.copyProperties(dto, BPj01GeneralParamsEntity.class);
        baseDao.insert(bPj01GeneralParamsEntity);
    }

    @Override
    public void updateInfo(BPj01GeneralParamsDTO dto) {
        BPj01GeneralParamsEntity bPj01GeneralParamsEntity = BeanUtil.copyProperties(dto, BPj01GeneralParamsEntity.class);
        baseDao.update(bPj01GeneralParamsEntity, new QueryWrapper<BPj01GeneralParamsEntity>().eq("pj0101", dto.getPj0101()));
    }

    @Override
    public List<BPj01GeneralParamsDTO> getWorkerExitProject() {
        return baseDao.getWorkerExitProject();
    }
}