package io.renren.modules.enterprise.tj01.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.tj01.dto.Tj01DTO;
import io.renren.modules.enterprise.tj01.entity.Tj01Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 住建厅数据写入情况
 *
 * <AUTHOR>
 * @since 1.0.0 2023-10-31
 */
@Mapper
public interface Tj01Dao extends BaseDao<Tj01Entity> {

    /**
     * 查询最近一周的数据
     *
     * @return List<Tj01DTO>
     */
    List<Tj01DTO> selectLastWeekList();
}