package io.renren.modules.reportData.service.impl;

import io.renren.modules.enterprise.tj01.dto.ReportWrite;
import io.renren.modules.reportData.dao.ReportDataDao;
import io.renren.modules.reportData.dto.ReportData;
import io.renren.modules.reportData.service.ReportDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/27 10:03
 */
@Service
public class ReportDataServiceImpl implements ReportDataService {
    @Autowired
    private ReportDataDao reportDataDao;

    @Override
    public ReportData getLuZhouReportData() {
        return reportDataDao.selectLuZhouReportData();
    }

    @Override
    public ReportData getDourReportData() {
        return reportDataDao.selectDourReportData();
    }

    @Override
    public List<ReportWrite> getReportWriteData() {
        return reportDataDao.selectReportWriteData();
    }
}
