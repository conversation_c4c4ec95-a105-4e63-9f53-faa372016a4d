package io.renren.modules.supervision.map.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/20 9:08
 */
@Data
@ApiModel(value = "项目地图信息")
public class ProjectMapDTO implements Serializable {

    private static final long serialVersionUID = -2306433983225603661L;

    @ApiModelProperty(value = "pj0101")
    private String pj0101;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目经度")
    private String lng;

    @ApiModelProperty(value = "项目纬度")
    private String lat;

    @ApiModelProperty(value = "项目状态")
    private String projectStatus;
}
