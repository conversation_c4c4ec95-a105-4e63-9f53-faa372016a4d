package io.renren.modules.enterprise.ps08.service;


import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.ps08.dto.Ps08DTO;
import io.renren.modules.enterprise.ps08.dto.Ps08PageDTO;
import io.renren.modules.enterprise.ps08.entity.Ps08Entity;

import java.util.Map;

/**
 * 工人不良记录信息
 *
 * <AUTHOR>
 * @since 1.0.0 2022-01-05
 */
public interface Ps08Service extends CrudService<Ps08Entity, Ps08DTO> {
    /**
     * 查询列表数据
     *
     * @param params Map<String, Object>
     * @return PageData<T>
     */
    PageData<Ps08PageDTO> pageList(Map<String, Object> params);
}