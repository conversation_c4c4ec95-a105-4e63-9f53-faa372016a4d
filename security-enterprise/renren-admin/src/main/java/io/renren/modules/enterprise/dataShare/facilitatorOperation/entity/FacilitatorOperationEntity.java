package io.renren.modules.enterprise.dataShare.facilitatorOperation.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 数据共享日志表
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-03-22
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_FACILITATOR_OPERATION")
public class FacilitatorOperationEntity {
	private static final long serialVersionUID = 1L;

    /**
     * id
     */
	private Long id;
    /**
     * 用户操作
     */
	private String operation;
    /**
     * 请求URI
     */
	private String requestUri;
    /**
     * 请求方式
     */
	private String requestMethod;
    /**
     * 请求参数
     */
	private String requestParams;
    /**
     * 请求时长(毫秒)
     */
	private Long requestTime;
    /**
     * 用户代理
     */
	private String userAgent;
    /**
     * 操作IP
     */
	private String ip;
    /**
     * 状态  0：失败   1：成功
     */
	private Short status;
    /**
     * 唯一标识
     */
	private String appKey;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}