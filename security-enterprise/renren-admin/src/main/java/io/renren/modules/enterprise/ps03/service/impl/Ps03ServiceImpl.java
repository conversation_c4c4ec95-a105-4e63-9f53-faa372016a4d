package io.renren.modules.enterprise.ps03.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.ConvertUtils;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.ot01.service.Ot01Service;
import io.renren.modules.enterprise.ps03.dao.Ps03Dao;
import io.renren.modules.enterprise.ps03.dto.Ps03DTO;
import io.renren.modules.enterprise.ps03.entity.Ps03Entity;
import io.renren.modules.enterprise.ps03.service.Ps03Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 建筑工人合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class Ps03ServiceImpl extends CrudServiceImpl<Ps03Dao, Ps03Entity, Ps03DTO> implements Ps03Service {
    @Autowired
    private Ot01Service ot01Service;

    @Override
    public QueryWrapper<Ps03Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Ps03Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }

    @Override
    public void save(Ps03DTO dto) {
        //保存PS03基础数据
        Ps03Entity ps03Entity = ConvertUtils.sourceToTarget(dto, Ps03Entity.class);
        baseDao.insert(ps03Entity);
        //保存合同信息
        List<Ot01DTO> ot01DTOList = dto.getOt01DTOList();
        if (ot01DTOList.size() > 0) {
            //过滤出业务id为空的数据
            List<Long> ot0101List = ot01DTOList
                    .stream()
                    .filter(ot01DTO -> StrUtil.isBlank(ot01DTO.getBusisysno()))
                    .map(Ot01DTO::getOt0101)
                    .collect(Collectors.toList());
            if (ot0101List.size() > 0) {
                ot01Service.updateBusily(ps03Entity.getPs0301(), ot0101List);
            }
        }
    }

    @Override
    public void update(Ps03DTO dto) {
        //保存PS03基础数据
        Ps03Entity ps03Entity = ConvertUtils.sourceToTarget(dto, Ps03Entity.class);
        baseDao.updateById(ps03Entity);
        //保存合同信息
        List<Ot01DTO> dtoList = dto.getOt01DTOList();
        if (dtoList.size() > 0) {
            List<Long> ot0101List = dtoList.stream()
                    .filter(ot01DTO -> StrUtil.isBlank(ot01DTO.getBusisysno()))
                    .map(Ot01DTO::getOt0101)
                    .collect(Collectors.toList());
            if (ot0101List.size() > 0) {
                ot01Service.updateBusily(dto.getPs0301(), ot0101List);
            }
        }
    }

    @Override
    public Ps03DTO getWorkerContract(Long ps0201) {

        return baseDao.selectByPs0201(ps0201);
    }
}