package io.renren.modules.enterprise.pj01info.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.common.annotation.cachelockvalidator.CacheParam;
import io.renren.common.annotation.ismobilevalidator.IsMobileValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Data
@ApiModel(value = "项目基础信息表")
public class Pj01InfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目ID")
    @CacheParam(name = "pj0101")
    private Long pj0101;

    @ApiModelProperty(value = "安监备案号", required = true)
    @NotBlank(message = "安监备案号不能为空")
    @Length(max = 40, message = "安监备案号不能超过40个字符")
    private String safetyno;

    @ApiModelProperty(value = "项目编码")
    private String code;

    @ApiModelProperty(value = "项目名称", required = true)
    @NotBlank(message = "项目名称不能为空")
    @Length(message = "项目名称不能超过200个字符")
    private String name;

    @ApiModelProperty(value = "项目简介")
    private String description;

    @ApiModelProperty(value = "所属行业_Select选择器", required = true)
    @NotBlank(message = "所属行业不能为空")
    private String industry;

    @ApiModelProperty(value = "项目类别_Select选择器", required = true)
    @NotBlank(message = "项目类别不能为空")
    private String category;

    @ApiModelProperty(value = "建设性质_Select选择器", required = true)
    @NotBlank(message = "建设性质不能为空")
    private String constructtype;

    @ApiModelProperty(value = "投资类型_Select选择器", required = true)
    @NotBlank(message = "投资类型不能为空")
    private String investtype;

    @ApiModelProperty(value = "项目所在地_Cascader级联选择器", required = true)
    @NotBlank(message = "项目所在地不能为空")
    private String areacode;

    @ApiModelProperty(value = "建设地址", required = true, example = "成都市武侯区人民南路三段17号")
    @NotBlank(message = "建设地址不能为空")
    private String address;

    @ApiModelProperty(value = "总面积(平方米)_InputNumber计数器", required = true)
    @NotNull(message = "总面积不能为空")
    private BigDecimal buildingarea;

    @ApiModelProperty(value = "总长度(米)_InputNumber计数器")
    private BigDecimal buildinglength;

    @ApiModelProperty(value = "总投资(万元)_InputNumber计数器", required = true)
    @NotNull(message = "总投资不能为空")
    private BigDecimal invest;

    @ApiModelProperty(value = "工程造价(万元)_InputNumber计数器")
    private BigDecimal engineering;

    @ApiModelProperty(value = "项目规模_Select选择器", required = true)
    @NotBlank(message = "项目规模不能为空")
    private String scale;

    @ApiModelProperty(value = "开工日期", required = true, example = "yyyy-MM-dd")
    @NotNull(message = "开工日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startdate;

    @ApiModelProperty(value = "竣工日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completeDate;

    @ApiModelProperty(value = "经度")
    private BigDecimal lng;

    @ApiModelProperty(value = "纬度")
    private BigDecimal lat;

    @ApiModelProperty(value = "联系人姓名", required = true)
    @NotBlank(message = "联系人姓名不能为空")
    private String linkman;

    @ApiModelProperty(value = "联系人电话", required = true)
    @NotBlank(message = "联系人电话不能为空")
    @IsMobileValidator(message = "联系人电话请填写正确的手机号码")
    private String linkphone;

    @ApiModelProperty(value = "是否重点项目_Select选择器")
    private String ismajorProject;

    @ApiModelProperty(value = "是否缴纳保证金_Select选择器")
    private String isdeposit;

    @ApiModelProperty(value = "项目状态_Select选择器", required = true)
    @NotBlank(message = "项目状态不能为空")
    private String prjstatus;

    @ApiModelProperty(value = "机构ID", hidden = true, required = true)
    private Long deptId;


}