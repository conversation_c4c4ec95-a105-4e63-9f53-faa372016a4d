package io.renren.modules.enterprise.dataShare.facilitatorProject.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 第三方服务商和项目的关系
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-22
 */
@Data
@ApiModel(value = "第三方服务商和项目的关系")
public class FacilitatorProjectDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业ID")
    @NotNull(message = "facilitatorId不能为空")
    private Long facilitatorId;

    @ApiModelProperty(value = "项目ID")
    @Valid
    private List<FacilitatorProjectInfo> list;

}