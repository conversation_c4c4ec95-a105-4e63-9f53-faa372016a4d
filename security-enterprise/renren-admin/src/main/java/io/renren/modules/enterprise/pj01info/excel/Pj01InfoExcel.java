package io.renren.modules.enterprise.pj01info.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Data
public class Pj01InfoExcel {
    @Excel(name = "主键id")
    private BigDecimal pj0101;
    @Excel(name = "安监备案号")
    private String safetyno;
    @Excel(name = "项目编码")
    private String code;
    @Excel(name = "项目名称")
    private String name;
    @Excel(name = "项目简介")
    private String description;
    @Excel(name = "所属行业")
    private String industry;
    @Excel(name = "项目类别")
    private String category;
    @Excel(name = "建设性质")
    private String constructtype;
    @Excel(name = "投资类型")
    private String investtype;
    @Excel(name = "项目所在地")
    private String areacode;
    @Excel(name = "建设地址（项目的详细地址具体到XX街道XX号）")
    private String address;
    @Excel(name = "总面积(平方米)")
    private BigDecimal buildingarea;
    @Excel(name = "总长度(米)")
    private BigDecimal buildinglength;
    @Excel(name = "总投资(万元)")
    private BigDecimal invest;
    @Excel(name = "工程造价(万元)")
    private BigDecimal engineering;
    @Excel(name = "项目规模")
    private String scale;
    @Excel(name = "开工日期")
    private Date startdate;
    @Excel(name = "竣工日期")
    private Date completeDate;
    @Excel(name = "经度")
    private BigDecimal lng;
    @Excel(name = "纬度")
    private BigDecimal lat;
    @Excel(name = "联系人姓名")
    private String linkman;
    @Excel(name = "联系人电话")
    private String linkphone;
    @Excel(name = "是否重点项目")
    private String ismajorProject;
    @Excel(name = "是否缴纳保证金")
    private String isdeposit;
    @Excel(name = "项目状态")
    private String prjstatus;
    @Excel(name = "机构ID")
    private BigDecimal deptId;
    @Excel(name = "创建者")
    private BigDecimal creator;
    @Excel(name = "创建时间")
    private Date createDate;
    @Excel(name = "更新者")
    private BigDecimal updater;
    @Excel(name = "更新时间")
    private Date updateDate;

}