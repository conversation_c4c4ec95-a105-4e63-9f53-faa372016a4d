package io.renren.modules.supervision.personneldataStatistical.controller;

import io.renren.common.utils.Result;
import io.renren.modules.supervision.personneldataStatistical.dto.PersonnelDataStatisticalDTO;
import io.renren.modules.supervision.personneldataStatistical.service.PersonnelDataStatisticalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * @description:人员数据统计分析
 * @author: jinghai
 * @date: 2022/3/8 13:46
 **/
@RestController
@RequestMapping("PersonnelData/DataStatistical")
@Api(tags ="人员数据统计分析")
public class PersonnelDataStatisticalController {

    @Autowired
    private PersonnelDataStatisticalService psdService;

    @GetMapping("getWorkerType")
    @ApiOperation("工种数据统计分析")
    @ApiImplicitParam(name = "prjstatus", value = "项目状态", dataType = "String")
    public Result<List<PersonnelDataStatisticalDTO>> getWorkerType(@ApiIgnore @RequestParam Map<String, Object> params){
        List<PersonnelDataStatisticalDTO> workerTypeList = psdService.getWorkType(params);
        return new Result<List<PersonnelDataStatisticalDTO>>().ok(workerTypeList);
    }


    @GetMapping("getNativeData")
    @ApiOperation("籍贯数据统计分析")
    @ApiImplicitParam(name = "prjstatus", value = "项目状态", dataType = "String")
    public Result<List<PersonnelDataStatisticalDTO>> getNativeData(@ApiIgnore @RequestParam Map<String, Object> params){
        List<PersonnelDataStatisticalDTO> nativeDataList = psdService.getNativeData(params);
        return new Result<List<PersonnelDataStatisticalDTO>>().ok(nativeDataList);
    }

    @GetMapping("getGenderData")
    @ApiOperation("性别数据统计分析")
    @ApiImplicitParam(name = "prjstatus", value = "项目状态", dataType = "String")
    public Result<List<PersonnelDataStatisticalDTO>> getGenderData(@ApiIgnore @RequestParam Map<String, Object> params){
        List<PersonnelDataStatisticalDTO> genderDataList = psdService.getGenderData(params);
        return new Result<List<PersonnelDataStatisticalDTO>>().ok(genderDataList);
    }

    @GetMapping("getAgeData")
    @ApiOperation("年龄数据统计分析")
    @ApiImplicitParam(name = "prjstatus", value = "项目状态", dataType = "String")
    public Result<List<PersonnelDataStatisticalDTO>> getAgeData(@ApiIgnore @RequestParam Map<String, Object> params){
        List<PersonnelDataStatisticalDTO> ageDataList = psdService.getAgeData(params);
        return new Result<List<PersonnelDataStatisticalDTO>>().ok(ageDataList);
    }
}
