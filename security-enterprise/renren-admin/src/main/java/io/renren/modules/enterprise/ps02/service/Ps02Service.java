package io.renren.modules.enterprise.ps02.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.ps02.dto.Ps02DTO;
import io.renren.modules.enterprise.ps02.dto.Ps02EmpRecordDTO;
import io.renren.modules.enterprise.ps02.dto.Ps02PageDTO;
import io.renren.modules.enterprise.ps02.entity.Ps02Entity;
import io.renren.modules.enterprise.ps02.excel.Ps02Excel;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
public interface Ps02Service extends CrudService<Ps02Entity, Ps02DTO> {
    /**
     * 列表查询数据
     *
     * @param params
     * @return
     */
    PageData<Ps02PageDTO> pageList(Map<String, Object> params);

    /**
     * 新增保存方法
     *
     * @param dto
     */
    void savePs02Info(Ps02DTO dto);

    /**
     * 更新保存方法
     *
     * @param dto
     */
    void updatePs02Info(Ps02DTO dto);

    /**
     * 加载数据
     *
     * @param id ps0201
     * @return
     */
    Ps02DTO getPs02Info(Long id);

    /**
     * 工人退场
     * @param list List<Ps02DTO>
     */
    void exitPerson(List<Ps02DTO> list);

    /**
     * 工人进场
     * @param list List<Ps02DTO>
     */
    void enterPerson(List<Ps02DTO> list);

    /**
     * 工人下发到设备
     * @param ids
     */
    void deviceAddPerson(Long[] ids);

    /**
     * 导出工人合同模板
     * @param params
     */
    void exportContract(Map<String, Object> params, HttpServletResponse response);

    /**
     * 人员用工记录信息
     * @param params
     * @return
     */
    PageData<Ps02EmpRecordDTO> empRecordPageList(Map<String, Object> params);

    /**
     * 查询人员信息
     * @param params Map<String, Object>
     * @return List<Ps02Excel>
     */
    List<Ps02Excel> getPersonList(Map<String, Object> params);

    /**
     * 获取未出勤工人信息
     * @param pj0101 项目id
     * @param exitdayparam 天数
     * @return
     */
    List<Ps02DTO> getNoAttendanceWorkerByDay(Long pj0101, Integer exitdayparam);
}