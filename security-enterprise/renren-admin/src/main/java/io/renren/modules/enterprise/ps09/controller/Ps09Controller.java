package io.renren.modules.enterprise.ps09.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.ps09.dto.Ps09DTO;
import io.renren.modules.enterprise.ps09.dto.Ps09Page;
import io.renren.modules.enterprise.ps09.service.Ps09Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/18 16:07
 */
@RestController
@RequestMapping("enterprise/ps09")
@Api(tags = "项目关键岗位人员信息")
public class Ps09Controller {
    @Autowired
    private Ps09Service ps09Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "projectName", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "personName", value = "姓名", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "jobType", value = "岗位类型", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "areaCode", value = "行政区划", paramType = "query", dataType = "String")

    })
    @RequiresPermissions("enterprise:ps09:page")
    public Result<PageData<Ps09Page>> page(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<Ps09Page> page = ps09Service.pageList(params);

        return new Result<PageData<Ps09Page>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:ps09:info")
    public Result<Ps09DTO> get(@PathVariable("id") Long id) {

        Ps09DTO data = ps09Service.getInfo(id);

        return new Result<Ps09DTO>().ok(data);
    }

    @PutMapping("audit")
    @ApiOperation("审核")
    @LogOperation("审核")
    @RequiresPermissions("enterprise:ps09:audit")
    public Result<String> audit(@RequestBody Ps09DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps09Service.auditInfo(dto);

        return new Result<>();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:ps09:update")
    public Result<String> update(@RequestBody Ps09DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        ps09Service.updateInfo(dto);

        return new Result<>();
    }
}
