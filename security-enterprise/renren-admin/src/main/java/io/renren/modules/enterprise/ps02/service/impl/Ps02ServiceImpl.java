package io.renren.modules.enterprise.ps02.service.impl;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.exception.RenException;
import io.renren.common.file.ImageUtil;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.common.utils.SendDataUtil;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.kq05.dto.PersonDTO;
import io.renren.modules.enterprise.kq05.service.Kq05Service;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.ot01.service.Ot01Service;
import io.renren.modules.enterprise.pj01.dao.Pj01Dao;
import io.renren.modules.enterprise.pj01.entity.Pj01Entity;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import io.renren.modules.enterprise.ps01.service.Ps01Service;
import io.renren.modules.enterprise.ps02.dao.Ps02Dao;
import io.renren.modules.enterprise.ps02.dto.Ps02ContractDTO;
import io.renren.modules.enterprise.ps02.dto.Ps02DTO;
import io.renren.modules.enterprise.ps02.dto.Ps02EmpRecordDTO;
import io.renren.modules.enterprise.ps02.dto.Ps02PageDTO;
import io.renren.modules.enterprise.ps02.entity.Ps02Entity;
import io.renren.modules.enterprise.ps02.excel.Ps02Excel;
import io.renren.modules.enterprise.ps02.service.Ps02Service;
import io.renren.modules.enterprise.ps03.dto.Ps03DTO;
import io.renren.modules.enterprise.ps03.service.Ps03Service;
import io.renren.modules.enterprise.ps06.dao.BPs06Dao;
import io.renren.modules.enterprise.ps06.dto.BPs06DTO;
import io.renren.modules.enterprise.ps06.entity.BPs06Entity;
import io.renren.modules.enterprise.ps06.service.BPs06Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class Ps02ServiceImpl extends CrudServiceImpl<Ps02Dao, Ps02Entity, Ps02DTO> implements Ps02Service {

    @Autowired
    private Ps02Service ps02Service;
    @Autowired
    private Pj01Dao pj01Dao;
    @Autowired
    private Ps01Service ps01Service;
    @Autowired
    private Ps03Service ps03Service;
    @Autowired
    private Ot01Service ot01Service;
    @Autowired
    private Kq05Service kq05Service;
    @Autowired
    private BPs06Dao ps06Dao;
    @Autowired
    private BPs06Service ps06Service;
    @Autowired
    private SendDataUtil sendDataUtil;

    private final static String IMAGE = "data:image";
    @Value("${expFile.contractExport}")
    private String CONTRACT_EXPORT;
    /**
     * 人员类型
     */
    private final static String PERSON_TYPE = "1";

    @Override
    public QueryWrapper<Ps02Entity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");
        QueryWrapper<Ps02Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }

    @Override
    public PageData<Ps02PageDTO> pageList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        IPage<Ps02Entity> page = getPage(params,"selectAllCount");
        List<Ps02PageDTO> list = baseDao.getListData(params);
        return getPageData(list, page.getTotal(), Ps02PageDTO.class);
    }

    @Override
    public void savePs02Info(Ps02DTO dto) {
        //新增或者更新PS01信息
        Ps01Entity ps01Entity = ConvertUtils.sourceToTarget(dto.getPs01DTO(), Ps01Entity.class);
        //根据身份证倒数第二位判断男女，前端此处处理有问题，改用后台处理
        int genderByIdCard = IdcardUtil.getGenderByIdCard(ps01Entity.getIdcardnumber());
        ps01Entity.setGender(genderByIdCard > 0 ? "1" : "2");
        //身份证转成大写保存,主要处理最后一位是X的身份证
        ps01Entity.setIdcardnumber(ps01Entity.getIdcardnumber().toUpperCase(Locale.ROOT));
        //身份证头像
        String headImageUrl = ps01Entity.getHeadimageurl();
        if (StrUtil.startWith(headImageUrl, IMAGE)) {
            ps01Entity.setHeadimageurl(ImageUtil.base64ToImage(headImageUrl));
        }
        ps01Service.saveOrUpdate(ps01Entity);
        //判断该项目下边人员是否存在
        Integer sum = baseDao.selectCountPs0101(CommonUtils.userProjectInfo().getPj0101(), ps01Entity.getPs0101());
        if (sum > 0) {
            throw new RenException("项目中已存在该人员！请勿重复添加");
        }
        //保存PS02的信息
        dto.setPj0101(CommonUtils.userProjectInfo().getPj0101());
        dto.setPs0101(ps01Entity.getPs0101());
        dto.setEntrytime(DateUtil.parse(DateUtil.today()));
        dto.setInOrOut("1");
        //判断图片是否为空,图片base64字符串储存到服务器，路径保存到数据库
        String headImage = dto.getIssuecardpicurl();
        if (StringUtils.isNotBlank(headImage)) {
            String imagePath = ImageUtil.base64ToImage(headImage);
            dto.setIssuecardpicurl(imagePath);
        }
        ps02Service.save(dto);
        //保存进退场信息
        BPs06Entity ps06Entity = new BPs06Entity();
        ps06Entity.setEntryOrExitTime(DateUtil.parse(DateUtil.today())).setInOrOut("1").setPs0201(dto.getPs0201());
        ps06Dao.insert(ps06Entity);
        //保存合同信息
        Ps03DTO ps03DTO = dto.getPs03DTO();
        ps03DTO.setPs0201(dto.getPs0201());
        ps03Service.save(ps03DTO);
        //人员注册、下发到设备
        kq05Service.saveCreatePerson(CommonUtils.userProjectInfo().getPj0101(), dto.getPs0201(), ps01Entity.getName(), dto.getIssuecardpicurl(), PERSON_TYPE);
        sendDataUtil.sendData(dto);
    }

    @Override
    public void updatePs02Info(Ps02DTO dto) {
        //新增或者更新PS01信息
        Ps01Entity ps01Entity = ConvertUtils.sourceToTarget(dto.getPs01DTO(), Ps01Entity.class);
        //处理身份证头像
        if (StrUtil.startWith(Objects.requireNonNull(ps01Entity).getHeadimageurl(), IMAGE)) {
            ps01Entity.setHeadimageurl(ImageUtil.base64ToImage(ps01Entity.getHeadimageurl()));
        }
        ps01Service.saveOrUpdate(ps01Entity);
        //更新PS02的信息
        boolean file = StrUtil.startWith(dto.getIssuecardpicurl(), IMAGE);
        //判断头像是否文件，如果不是默认为base64字符串，为修改了头像
        if (file) {
            String headImage = ImageUtil.base64ToImage(dto.getIssuecardpicurl());
            dto.setIssuecardpicurl(headImage);
            //更新设备人员照片
            kq05Service.updatePersonImage(CommonUtils.userProjectInfo().getPj0101(), dto.getPs0201(), headImage, PERSON_TYPE);
        }
        ps02Service.update(dto);
        //更新合同信息
        Ps03DTO ps03DTO = dto.getPs03DTO();
        ps03DTO.setPs0201(dto.getPs0201());
        ps03Service.update(ps03DTO);
        sendDataUtil.sendData(dto);

    }

    @Override
    public Ps02DTO getPs02Info(Long id) {
        Ps02DTO ps02DTO = ps02Service.get(id);
        //ps01的基础信息
        Ps01DTO ps01Info = ps01Service.getPs01Info(ps02DTO.getPs0101());
        ps02DTO.setPs01DTO(ps01Info);
        //ps03合同信息
        Ps03DTO ps03DTO = ps03Service.getWorkerContract(ps02DTO.getPs0201());
        ps02DTO.setPs03DTO(ps03DTO);
        //合同附件
        if (BeanUtil.isNotEmpty(ps03DTO)) {
            List<Ot01DTO> ot01DTOList = ot01Service.loadBusinessData(ps03DTO.getPs0301(), "10");
            ps03DTO.setOt01DTOList(ot01DTOList);
        }
        return ps02DTO;
    }

    @Override
    public void exitPerson(List<Ps02DTO> list) {
        List<Long> longs = list.stream().map(Ps02DTO::getPs0201).collect(Collectors.toList());
        baseDao.updateInOrOutByIds(longs, "2");
        for (Ps02DTO ps02DTO : list) {
            Long ps0201 = ps02DTO.getPs0201();
            Long pj0101 = ps02DTO.getPj0101();
            //退场记录表增加数据
            BPs06DTO ps06DTO = new BPs06DTO();
            ps06DTO.setEntryOrExitTime(DateUtil.parse(DateUtil.today()));
            ps06DTO.setInOrOut("2");
            ps06DTO.setPs0201(ps0201);
            ps06Service.save(ps06DTO);
            //人员退场进行销权操作
            kq05Service.personSellRight(pj0101, ps0201, PERSON_TYPE);
            sendDataUtil.sendData(ps0201, pj0101);
        }
    }

    @Override
    public void enterPerson(List<Ps02DTO> list) {
        List<Long> longs = list.stream().map(Ps02DTO::getPs0201).collect(Collectors.toList());
        baseDao.updateInOrOutByIds(longs, "1");
        for (Ps02DTO ps02DTO : list) {
            Long ps0201 = ps02DTO.getPs0201();
            Long pj0101 = ps02DTO.getPj0101();
            BPs06DTO ps06DTO = new BPs06DTO();
            ps06DTO.setEntryOrExitTime(DateUtil.parse(DateUtil.today()));
            ps06DTO.setInOrOut("1");
            ps06DTO.setPs0201(ps0201);
            ps06Service.save(ps06DTO);
            kq05Service.addDevicePerson(ps0201, pj0101, PERSON_TYPE);
            //重新进场人员需要重新上报数据
            Ps02DTO person = getPs02Info(ps0201);
            sendDataUtil.sendData(person);
        }
    }

    @Override
    public void deviceAddPerson(Long[] ids) {
        List<Long> longs = Arrays.asList(ids);
        List<PersonDTO> person = baseDao.selectPersonByIds(longs);
        for (PersonDTO personDTO : person) {
            //人员授权到设备
            kq05Service.saveCreatePerson(personDTO.getPj0101(), personDTO.getUserId(), personDTO.getName(), personDTO.getImageUrl(), PERSON_TYPE);
        }
    }

    @Override
    public void exportContract(Map<String, Object> params, HttpServletResponse response) {
        String ps0201s = params.get("ps0201s").toString();
        if (StringUtils.isBlank(ps0201s)) {
            return;
        }
        String[] ps0201List = ps0201s.split(",");
        OutputStream os = null;
        List<String> files = new ArrayList<>();
        File file = new File(CONTRACT_EXPORT);
        Pj01Entity pj01Entity = pj01Dao.selectOne(new QueryWrapper<Pj01Entity>().eq("dept_id", SecurityUser.getDeptId()));
        String parentPath = file.getParent() + "/contract/" + pj01Entity.getName();
        File dir = new File(parentPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        FileUtil.clean(dir);
        try {
            for (String ps0201 : ps0201List) {
                Ps02ContractDTO ps02ContractDTO = baseDao.exportContract(ps0201);
                // 填充参数
                Map<String, Object> map = new HashMap<>(2);
                map.put("corpname", ps02ContractDTO.getCorpname());
                map.put("legalman", ps02ContractDTO.getLegalman());
                map.put("corpaddress", ps02ContractDTO.getCorpaddress());
                map.put("name", ps02ContractDTO.getName());
                map.put("gender", ps02ContractDTO.getGender());
                map.put("birthday", DateUtil.format(ps02ContractDTO.getBirthday(), "yyyy-MM-dd"));
                map.put("address", ps02ContractDTO.getAddress());
                map.put("idcardnumber", ps02ContractDTO.getIdcardnumber());
                map.put("cellphone", ps02ContractDTO.getCellphone());
                map.put("corpcode", ps02ContractDTO.getCorpcode());
                map.put("linkcellphone", ps02ContractDTO.getLinkcellphone());
                map.put("projectname", ps02ContractDTO.getProjectname());
                map.put("worktypecode", ps02ContractDTO.getWorktypecode());
                XWPFDocument doc = WordExportUtil.exportWord07(CONTRACT_EXPORT, map);
                String fileName = parentPath + "/" + ps02ContractDTO.getName() + "-" + ps02ContractDTO.getIdcardnumber() + ".docx";
                os = new FileOutputStream(fileName);
                files.add(fileName);
                doc.write(os);
                doc.close();
            }
            Objects.requireNonNull(os).flush();
        } catch (Exception e) {
            log.error(e.toString());
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        CommonUtils.writeZip(files, pj01Entity.getName() + "工人合同", response);
    }

    @Override
    public PageData<Ps02EmpRecordDTO> empRecordPageList(Map<String, Object> params) {
        IPage<Ps02Entity> page = getPage(params, "", false);
        List<Ps02EmpRecordDTO> list = baseDao.getEmpRecordListData(params);
        return getPageData(list, page.getTotal(), Ps02EmpRecordDTO.class);
    }

    @Override
    public List<Ps02Excel> getPersonList(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());
        return baseDao.selectPersonExcelList(params);
    }

    @Override
    public List<Ps02DTO> getNoAttendanceWorkerByDay(Long pj0101, Integer exitdayparam) {
        return baseDao.getNoAttendanceWorkerByDay(pj0101, exitdayparam);
    }

}