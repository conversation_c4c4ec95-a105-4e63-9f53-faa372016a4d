package io.renren.modules.enterprise.pj07.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj07.dto.Pj07Page;
import io.renren.modules.enterprise.pj07.entity.Pj07Entity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/7 9:40
 */
@Mapper
public interface Pj07Dao extends BaseDao<Pj07Entity> {
    /**
     * 分页查询
     *
     * @param params Map<String, Object>
     * @return List<Pj07DTO>
     */
    List<Pj07Page> getPageList(Map<String, Object> params);
}
