package io.renren.modules.enterprise.pj01GeneralParams.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 项目通用配置
 *
 * <AUTHOR>
 * @since 1.0.0 2025-04-22
 */
@Data
@ApiModel(value = "项目通用配置")
public class BPj01GeneralParamsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目id")
    private Long pj0101;
    @ApiModelProperty(value = "项目名称")
    private String name;
    @ApiModelProperty(value = "定时退场天数参数")
    private Integer exitdayparam;
    @ApiModelProperty(value = "定时退场开关")
    private String exitdayswitch;

}