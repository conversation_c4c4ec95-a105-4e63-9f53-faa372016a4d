package io.renren.modules.supervision.equipmentStatistical.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.dao.BaseDao;
import io.renren.modules.supervision.equipmentStatistical.dto.EquipmentStatisticalDTO;
import io.renren.modules.supervision.equipmentStatistical.dto.OutEquipmentDTO;
import io.renren.modules.supervision.equipmentStatistical.entity.EquipmentStatisticalEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @description: 设备统计
 * @className: EquipmentStatisticalDao
 * @author: lrl
 * @date: 2021-12-28 13:34
 **/
@Mapper
public interface EquipmentStatisticalDao extends BaseDao<EquipmentStatisticalEntity> {
    /**
     * 地区设备统计分页数据
     * @param params 查询参数
     * @return 地区设备统计分页数据
     */
    List<EquipmentStatisticalDTO> getAreaCodePageData(Map<String, Object> params);
    /**
     * 离线设备分页数据
     * @param page 分页参数
     * @param params 查询参数
     * @return 离线设备分页数据
     */
    List<OutEquipmentDTO> getOutEquipmentPageData(Page<OutEquipmentDTO> page, Map<String, Object> params);
}
