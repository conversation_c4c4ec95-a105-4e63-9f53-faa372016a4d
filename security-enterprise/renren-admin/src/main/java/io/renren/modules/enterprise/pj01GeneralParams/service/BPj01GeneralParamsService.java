package io.renren.modules.enterprise.pj01GeneralParams.service;

import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.pj01.dto.Pj01DTO;
import io.renren.modules.enterprise.pj01GeneralParams.dto.BPj01GeneralParamsDTO;
import io.renren.modules.enterprise.pj01GeneralParams.entity.BPj01GeneralParamsEntity;

import java.util.List;

/**
 * 项目通用配置
 *
 * <AUTHOR>
 * @since 1.0.0 2025-04-22
 */
public interface BPj01GeneralParamsService extends CrudService<BPj01GeneralParamsEntity, BPj01GeneralParamsDTO> {

    /**
     * 获取项目通用配置信息
     * @param id 项目id
     * @return BPj01GeneralParamsDTO
     */
    BPj01GeneralParamsDTO getGeneralParamsInfo(Long id);

    /**
     * 保存项目通用配置信息
     * @param dto BPj01GeneralParamsDTO
     */
    void saveInfo(BPj01GeneralParamsDTO dto);

    /**
     * 更新项目通用配置信息
     * @param dto BPj01GeneralParamsDTO
     */
    void updateInfo(BPj01GeneralParamsDTO dto);

    /**
     * 获取项目退场工人配置
     * @return List<BPj01GeneralParamsDTO>
     */
    List<BPj01GeneralParamsDTO> getWorkerExitProject();
}