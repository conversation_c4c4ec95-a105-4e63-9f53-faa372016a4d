package io.renren.modules.supervision.equipmentStatistical.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.supervision.equipmentStatistical.dto.EquipmentStatisticalDTO;
import io.renren.modules.supervision.equipmentStatistical.dto.OutEquipmentDTO;
import io.renren.modules.supervision.equipmentStatistical.service.EquipmentStatisticalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * @description 设备统计
 *
 * @className: AttStatisticalController
 * @author: lrl
 * @date: 2021-12-28 13:15
 **/
@RestController
@RequestMapping("equipmentStatistical/equipmentStatistical")
@Api(tags = "设备统计")
public class EquipmentStatisticalController {

    @Autowired
    private EquipmentStatisticalService equipmentStatisticalService;


    /**
     * 地区设备统计分页数据
     * @param params 参数
     * @return 地区设备统计分页数据
     */
    @GetMapping("areaCodePage")
    @ApiOperation("地区设备统计分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "areaCode", value = "行政区划",  required = true, dataType = "String"),
    })
    @RequiresPermissions("equipmentStatistical:equipmentStatistical:areaCodePage")
    public Result<List<EquipmentStatisticalDTO>> areaCodePage(@ApiIgnore @RequestParam Map<String, Object> params) {

        List<EquipmentStatisticalDTO> page = equipmentStatisticalService.getAreaCodePageData(params);

        return new Result<List<EquipmentStatisticalDTO>>().ok(page);
    }


    /**
     * 离线设备分页数据
     * @param params 参数
     * @return 离线设备分页数据
     */
    @GetMapping("outEquipmentPage")
    @ApiOperation("离线设备分页数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = "areaCode", value = "行政区划",  required = true, dataType = "String"),
            @ApiImplicitParam(name = "projectName", value = "项目名称",   dataType = "String"),
            @ApiImplicitParam(name = "equipmentNo", value = "设备序列号",   dataType = "String"),
    })
    @RequiresPermissions("equipmentStatistical:equipmentStatistical:page")
    public Result<PageData<OutEquipmentDTO>> outEquipmentPage(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<OutEquipmentDTO> page = equipmentStatisticalService.outEquipmentPage(params);

        return new Result<PageData<OutEquipmentDTO>>().ok(page);
    }
}
