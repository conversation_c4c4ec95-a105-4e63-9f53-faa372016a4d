package io.renren.modules.enterprise.rpj01dept.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.rpj01dept.dto.InsertRpdDTO;
import io.renren.modules.enterprise.rpj01dept.dto.Pj0101NameDTO;
import io.renren.modules.enterprise.rpj01dept.entity.RPj01DeptEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目和机构的关系表
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-05-11
 */
@Mapper
public interface RPj01DeptDao extends BaseDao<RPj01DeptEntity> {

//    /**
//     * 添加项目和机构的关系
//     */
//    int insertByInsertRpdDTO(InsertRpdDTO dto);

    /**
     * 获取项目名称和id
     */
    List<Pj0101NameDTO> getPj0101List();
    /**
     * 验证是否已存在数据
     */
    int getCountByRPj01DeptDTO( RPj01DeptEntity rPj01DeptEntity);

    /**
     * 获取此机构下的所有项目id
     */
    List<String> getOwnPj0101List(Long deptId);

    /**
     * 删除不在此次绑定关系的数据
     */
    int deleteByInsertRpdDTO(InsertRpdDTO insertRpdDTO);
}