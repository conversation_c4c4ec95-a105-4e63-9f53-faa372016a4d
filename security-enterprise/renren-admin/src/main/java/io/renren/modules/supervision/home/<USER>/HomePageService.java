package io.renren.modules.supervision.home.service;

import io.renren.modules.supervision.home.dto.StatisticsAttendance;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/*
 * @Author(作者) 王崇人
 * @Description(类的描述): 监管PC端首页接口
 *：@version（开发的版本）：1.0
 * @Date(创建时间)2022-06-20 15:26
 */
public interface HomePageService {
    /**
     * 获取当前用户所属区域
     * @return 行政区域
     */
    String getCurrentArea();
    /**
     * 获取当前登录用户所属行政区划下的项目总数
     * @return 项目总数
     */
    Long getProjectSum();

    /**
     * 获取当前登录用户所属行政区划下的在建项目总数
     * @return 在建项目总数
     */
    Long getConstructionProjects();

    /**
     * 获取当前登录用户所属行政区划下的项目管理人员总数
     * @return 管理人员总数
     */
    Long getManagerSum();

    /**
     * 获取当前登录用户所属行政区划下的建筑工人总数
     * @return 建筑工人总数
     */
    Long getWorkerSum();

    /**
     * 获取当前登录用户所属行政区划下的今日考勤
     * @return 今日考勤人员总数
     */
    Long getPersonAttendance();

    /**
     * 获取当前登录用户所属行政区划下的最近七天考勤统计
     * @return 最近七天的考勤
     */
    List<StatisticsAttendance> statisticsAttendance();






}
