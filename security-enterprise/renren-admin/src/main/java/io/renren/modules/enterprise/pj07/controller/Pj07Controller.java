package io.renren.modules.enterprise.pj07.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.modules.enterprise.pj07.dto.Pj07DTO;
import io.renren.modules.enterprise.pj07.dto.Pj07Page;
import io.renren.modules.enterprise.pj07.service.Pj07Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.formula.functions.T;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/7 9:40
 */
@RestController
@RequestMapping("enterprise/pj07")
@Api(tags = "项目核查情况")
public class Pj07Controller {
    @Autowired
    private Pj07Service pj07Service;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "projectName", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "areaCode", value = "行政区划", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "projectStatus", value = "项目状态", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "whether", value = "核查状态", paramType = "query", dataType = "String")
    })
    @RequiresPermissions("enterprise:pj07:page")
    public Result<PageData<Pj07Page>> page(@ApiIgnore @RequestParam Map<String, Object> params) {

        PageData<Pj07Page> page = pj07Service.pageList(params);

        return new Result<PageData<Pj07Page>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:pj07:info")
    public Result<Pj07DTO> get(@PathVariable("id") Long id) {

        Pj07DTO data = pj07Service.get(id);

        return new Result<Pj07DTO>().ok(data);
    }

    @PostMapping("saveOrUpdate")
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("enterprise:pj07:saveOrUpdate")
    public Result<T> saveOrUpdate(@RequestBody Pj07DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj07Service.saveOrUpdate(dto);

        return new Result<>();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:pj07:update")
    public Result<T> update(@RequestBody Pj07DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        pj07Service.update(dto);

        return new Result<>();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("enterprise:pj07:delete")
    public Result<T> delete(@RequestBody Long[] ids) {
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        pj07Service.delete(ids);

        return new Result<>();
    }
}
