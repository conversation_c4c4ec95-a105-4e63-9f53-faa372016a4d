package io.renren.modules.enterprise.dataShare.facilitatorProject.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 第三方服务商和项目的关系
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-22
 */
@Data
@ApiModel(value = "企业已绑定的项目")
public class FacilitatorProjectInfo implements Serializable {

    private static final long serialVersionUID = 7003642930091515009L;

    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

}