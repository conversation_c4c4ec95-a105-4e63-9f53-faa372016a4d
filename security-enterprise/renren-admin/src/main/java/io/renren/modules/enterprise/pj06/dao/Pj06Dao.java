package io.renren.modules.enterprise.pj06.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj06.entity.Pj06Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目注册信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-25
 */
@Mapper
public interface Pj06Dao extends BaseDao<Pj06Entity> {

    List<Pj06Entity> getPageData(Map<String, Object> params);

    /**
     * 查询未审核的项目
     *
     * @param name     项目名称
     * @param areaCode 行政区划
     * @return Long
     */
    Long getProjectNumber(@Param("name") String name, @Param("areaCode") String areaCode);

    /**
     * 查询PJ01是否存在项目
     *
     * @param name     项目名称
     * @param areaCode 行政区划
     * @return Long
     */
    Long getPj01Number(@Param("name") String name, @Param("areaCode") String areaCode);

    /**
     * 查询当前区域下边账号的最大值
     *
     * @param areaCode 行政区划
     * @return String
     */
    String selectUserNameMax(String areaCode);

    /**
     * 保存项目和银行的关系
     *
     * @param pj0101      项目ID
     * @param payBankCode 银行代码
     */
    void saveProjectBankRelation(@Param("pj0101") Long pj0101, @Param("payBankCode") String payBankCode);

    /**
     * 调用关系表储存过程
     */
    void callProjectRelation();
}