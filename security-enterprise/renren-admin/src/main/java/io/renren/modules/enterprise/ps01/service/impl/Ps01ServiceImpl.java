package io.renren.modules.enterprise.ps01.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ConvertUtils;
import io.renren.modules.enterprise.ps01.dao.Ps01Dao;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.renren.modules.enterprise.ps01.entity.Ps01Entity;
import io.renren.modules.enterprise.ps01.service.Ps01Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 人员实名基础信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Service
public class Ps01ServiceImpl extends ServiceImpl<Ps01Dao, Ps01Entity> implements Ps01Service {

    @Autowired
    private Ps01Dao ps01Dao;

    @Override
    public Ps01DTO selectByIdCardNumber(String idCardNumber) {
        return ps01Dao.loadPs01(idCardNumber.toUpperCase(Locale.ROOT));
    }

    @Override
    public PageData<Ps01DTO> pageList(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        Page<Ps01DTO> page = new Page<>(curPage, limit);
        List<Ps01DTO> list = ps01Dao.getList(page, params);
        return getPageData(list, page.getTotal(), Ps01DTO.class);
    }

    @Override
    public Ps01DTO getPs01Info(Long id) {
        Ps01Entity ps01Entity = ps01Dao.selectById(id);
        return ConvertUtils.sourceToTarget(ps01Entity, Ps01DTO.class);
    }

    @Override
    public void savePs01Info(Ps01DTO dto) {
        Ps01Entity ps01Entity = ConvertUtils.sourceToTarget(dto, Ps01Entity.class);
        ps01Dao.insert(ps01Entity);
    }

    @Override
    public void updatePs01Info(Ps01DTO dto) {
        Ps01Entity ps01Entity = ConvertUtils.sourceToTarget(dto, Ps01Entity.class);
        ps01Dao.updateById(ps01Entity);
    }

    @Override
    public Ps01DTO getPersonByBusinessId(Long id) {
        return ps01Dao.selectPersonByBusinessId(id);
    }

    protected PageData<Ps01DTO> getPageData(List<Ps01DTO> list, long total, Class<Ps01DTO> ps01DTOClass) {
        List<Ps01DTO> targetList = ConvertUtils.sourceToTarget(list, ps01DTOClass);
        return new PageData<>(targetList, total);
    }
}