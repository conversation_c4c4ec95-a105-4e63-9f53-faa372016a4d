package io.renren.modules.supervision.map.controller;

import io.renren.common.utils.Result;
import io.renren.modules.supervision.map.dto.ProjectMapDTO;
import io.renren.modules.supervision.map.service.ProjectMapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/20 8:59
 */
@RestController
@RequestMapping("supervision/map")
@Api(tags = "GIS地图信息")
public class ProjectMapController {
    @Autowired
    private ProjectMapService projectMapService;

    @GetMapping("projectInfo")
    @ApiOperation("项目信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "areaCode", value = "行政区划", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "projectName", value = "项目名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "projectStatus", value = "项目状态", paramType = "query", required = true, dataType = "String")
    })
    public Result<List<ProjectMapDTO>> info(@ApiIgnore @RequestParam Map<String, Object> params) {
        List<ProjectMapDTO> list = projectMapService.getProjectInfoList(params);
        return new Result<List<ProjectMapDTO>>().ok(list);
    }
}
