package io.renren.modules.enterprise.yj01.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.yj01.dto.ManagerDTO;
import io.renren.modules.enterprise.yj01.dto.ProjectKeyPositInfo;
import io.renren.modules.enterprise.yj01.entity.Yj01Entity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 实名制预警信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
@Mapper
public interface Yj01Dao extends BaseDao<Yj01Entity> {
    /**
     * 查询在建项目
     *
     * @return List<Long>
     */
    List<Long> selectProjectInfo();

    /**
     * 查询管理人员信息
     *
     * @param id       pj0101
     * @param partType 参建单位类型
     * @param jobType  岗位类型
     * @return ManagerDTO
     */
    ManagerDTO selectManager(@Param("id") Long id, @Param("partType") String partType, @Param("jobType") String jobType);

    /**
     * 查询人员工时
     * @param id 人员ID
     * @return String(工时)
     */
    String selectByWorkHoursById(String id);

    /**
     * 根据项目ID查询预警数据
     * @param id pj0101
     * @return Yj01Entity
     */
    Yj01Entity selectInfo(Long id);

    /**
     * 分页数据查询
     * @param params Map<String, Object>
     * @return List<ProjectKeyPositInfo>
     */
    List<ProjectKeyPositInfo> getList(Map<String, Object> params);

    /**
     * 查询预警内容
     * @param pj0101 项目ID
     * @return String
     */
    String selectWarningContent(Long pj0101);
}