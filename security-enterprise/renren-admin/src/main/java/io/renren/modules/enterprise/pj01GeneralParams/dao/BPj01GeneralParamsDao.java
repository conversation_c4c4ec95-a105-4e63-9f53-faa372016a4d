package io.renren.modules.enterprise.pj01GeneralParams.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.pj01GeneralParams.dto.BPj01GeneralParamsDTO;
import io.renren.modules.enterprise.pj01GeneralParams.entity.BPj01GeneralParamsEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 项目通用配置
 *
 * <AUTHOR>
 * @since 1.0.0 2025-04-22
 */
@Mapper
public interface BPj01GeneralParamsDao extends BaseDao<BPj01GeneralParamsEntity> {

    /**
     * 分页查询
     * @param params
     * @return
     */
    List<BPj01GeneralParamsDTO> getListData(Map<String, Object> params);

    /**
     * 获取项目通用配置信息
     * @param pj0101
     * @return
     */
    BPj01GeneralParamsDTO getGeneralParamsInfo(Long pj0101, Long deptId);

    /**
     * 获取项目退场工人配置
     * @return
     */
    List<BPj01GeneralParamsDTO> getWorkerExitProject();
}