package io.renren.modules.enterprise.ps09.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.ps01.dto.Ps01DTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@ApiModel(value = "项目关键岗位审核信息")
public class Ps09DTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long ps0901;

    @ApiModelProperty(value = "项目ID")
    @NotNull(message = "pj0101不能为空")
    private Long pj0101;

    @ApiModelProperty(value = "企业ID")
    @NotNull(message = "cp0201不能为空")
    private Long cp0201;

    @ApiModelProperty(value = "人员ID")
    @NotNull(message = "ps0101不能为空")
    private Long ps0101;

    @ApiModelProperty(value = "岗位类型")
    @NotBlank(message = "岗位类型不能为空")
    private String jobtype;

    @ApiModelProperty(value = "头像采集照片")
    @NotBlank(message = "头像信息不能为空")
    private String photo;

    @ApiModelProperty(value = "是否购买工伤或意外伤害保险")
    private String hasbuyinsurance;

    @ApiModelProperty(value = "进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date entrytime;

    @ApiModelProperty(value = "进退场状态")
    private String inOrOut;

    @ApiModelProperty(value = "审核状态", required = true)
    @NotBlank(message = "审核状态不能为空")
    private String state;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date auditDate;

    @ApiModelProperty(value = "审核不通过原因")
    private String notPassReasons;

    @ApiModelProperty(value = "人员基础信息")
    @Valid
    private Ps01DTO ps01DTO;

    @ApiModelProperty(value = "合同附件")
    @Valid
    private List<Ot01DTO> ot01DTOList;
}