package io.renren.modules.supervision.projectStatistic.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.renren.common.dao.BaseDao;
import io.renren.modules.supervision.projectStatistic.dto.ProjectStatDTO;
import io.renren.modules.supervision.projectStatistic.dto.ProjectStatPageDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-12-28 13:09
 */
@Mapper
public interface ProjectStatDao extends BaseDao<ProjectStatDTO> {
    /**
     * 查询项目统计数据
     *
     * @param params Map<String, Object> params
     * @return List<ProjectStatDTO>
     */
    List<ProjectStatDTO> selectDataList(Map<String, Object> params);

    /**
     * 项目信息列表
     *
     * @param page   page
     * @param params Map<String, Object>
     * @return List
     */
    List<ProjectStatPageDTO> selectPageList(@Param("page") Page<ProjectStatPageDTO> page, @Param("params") Map<String, Object> params);
}
