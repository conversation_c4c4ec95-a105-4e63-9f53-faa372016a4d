package io.renren.modules.enterprise.excelexp.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * DO 考勤记录表对象
 *
 * @className: WorkerRecordDTO
 * @author: lrl
 * @date: 2021-05-18 10:36
 **/
@Data
@ApiModel(value = "月份的每一天")
public class MonthDayDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String dayId;

    private String dayName;

    private String unId;

    private String hours;

}
