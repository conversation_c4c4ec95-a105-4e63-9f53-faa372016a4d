package io.renren.modules.enterprise.ps05.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目管理人员信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("B_PS05")
public class Ps05Entity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.INPUT)
    private Long ps0501;
    /**
     * guid
     */
    private String guid;
    /**
     * imageGuid
     */
    private String imageGuid;
    /**
     * createDate
     */
    private Date createDate;
    /**
     * 人员类型
     */
    private String personType;

}