package io.renren.modules.enterprise.pj01GeneralParams.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目通用配置
 *
 * <AUTHOR>
 * @since 1.0.0 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PJ01_GENERAL_PARAMS")
public class BPj01GeneralParamsEntity  {
	private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
	@NotNull(message="项目id不能为空")
	private Long pj0101;
    /**
     * 定时退场天数参数
     */
	@NotNull(message="退场参数不能为空")
	private Integer exitdayparam;
    /**
     * 定时退场开关
     */
	@NotNull(message="退场开关不能为空")
	private String exitdayswitch;

}