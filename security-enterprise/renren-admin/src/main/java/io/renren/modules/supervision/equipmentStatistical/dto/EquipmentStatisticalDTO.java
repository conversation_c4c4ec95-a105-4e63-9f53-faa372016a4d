package io.renren.modules.supervision.equipmentStatistical.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description
 * @className: attStatisticalDTO
 * @author: lrl
 * @date: 2021-12-28 13:38
 **/
@Data
public class EquipmentStatisticalDTO implements Serializable {


    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "行政区划")
    private String areaCode;

    @ApiModelProperty(value = "行政区划名")
    private String areaName;

    @ApiModelProperty(value = "总设备数")
    private String totalEquipmentNum;

    @ApiModelProperty(value = "在线设备数")
    private String inEquipmentNum;

    @ApiModelProperty(value = "离线设备数")
    private String outEquipmentNum;
}
