package io.renren.modules.enterprise.pj01info.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.ot01.dto.Ot01DTO;
import io.renren.modules.enterprise.pa01.entity.Pa01Entity;
import io.renren.modules.enterprise.pj01info.entity.Pj01InfoEntity;
import io.renren.modules.enterprise.pj01info.dto.*;

import java.util.List;
import java.util.Map;

/**
 * 项目基础信息表
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-26
 */
public interface Pj01InfoService extends CrudService<Pj01InfoEntity, Pj01InfoDTO> {
    /**
     * 查询列表数据
     * @param params
     * @return
     */
    PageData<Pj01InfoDTO> selectListData(Map<String, Object> params);

    /**
     * 查询项目及施工许可证
     * @param id
     * @return
     */
    Pj01InfoDTO getProjectInfo(Long id);

    /**
     * 获取项目的专户
     * @param pj0101 项目id
     * @return
     */
    Pa01Entity getSpecialAccount(Long pj0101);

    /**
     * 项目查询获取参建单位
     * @param params
     * @return
     */
    PageData<Cp02PageDTO> cp02PageList(Map<String, Object> params);

    /**
     * 项目查询获取班组
     * @param params
     * @return
     */
    PageData<Tm01PageDTO> teamPageList(Map<String, Object> params);

    /**
     * 项目查询获取工人
     * @param params
     * @return
     */
    PageData<Ps02PageDTO> workerPageList(Map<String, Object> params);

    /**
     * 项目查询获取考勤
     * @param params
     * @return
     */
    PageData<Kq02PageDTO> attendancePageList(Map<String, Object> params);

    /**
     * 项目查询获取管理人员
     * @param params
     * @return
     */
    PageData<Ps04PageDTO> managerPageList(Map<String, Object> params);

    /**
     * 查询项目施工许可证或合同文件文件
     * @param pj0101 项目名称
     * @param type 文件类型
     * @return
     */
    List<Ot01DTO> getBusinessData(Long pj0101, String type);
}