package io.renren.modules.enterprise.dataShare.facilitatorOperation.service;

import io.renren.common.page.PageData;
import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.dataShare.facilitatorOperation.dto.FacilitatorOperationDTO;
import io.renren.modules.enterprise.dataShare.facilitatorOperation.entity.FacilitatorOperationEntity;

import java.util.Map;

/**
 * 数据共享日志表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-22
 */
public interface FacilitatorOperationService extends CrudService<FacilitatorOperationEntity, FacilitatorOperationDTO> {

    /**
     * 分页查询
     *
     * @param params Map<String, Object>
     * @return PageData<FacilitatorOperationDTO>
     */
    PageData<FacilitatorOperationDTO> pageInfo(Map<String, Object> params);
}