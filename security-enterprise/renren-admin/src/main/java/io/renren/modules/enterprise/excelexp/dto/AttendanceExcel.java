package io.renren.modules.enterprise.excelexp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/9 14:14
 */
@Data
@ApiModel(value = "考勤表导出")
public class AttendanceExcel implements Serializable {
    private static final long serialVersionUID = -2826258973306473711L;
    @ApiModelProperty(value = "人员ID")
    private Long userid;

    @ApiModelProperty(value = "班组名称")
    private String teamname;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证号码")
    private String idcardnumber;

    @ApiModelProperty(value = "工资卡账号")
    private String payRollBankCardNumber;

    @ApiModelProperty(value = "开户行名称")
    private String payRollBankName;

    @ApiModelProperty(value = "出勤")
    private String isAtt;

    @ApiModelProperty(value = "工时")
    private String workingHours;

    @ApiModelProperty(value = "考勤天数")
    private String attTotal;

    @ApiModelProperty(value = "总工时")
    private String hoursTotal;

    private String day1;

    private String hours1;

    private String day2;

    private String hours2;

    private String day3;

    private String hours3;

    private String day4;

    private String hours4;

    private String day5;

    private String hours5;

    private String day6;

    private String hours6;

    private String day7;

    private String hours7;

    private String day8;

    private String hours8;

    private String day9;

    private String hours9;

    private String day10;

    private String hours10;

    private String day11;

    private String hours11;

    private String day12;

    private String hours12;

    private String day13;

    private String hours13;

    private String day14;

    private String hours14;

    private String day15;

    private String hours15;

    private String day16;

    private String hours16;

    private String day17;

    private String hours17;

    private String day18;

    private String hours18;

    private String day19;

    private String hours19;

    private String day20;

    private String hours20;

    private String day21;

    private String hours21;

    private String day22;

    private String hours22;

    private String day23;

    private String hours23;

    private String day24;

    private String hours24;

    private String day25;
    private String hours25;

    private String day26;

    private String hours26;

    private String day27;

    private String hours27;

    private String day28;

    private String hours28;

    private String day29;

    private String hours29;

    private String day30;

    private String hours30;

    private String day31;

    private String hours31;

    private String counts;
}
