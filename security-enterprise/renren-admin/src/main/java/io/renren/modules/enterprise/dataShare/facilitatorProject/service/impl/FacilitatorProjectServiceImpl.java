package io.renren.modules.enterprise.dataShare.facilitatorProject.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dao.FacilitatorProjectDao;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dto.FacilitatorProjectDTO;
import io.renren.modules.enterprise.dataShare.facilitatorProject.dto.FacilitatorProjectInfo;
import io.renren.modules.enterprise.dataShare.facilitatorProject.entity.FacilitatorProjectEntity;
import io.renren.modules.enterprise.dataShare.facilitatorProject.service.FacilitatorProjectService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 第三方服务商和项目的关系
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-03-22
 */
@Service
public class FacilitatorProjectServiceImpl extends CrudServiceImpl<FacilitatorProjectDao, FacilitatorProjectEntity, FacilitatorProjectDTO> implements FacilitatorProjectService {

    @Override
    public QueryWrapper<FacilitatorProjectEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");
        QueryWrapper<FacilitatorProjectEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }


    @Override
    public List<FacilitatorProjectInfo> getRelatedProjectList(Map<String, Object> params) {
        return baseDao.selectByFacilitatorId(params);
    }
}