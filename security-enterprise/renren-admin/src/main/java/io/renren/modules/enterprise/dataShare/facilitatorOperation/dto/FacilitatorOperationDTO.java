package io.renren.modules.enterprise.dataShare.facilitatorOperation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据共享日志表
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-22
 */
@Data
@ApiModel(value = "数据共享日志表")
public class FacilitatorOperationDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户操作")
    private String operation;

    @ApiModelProperty(value = "请求URI")
    private String requestUri;

    @ApiModelProperty(value = "请求方式")
    private String requestMethod;

    @ApiModelProperty(value = "请求参数")
    private String requestParams;

    @ApiModelProperty(value = "请求时长(毫秒)")
    private Long requestTime;

    @ApiModelProperty(value = "用户代理")
    private String userAgent;

    @ApiModelProperty(value = "操作IP")
    private String ip;

    @ApiModelProperty(value = "状态  0：失败   1：成功")
    private Short status;

    @ApiModelProperty(value = "唯一标识")
    private String appKey;

    @ApiModelProperty(value = "请求时间")
    private Date createDate;

}