package io.renren.modules.supervision.home.service.impl;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.supervision.home.dao.HomeDao;
import io.renren.modules.supervision.home.dto.StatisticsAttendance;
import io.renren.modules.supervision.home.service.HomePageService;
import io.renren.modules.supervision.home.service.HomePageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * @Author(作者) 王崇人
 * @Description(类的描述): 金筑工监管首页PC端接口实现类
 *：@version（开发的版本）：1.0
 * @Date(创建时间)2022-06-20 16:05
 */
@Service
public class HomePageServiceImpl implements HomePageService {

    @Autowired
    private HomeDao homeDao;

    @Override
    public String getCurrentArea() {
        Long deptId = SecurityUser.getDeptId();
        String currentArea= homeDao.getCurrentArea(deptId);
        return currentArea;
    }

    @Override
    public Long getProjectSum() {
        Long deptId = SecurityUser.getDeptId();
        Long projectSum = homeDao.getProjectSum(deptId);
        if (ObjectUtil.isNotEmpty(projectSum)){
            return projectSum;
        }
        //如果项目数量是空，就返回0
        projectSum = Long.valueOf(0);
        return projectSum;
    }

    @Override
    public Long getConstructionProjects() {
        Long deptId = SecurityUser.getDeptId();
        Long projectSum = homeDao.getConstructionProjects(deptId);
        if (ObjectUtil.isNotEmpty(projectSum)){
            return projectSum;
        }
        projectSum = Long.valueOf(0);
        return projectSum;
    }

    @Override
    public Long getManagerSum() {
        Long deptId = SecurityUser.getDeptId();
        Long managerSum = homeDao.getManagerSum(deptId);
        if (ObjectUtil.isNotEmpty(managerSum)){
            return managerSum;
        }
        managerSum = Long.valueOf(0);
        return managerSum;
    }

    @Override
    public Long getWorkerSum() {
        Long deptId = SecurityUser.getDeptId();
        Long workerSum = homeDao.getWorkerSum(deptId);
        if (ObjectUtil.isNotEmpty(workerSum)){
            return workerSum;
        }
        workerSum = Long.valueOf(0);
        return workerSum;
    }

    @Override
    public Long getPersonAttendance() {
        Long deptId = SecurityUser.getDeptId();
        Long sum = homeDao.getPersonAttendance(deptId);
        return sum;
    }

    @Override
    public List<StatisticsAttendance> statisticsAttendance() {
        Long deptId = SecurityUser.getDeptId();
        return homeDao.statisticsAttendance(deptId);
    }
}
