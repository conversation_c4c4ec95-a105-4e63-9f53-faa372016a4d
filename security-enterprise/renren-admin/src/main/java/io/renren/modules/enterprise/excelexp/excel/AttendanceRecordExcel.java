package io.renren.modules.enterprise.excelexp.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * DO 考勤记录表对象
 *
 * @className: WorkerRecordDTO
 * @author: lrl
 * @date: 2021-05-18 10:36
 **/
@Data
@ApiModel(value = "考勤记录表对象")
public class AttendanceRecordExcel implements Serializable {

    @Excel(name = "班组名称")
    private String teamname;

    @Excel(name = "姓名")
    private String name;

    @Excel(name = "身份证号码")
    private String idcardnumber;

    @Excel(name = "1" )
    private String day1;

    @Excel(name = "2")
    private String day2;

    @Excel(name = "3")
    private String day3;

    @Excel(name = "4")
    private String day4;

    @Excel(name = "5")
    private String day5;

    @Excel(name = "6")
    private String day6;

    @Excel(name = "7")
    private String day7;

    @Excel(name = "8")
    private String day8;

    @Excel(name = "9")
    private String day9;

    @Excel(name = "10")
    private String day10;

    @Excel(name = "11")
    private String day11;

    @Excel(name = "12")
    private String day12;

    @Excel(name = "13")
    private String day13;

    @Excel(name = "14")
    private String day14;

    @Excel(name = "15")
    private String day15;

    @Excel(name = "16")
    private String day16;

    @Excel(name = "17")
    private String day17;

    @Excel(name = "18")
    private String day18;

    @Excel(name = "19")
    private String day19;

    @Excel(name = "20")
    private String day20;

    @Excel(name = "21")
    private String day21;

    @Excel(name = "22")
    private String day22;

    @Excel(name = "23")
    private String day23;

    @Excel(name = "24")
    private String day24;

    @Excel(name = "25")
    private String day25;

    @Excel(name = "26")
    private String day26;

    @Excel(name = "27")
    private String day27;

    @Excel(name = "28")
    private String day28;

    @Excel(name = "29")
    private String day29;

    @Excel(name = "30")
    private String day30;

    @Excel(name = "31")
    private String day31;

    @Excel(name = "合计天数")
    private String counts;


}
