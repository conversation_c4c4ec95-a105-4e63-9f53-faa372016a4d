/**
 * Copyright (c) 2019 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package io.renren.modules.enterprise.projectledger.entity;

import lombok.Data;

import java.io.Serializable;

/**
 *  台账对象
 *
 * <AUTHOR> <PERSON>@gmail.com
 */
@Data
public class ProjectLedgerEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long pj0101;

    private String projectName;

    private String areaCode;
    /**
     *联系人
     */
    private String linkMan;
    /**
     *联系电话
     */
    private String linkCellphone;
    /**
    *设备数量
    */
    private String equipmentNum;

//总包单位情况
    /**
     *在场工人
     */
    private String workerInNumZb;
    /**
     *工人考勤数
     */
    private String workerAttendanceNumZb;
    /**
     *工人考勤率
     */
    private String workerAttendanceRateZb;
    /**
     *总在场管理人员
     */
    private String managerInNumZb;
    /**
     *管理人员考勤数
     */
    private String managerAttendanceNumZb;
    /**
     *管理人员考勤率
     */
    private String managerAttendanceRateZb;
    /**
     *项目经理到岗率
     */
    private String projectManagerAttendanceRateZb;

 //监理单位情况
    /**
     *在场工人
     */
    private String workerInNumJl;
    /**
     *工人考勤数
     */
    private String workerAttendanceNumJl;
    /**
     *工人考勤率
     */
    private String workerAttendanceRateJl;
    /**
     *总在场管理人员
     */
    private String managerInNumJl;
    /**
     *管理人员考勤数
     */
    private String managerAttendanceNumJl;
    /**
     *管理人员考勤率
     */
    private String managerAttendanceRateJl;
    /**
     *总监理考到岗率
     */
    private String supervisorAttendanceRateJl;

//建设单位情况
    /**
     *在场工人
     */
    private String workerInNumJs;
    /**
     *工人考勤数
     */
    private String workerAttendanceNumJs;
    /**
     *工人考勤率
     */
    private String workerAttendanceRateJs;
    /**
     *总在场管理人员
     */
    private String managerInNumJs;
    /**
     *管理人员考勤数
     */
    private String managerAttendanceNumJs;
    /**
     *管理人员考勤率
     */
    private String managerAttendanceRateJs;
//其他单位情况
    /**
     *在场工人
     */
    private String workerInNumQt;
    /**
     *工人考勤数
     */
    private String workerAttendanceNumQt;
    /**
     *工人考勤率
     */
    private String workerAttendanceRateQt;
    /**
     *总在场管理人员
     */
    private String managerInNumQt;
    /**
     *管理人员考勤数
     */
    private String managerAttendanceNumQt;
    /**
     *管理人员考勤率
     */
    private String managerAttendanceRateQt;

//项目总体情况
    /**
     *总在场工人
     */
    private String totalWorkerInNum;
    /**
     *工人考勤数
     */
    private String totalWorkerAttendanceNum;
    /**
     *工人考勤率
     */
    private String totalWorkerAttendanceRate;
    /**
     *总在场管理人员
     */
    private String totalManagerInNum;
    /**
     *管理人员考勤数
     */
    private String totalManagerAttendanceNum;
    /**
     *管理人员考勤率
     */
    private String totalManagerAttendanceRate;



}
