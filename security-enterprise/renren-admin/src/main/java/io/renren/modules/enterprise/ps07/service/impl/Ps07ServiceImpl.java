package io.renren.modules.enterprise.ps07.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.ps07.dao.Ps07Dao;
import io.renren.modules.enterprise.ps07.dto.Ps07DTO;
import io.renren.modules.enterprise.ps07.entity.Ps07Entity;
import io.renren.modules.enterprise.ps07.service.Ps07Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 项目管理人员进退场信息
 *
 * <AUTHOR> 
 * @since 1.0.0 2021-05-06
 */
@Service
public class Ps07ServiceImpl extends CrudServiceImpl<Ps07Dao, Ps07Entity, Ps07DTO> implements Ps07Service {

    @Override
    public QueryWrapper<Ps07Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<Ps07Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


}