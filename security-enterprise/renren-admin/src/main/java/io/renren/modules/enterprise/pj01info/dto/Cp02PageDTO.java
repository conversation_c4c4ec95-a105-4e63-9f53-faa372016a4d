package io.renren.modules.enterprise.pj01info.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 参建单位列表分页信息
 * <p>
 * 关于列表查询如果出现多个表的字段信息需要展示，建议新写一个DTO,
 * 专门进行列表数据的展示，不和实际的业务DTO产生冲突,方便维护
 * </p>
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "参建单位分页列表信息")
public class Cp02PageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", hidden = true)
    private Long cp0201;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "企业名称")
    private String corpname;

    @ApiModelProperty(value = "统一社会信用代码")
    private String corpcode;

    @ApiModelProperty(value = "联系人")
    private String linkman;

    @ApiModelProperty(value = "联系手机号码")
    private String linkcellphone;

    @ApiModelProperty(value = "参建类型")
    private String corptype;
}
