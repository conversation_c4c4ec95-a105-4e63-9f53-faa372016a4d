package io.renren.modules.enterprise.tj01.service;

import io.renren.common.service.CrudService;
import io.renren.modules.enterprise.tj01.dto.Tj01DTO;
import io.renren.modules.enterprise.tj01.entity.Tj01Entity;

import java.util.List;

/**
 * 住建厅数据写入情况
 *
 * <AUTHOR>
 * @since 1.0.0 2023-10-31
 */
public interface Tj01Service extends CrudService<Tj01Entity, Tj01DTO> {

    /**
     * 查询最近一周的数据
     *
     * @return List<Tj01DTO>
     */
    List<Tj01DTO> getLastWeekList();
}