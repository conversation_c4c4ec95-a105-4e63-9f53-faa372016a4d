package io.renren.modules.enterprise.ps02.strategy;

import cn.hutool.core.collection.ListUtil;
import io.renren.common.file.FileProper;
import io.renren.common.utils.SendDataUtil;
import io.renren.modules.enterprise.kq01.dao.Kq01Dao;
import io.renren.modules.enterprise.kq01.dto.Kq01DTO;
import io.renren.modules.enterprise.kq01.service.Kq01Service;
import io.renren.modules.enterprise.kq05.dao.Kq05Dao;
import io.renren.modules.enterprise.kq05.dto.PersonDTO;
import io.renren.modules.enterprise.kq05.service.Kq05Service;
import io.renren.modules.enterprise.ps02.dao.Ps02Dao;
import io.renren.modules.enterprise.ps02.dto.Ps02DTO;
import io.renren.modules.enterprise.ps04.dao.Ps04Dao;
import io.renren.modules.enterprise.ps06.service.BPs06Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人员数据下发策略模板方法
 * <p>
 * TODO：需要解决一个项目可能存在多设备类型，下发数据问题
 *
 * @version 1.0
 * @Description: TODO
 * <AUTHOR> chris
 * @Date : 2025/6/17 9:26
 **/
@Slf4j
@Component
public abstract class AbstractPs02IssuePersonHandleStrategy implements InitializingBean, IPs02IssuePersonToDevice {

    @Autowired
    protected Ps02Dao ps02Dao;

    @Autowired
    protected Ps04Dao ps04Dao;

    @Autowired
    protected Kq01Dao kq01Dao;

    @Autowired
    protected Kq05Dao kq05Dao;

    @Autowired
    protected FileProper fileProper;

    @Autowired
    protected Kq01Service kq01Service;

    @Autowired
    protected Kq05Service kq05Service;

    @Autowired
    protected BPs06Service ps06Service;

    @Autowired
    protected SendDataUtil sendDataUtil;

    /**
     * 销权
     *
     * @param pj0101     项目Id
     * @param userId     人员Id
     * @param personType 人员类型
     * @param kq01DTO    考勤设备
     */
    public void personSellRight(Long pj0101,
                                Long userId,
                                String personType,
                                Kq01DTO kq01DTO
    ) {
    }

    /**
     * 下发数据
     *
     * @param ps0201 人员id
     * @param pj0101 项目id
     */
    public void sendMq(Long ps0201, Long pj0101) {
        sendDataUtil.sendData(ps0201, pj0101);
    }

    /**
     * 下发人员数据
     *
     * @param person 人员信息
     */
    public void sendMq(Ps02DTO person) {
        sendDataUtil.sendData(person);
    }

    /**
     * 获取项目设备信息
     *
     * @param pj0101 项目id
     * @return
     */
    public List<Kq01DTO> getKq01DTOList(Long pj0101) {
        return kq01Service.selectByPj0101(pj0101);
    }

    /**
     * 查询人员信息
     *
     * @param userIds 人员id
     * @return
     */
    public List<PersonDTO> getWorkerList(List<Long> userIds) {
        return ps02Dao.selectPersonByIds(userIds);
    }

    /**
     * 查询人员信息
     *
     * @param userIds 人员id
     * @return
     */
    public List<PersonDTO> getManagerList(List<Long> userIds) {
        return ps04Dao.selectPersonByIds(userIds);
    }

    /**
     * 下发人员
     *
     * @param pj0101     项目id
     * @param userId     人员Id
     * @param name       姓名
     * @param imageUrl   人员图片
     * @param personType 人员类型
     */
    public void issuedWorkerPersonnel(Long pj0101,
                                      Long userId,
                                      String name,
                                      String imageUrl,
                                      String personType,
                                      String deviceType
    ) {

    }

    /**
     * 下发人员到设备
     *
     * @param pj0101         项目id
     * @param userId         人员Id
     * @param personType     人员类型
     * @param terminaltype   设备类型
     * @param cmdCode        指令
     * @param deviceserialno 设备序列号
     */
    public void issuedDeviceAddPerson(Long userId,
                                      Long pj0101,
                                      String personType,
                                      String terminaltype,
                                      String cmdCode,
                                      String deviceserialno) {

    }

    /**
     * 下发人员（重写）
     *
     * @param pj0101         项目id
     * @param userId         人员Id
     * @param personType     人员类型
     * @param terminaltype   设备类型
     * @param deviceserialno 设备序列号
     */
    public void personSellRight(Long pj0101,
                                Long userId,
                                String personType,
                                String terminaltype,
                                String deviceserialno) {

    }

    /**
     * 更新人员图片
     *
     * @param pj0101     项目id
     * @param userId     人员Id
     * @param personType 人员类型
     */
    public void updatePersonImage(Long pj0101, Long userId, String imageUrl, String personType, String deviceType) {

    }

    /**
     * 下发人员（工人）
     */
    public abstract void issuedWorkerPersonnel(List<Long> userIds, String personType);

    /**
     * 下发人员（管理人员）
     */
    public abstract void issuedManagerPersonnel(List<Long> userIds, String personType);

    /**
     * 转换成List
     *
     * @param userIds 人员ids
     * @return
     */
    protected List<Long> toList(Long[] userIds) {
        return ListUtil.of(userIds);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Ps02IssuePersonBusinessFactory.registerStrategy(this.businessType(), this);
    }
}
