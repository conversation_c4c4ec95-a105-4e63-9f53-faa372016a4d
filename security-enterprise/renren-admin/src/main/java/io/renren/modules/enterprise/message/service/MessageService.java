package io.renren.modules.enterprise.message.service;

import io.renren.common.page.PageData;
import io.renren.common.service.BaseService;
import io.renren.modules.enterprise.message.dto.MessagePage;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/6 8:56
 */
public interface MessageService extends BaseService<MessagePage> {
    /**
     * 分页查询
     * @param params Map<String, Object>
     * @return PageData<MessagePage>
     */
    PageData<MessagePage> pageList(Map<String, Object> params);

    /**
     * 消息批量推送
     * @param asList List<Long>
     */
    void batchPush(List<Long> asList);
}
