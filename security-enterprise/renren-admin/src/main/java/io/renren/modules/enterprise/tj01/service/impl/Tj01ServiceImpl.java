package io.renren.modules.enterprise.tj01.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.enterprise.tj01.dao.Tj01Dao;
import io.renren.modules.enterprise.tj01.dto.Tj01DTO;
import io.renren.modules.enterprise.tj01.entity.Tj01Entity;
import io.renren.modules.enterprise.tj01.service.Tj01Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 住建厅数据写入情况
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-10-31
 */
@Service
public class Tj01ServiceImpl extends CrudServiceImpl<Tj01Dao, Tj01Entity, Tj01DTO> implements Tj01Service {

    @Override
    public QueryWrapper<Tj01Entity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");
        QueryWrapper<Tj01Entity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(id), "id", id);
        return wrapper;
    }


    @Override
    public List<Tj01DTO> getLastWeekList() {
        return baseDao.selectLastWeekList();
    }
}