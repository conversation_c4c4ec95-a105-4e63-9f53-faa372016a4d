package io.renren.modules.supervision.home.dao;

import io.renren.modules.supervision.home.dto.StatisticsAttendance;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Mapper
public interface HomeDao{

    /**
     * 获取当前用户所属区域
     * @param deptId 机构ID
     * @return 行政区域
     */
    String getCurrentArea(Long deptId);

    /**
     * 获取当前登录用户所属行政区划下的项目总数
     * @param deptId 机构ID
     * @return 项目总数
     */
    Long getProjectSum(Long deptId);

    /**
     * 获取当前登录用户所属行政区划下的在建项目总数
     * @param deptId 机构ID
     * @return 在建项目总数
     */
    Long getConstructionProjects(Long deptId);

    /**
     * 获取当前登录用户所属行政区划下的项目管理人员总数
     * @param deptId  机构ID
     * @return 管理人员总数
     */
    Long getManagerSum(Long deptId);

    /**
     * 获取当前登录用户所属行政区划下的建筑工人总数
     * @param deptId 机构ID
     * @return 建筑工人总数
     */
    Long getWorkerSum(Long deptId);

    /**
     * 获取当前登录用户所属行政区划下所有人员的今日考勤
     * @param deptId 机构ID
     * @return 今日考勤的人员总数
     */
    Long getPersonAttendance(Long deptId);

    /**
     * 获取当前登录用户所属行政区划下的最近七天考勤统计
     * @param deptId 机构ID
     * @return 最近七天的考勤
     */
    List<StatisticsAttendance> statisticsAttendance(Long deptId);

    
}
