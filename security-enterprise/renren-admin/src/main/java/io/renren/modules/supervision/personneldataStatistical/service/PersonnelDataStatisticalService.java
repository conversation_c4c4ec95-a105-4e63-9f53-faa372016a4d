package io.renren.modules.supervision.personneldataStatistical.service;

import io.renren.common.service.CrudService;
import io.renren.modules.supervision.personneldataStatistical.dto.PersonnelDataStatisticalDTO;
import io.renren.modules.supervision.personneldataStatistical.entity.PersonnelDataStatisticalEntity;

import java.util.List;
import java.util.Map;

/**
 * @description:人员数据统计分析
 * @author: jinghai
 * @date: 2022/3/8 13:53
 **/
public interface PersonnelDataStatisticalService extends CrudService<PersonnelDataStatisticalEntity, PersonnelDataStatisticalDTO> {

    /**
     * 工种数据统计分析
     * @return
     */
    List<PersonnelDataStatisticalDTO> getWorkType(Map<String, Object> params);

    /**
     * 籍贯数据统计分析
     * @return
     */
    List<PersonnelDataStatisticalDTO> getNativeData(Map<String, Object> params);

    /**
     * 性别数据统计分析
     * @return
     */
    List<PersonnelDataStatisticalDTO> getGenderData(Map<String, Object> params);

    /**
     * 年龄数据统计分析
     * @return
     */
    List<PersonnelDataStatisticalDTO> getAgeData(Map<String, Object> params);

}
