package io.renren.modules.supervision.projectStatistic.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.CommonUtils;
import io.renren.common.utils.ConvertUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.supervision.projectStatistic.dao.ProjectStatDao;
import io.renren.modules.supervision.projectStatistic.dto.ProjectStatDTO;
import io.renren.modules.supervision.projectStatistic.dto.ProjectStatPageDTO;
import io.renren.modules.supervision.projectStatistic.service.ProjectStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-12-28 13:00
 */
@Service
public class ProjectStatServiceImpl extends ServiceImpl<ProjectStatDao, ProjectStatDTO> implements ProjectStatService {
    @Autowired
    private ProjectStatDao projectStatDao;


    @Override
    public List<ProjectStatDTO> getList(Map<String, Object> params) {
        int treeLevel = Integer.parseInt((String) params.get("treeLevel"));
        //根据层级关系处理数据
        params.put("treeLevel", treeLevel * 2 + 2);
        params.put("deptId", SecurityUser.getDeptId());
        return projectStatDao.selectDataList(params);
    }

    @Override
    public PageData<ProjectStatPageDTO> getPageList(Map<String, Object> params) {
        long curPage = Long.parseLong((String) params.get(Constant.PAGE));
        long limit = Long.parseLong((String) params.get(Constant.LIMIT));
        String areaCode = (String) params.get("areaCode");
        //处理行政区划
        params.put("areaCode", CommonUtils.coverAreaCode(areaCode));
        Page<ProjectStatPageDTO> page = new Page<>(curPage, limit);
        List<ProjectStatPageDTO> list = projectStatDao.selectPageList(page, params);
        return getPageData(list, page.getTotal(), ProjectStatPageDTO.class);
    }

    protected PageData<ProjectStatPageDTO> getPageData(List<ProjectStatPageDTO> list, long total, Class<ProjectStatPageDTO> statPageDTOClass) {
        List<ProjectStatPageDTO> targetList = ConvertUtils.sourceToTarget(list, statPageDTOClass);
        return new PageData<>(targetList, total);
    }
}
