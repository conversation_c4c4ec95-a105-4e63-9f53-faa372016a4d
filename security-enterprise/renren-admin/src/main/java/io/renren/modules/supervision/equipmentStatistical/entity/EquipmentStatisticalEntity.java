package io.renren.modules.supervision.equipmentStatistical.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description:
 * @className: EquipmentStatisticalEntity
 * @author: lrl
 * @date: 2021-12-28 13:48
 **/
@Data
@EqualsAndHashCode(callSuper=false)
public class EquipmentStatisticalEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 行政区划
     */
    private String areaCode;
    /**
     * 行政区划名
     */
     private String areaName;
    /**
     * 总设备数
     */
    private String totalEquipmentNum;
    /**
     * 在线设备数
     */
    private String inEquipmentNum;
    /**
     * 离线设备数
     */
    private String outEquipmentNum;


}
