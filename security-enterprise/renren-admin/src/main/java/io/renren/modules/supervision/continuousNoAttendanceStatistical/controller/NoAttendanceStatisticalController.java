package io.renren.modules.supervision.continuousNoAttendanceStatistical.controller;

import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.modules.enterprise.kq02.dto.Kq02AttendanceDTO;
import io.renren.modules.supervision.continuousNoAttendanceStatistical.dto.NoAttendanceStatisticalDTO;
import io.renren.modules.supervision.continuousNoAttendanceStatistical.service.NoAttendanceStatisticalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR> chris
 * @Date : 2022-04-20
 **/
@RestController
@RequestMapping("noAttendanceStatistical/noAttendance")
@Api(tags = "连续无考勤页面")
public class NoAttendanceStatisticalController {

    @Autowired
    private NoAttendanceStatisticalService attendanceStatisticalService;

    @GetMapping("getAbsenceDays")
    @ApiOperation("获取连续缺勤天数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
    })
    @RequiresPermissions("enterprise:noattendancestatistical:getAbsenceDays")
    public Result<PageData<NoAttendanceStatisticalDTO>> getAbsenceDays(@RequestParam Map<String, Object> params) {
        PageData<NoAttendanceStatisticalDTO> absenceDays = attendanceStatisticalService.getAbsenceDays(params);
        return new Result<PageData<NoAttendanceStatisticalDTO>>().ok(absenceDays);
    }

    @GetMapping("exportAbsenceDays")
    @ApiOperation("导出连续缺勤天数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query", required = true, dataType = "int"),
            @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType = "String"),
    })
    @RequiresPermissions("enterprise:noattendancestatistical:exportAbsenceDays")
    public void exportAbsenceDays(
            HttpServletResponse response,
            @RequestParam Map<String, Object> params) {
        attendanceStatisticalService.exportAbsenceDays(response, params);
    }

}
