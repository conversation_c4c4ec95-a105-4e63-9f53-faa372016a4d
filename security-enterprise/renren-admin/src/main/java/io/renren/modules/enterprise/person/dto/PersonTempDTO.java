package io.renren.modules.enterprise.person.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 门禁临时人员
 *
 * <AUTHOR>
 * @since 1.0.0 2024-03-12
 */
@Data
@ApiModel(value = "门禁临时人员")
public class PersonTempDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "人员ID")
    private Long id;
    
    @ApiModelProperty(value = "项目ID")
    private Long pj0101;

    @ApiModelProperty(value = "姓名")
	@NotBlank(message = "姓名不能为空")
    private String name;

    @ApiModelProperty(value = "身份证号码")
    private String idCardNumber;

    @ApiModelProperty(value = "手机号码")
    private String cellphone;

	@ApiModelProperty(value = "头像地址")
	@NotBlank(message = "头像不能为空")
	private String headImageUrl;

	@ApiModelProperty(value = "是否可用")
	private String whether;

}