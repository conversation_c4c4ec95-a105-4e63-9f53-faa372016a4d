package io.renren.modules.enterprise.pj01GeneralParams.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.common.validator.group.UpdateGroup;
import io.renren.modules.enterprise.pj01GeneralParams.dto.BPj01GeneralParamsDTO;
import io.renren.modules.enterprise.pj01GeneralParams.excel.BPj01GeneralParamsExcel;
import io.renren.modules.enterprise.pj01GeneralParams.service.BPj01GeneralParamsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.MapUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 项目通用配置
 *
 * <AUTHOR>
 * @since 1.0.0 2025-04-22
 */
@RestController
@RequestMapping("enterprise/pj01GeneralParams")
@Api(tags="项目通用配置")
public class BPj01GeneralParamsController {

    @Autowired
    private BPj01GeneralParamsService bPj01GeneralParamsService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("enterprise:bpj01generalparams:page")
    public Result<PageData<BPj01GeneralParamsDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<BPj01GeneralParamsDTO> page = bPj01GeneralParamsService.page(params);

        return new Result<PageData<BPj01GeneralParamsDTO>>().ok(page);
    }

    @PostMapping("info")
    @ApiOperation("信息")
    @RequiresPermissions("enterprise:bpj01generalparams:info")
    public Result<BPj01GeneralParamsDTO> get(@RequestBody HashMap<String, Object> params){
        BPj01GeneralParamsDTO data = bPj01GeneralParamsService.getGeneralParamsInfo(MapUtils.getLong(params, "pj0101"));

        return new Result<BPj01GeneralParamsDTO>().ok(data);
    }

    @PostMapping("update")
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("enterprise:bpj01generalparams:update")
    public Result update(@RequestBody BPj01GeneralParamsDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto);

        bPj01GeneralParamsService.updateInfo(dto);

        return new Result();
    }

}