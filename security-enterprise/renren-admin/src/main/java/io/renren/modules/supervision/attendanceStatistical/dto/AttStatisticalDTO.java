package io.renren.modules.supervision.attendanceStatistical.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description
 * @className: attStatisticalDTO
 * @author: lrl
 * @date: 2021-12-28 13:38
 **/
@Data
public class AttStatisticalDTO implements Serializable {


    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目名")
    private String projectName;

    @ApiModelProperty(value = "项目ID")
    private String pj0101;

    @ApiModelProperty(value = "在场工人数")
    private String workerNum;

    @ApiModelProperty(value = "出勤工人数")
    private String attWorkerNum;

    @ApiModelProperty(value = "工人出勤率")
    private String workerRate;

    @ApiModelProperty(value = "在场管理员数")
    private String managerNum;

    @ApiModelProperty(value = "出勤管理员数")
    private String attManagerNum;

    @ApiModelProperty(value = "管理员出勤率")
    private String managerRate;

    @ApiModelProperty(value = "总出勤率")
    private String totalRate;

}
