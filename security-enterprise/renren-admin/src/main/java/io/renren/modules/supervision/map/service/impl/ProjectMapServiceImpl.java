package io.renren.modules.supervision.map.service.impl;

import io.renren.common.utils.CommonUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.supervision.map.dao.ProjectMapDao;
import io.renren.modules.supervision.map.dto.ProjectMapDTO;
import io.renren.modules.supervision.map.service.ProjectMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/20 9:02
 */
@Service
public class ProjectMapServiceImpl implements ProjectMapService {
    @Autowired
    private ProjectMapDao projectMapDao;

    @Override
    public List<ProjectMapDTO> getProjectInfoList(Map<String, Object> params) {
        String areaCode = (String) params.get("areaCode");
        //处理行政区划
        params.put("areaCode", CommonUtils.coverAreaCode(areaCode));
        params.put("deptId", SecurityUser.getDeptId());
        return projectMapDao.selectProjectInfo(params);
    }
}
