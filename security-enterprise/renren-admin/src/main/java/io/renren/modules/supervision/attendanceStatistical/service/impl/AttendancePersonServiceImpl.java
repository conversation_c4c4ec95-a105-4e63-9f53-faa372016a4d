package io.renren.modules.supervision.attendanceStatistical.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.supervision.attendanceStatistical.dao.AttendancePersonDao;
import io.renren.modules.supervision.attendanceStatistical.dto.AttendancePersonDTO;
import io.renren.modules.supervision.attendanceStatistical.entity.AttendancePersonEntity;
import io.renren.modules.supervision.attendanceStatistical.service.AttendancePersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description 考勤人员详情页
 *
 *  @className: AttendancePersonService
 *  @author: jh
 *  @date: 2022-3-1
 */
@Service
public class AttendancePersonServiceImpl extends CrudServiceImpl<AttendancePersonDao, AttendancePersonEntity, AttendancePersonDTO> implements AttendancePersonService {

    @Autowired
    private AttendancePersonDao attendancePersonDao;


    @Override
    public PageData<AttendancePersonDTO> getPageDataByIsOut(Map<String, Object> params) {
        IPage<AttendancePersonEntity> page = getPage(params,"",false);
        List<AttendancePersonDTO> pageData = new ArrayList<>();
        if("1".equals(params.get("personType"))){
             pageData = attendancePersonDao.getPageDataByIsOut(params);
        }else if ("2".equals(params.get("personType"))){
            pageData = attendancePersonDao.getManagePageDataByIsOut(params);
        }

        return getPageData(pageData, page.getTotal(), AttendancePersonDTO.class);
    }

    @Override
    public PageData<AttendancePersonDTO> getPageDataByIsCheck(Map<String, Object> params) {
        IPage<AttendancePersonEntity> page = getPage(params,"",false);
        List<AttendancePersonDTO> pageData = new ArrayList<>();
        //获取统计数据
        if("1".equals(params.get("personType"))){
            pageData = attendancePersonDao.getPageDataByIsCheck(params);
        }else if ("2".equals(params.get("personType"))){
            pageData = attendancePersonDao.getManagePageDataByIsCheck(params);
        }
        return getPageData(pageData, page.getTotal(), AttendancePersonDTO.class);
    }

    @Override
    public List<String> getKqByMonth(Map<String, Object> params) {
        List<String> kqList = attendancePersonDao.getKqByMonth(params);
        return kqList;
    }

    @Override
    public QueryWrapper<AttendancePersonEntity> getWrapper(Map<String, Object> params) {
        return null;
    }
}