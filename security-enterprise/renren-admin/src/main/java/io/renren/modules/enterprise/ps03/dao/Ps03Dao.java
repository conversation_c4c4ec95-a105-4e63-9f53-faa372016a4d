package io.renren.modules.enterprise.ps03.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.ps03.dto.Ps03DTO;
import io.renren.modules.enterprise.ps03.entity.Ps03Entity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 建筑工人合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface Ps03Dao extends BaseDao<Ps03Entity> {

    /**
     * 查询工人合同
     * @param ps0201 建筑工人ID
     * @return Ps03DTO
     */
    Ps03DTO selectByPs0201(Long ps0201);
}