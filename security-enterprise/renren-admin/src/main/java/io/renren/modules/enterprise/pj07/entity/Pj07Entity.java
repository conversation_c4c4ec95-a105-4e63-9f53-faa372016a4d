package io.renren.modules.enterprise.pj07.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目核查情况
 *
 * <AUTHOR> 
 * @since 1.0.0 2023-06-08
 */
@Data
@EqualsAndHashCode(callSuper=false)
@TableName("B_PJ07")
public class Pj07Entity  {
	private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @TableId
	private Long pj0101;
    /**
     * 是否已核查
     */
	private String whether;
    /**
     * 备注
     */
	private String remark;
	/**
     * 创建者
     */
	@TableField(fill = FieldFill.INSERT)
	private Long  creator;
	/**
     * 创建时间
     */
	@TableField(fill = FieldFill.INSERT)
	private Date createDate;
	/**
	 * 更新者
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updater;
	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateDate;
}