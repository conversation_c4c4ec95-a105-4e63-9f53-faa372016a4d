package io.renren.modules.enterprise.pj01report.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.renren.common.page.PageData;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.common.utils.CommonUtils;
import io.renren.modules.admin.security.user.SecurityUser;
import io.renren.modules.enterprise.pj01report.dao.Pj01ReportDao;
import io.renren.modules.enterprise.pj01report.dto.Pj01ReportDTO;
import io.renren.modules.enterprise.pj01report.entity.Pj01ReportEntity;
import io.renren.modules.enterprise.pj01report.service.Pj01ReportService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 项目上报配置表
 *
 * <AUTHOR>
 * @since 1.0.0 2021-05-12
 */
@Service
public class Pj01ReportServiceImpl extends CrudServiceImpl<Pj01ReportDao, Pj01ReportEntity, Pj01ReportDTO> implements Pj01ReportService {

    @Autowired
    private Pj01ReportDao pj01ReportDao;

    @Override
    public QueryWrapper<Pj01ReportEntity> getWrapper(Map<String, Object> params) {
        String id = (String) params.get("id");

        QueryWrapper<Pj01ReportEntity> wrapper = new QueryWrapper<>();

        wrapper.eq(StringUtils.isNotBlank(id), "id", id);

        return wrapper;
    }


    @Override
    public PageData<Pj01ReportDTO> selectListData(Map<String, Object> params) {
        params.put("deptId", SecurityUser.getDeptId());

        String areaCode = (String) params.get("areaCode");

        params.put("areaCode", CommonUtils.coverAreaCode(areaCode));

        IPage<Pj01ReportEntity> page = getPage(params, "", false);

        List<Pj01ReportDTO> list = pj01ReportDao.getListData(params);

        return getPageData(list, page.getTotal(), Pj01ReportDTO.class);
    }

    @Override
    public Pj01ReportDTO getData(Long pj0101) {
        return pj01ReportDao.getData(pj0101);
    }

    @Override
    public void updateData(Pj01ReportDTO dto) {
        Pj01ReportEntity pj01ReportEntity = pj01ReportDao.selectById(dto.getPj0101());

        pj01ReportEntity.setReportType(dto.getReport());

        pj01ReportEntity.setPayBankCode(dto.getCode());

        pj01ReportDao.updateById(pj01ReportEntity);
    }
}