package io.renren.modules.enterprise.ps02.dao;

import io.renren.common.dao.BaseDao;
import io.renren.modules.enterprise.kq05.dto.PersonDTO;
import io.renren.modules.enterprise.ps02.dto.Ps02ContractDTO;
import io.renren.modules.enterprise.ps02.dto.Ps02DTO;
import io.renren.modules.enterprise.ps02.dto.Ps02EmpRecordDTO;
import io.renren.modules.enterprise.ps02.dto.Ps02PageDTO;
import io.renren.modules.enterprise.ps02.entity.Ps02Entity;
import io.renren.modules.enterprise.ps02.excel.Ps02Excel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 建筑工人信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-04-30
 */
@Mapper
public interface Ps02Dao extends BaseDao<Ps02Entity> {

    /**
     * 查询分页数据
     *
     * @param params params
     * @return List<Ps02DTO>
     */
    List<Ps02PageDTO> getListData(Map<String, Object> params);

    /**
     * 工人退场
     *
     * @param ids  ps0201
     * @param type 进场或退场
     */
    void updateInOrOutByIds(@Param("list") List<Long> ids, @Param("type") String type);

    /**
     * 查询人员信息
     *
     * @param longs 人员id
     * @return
     */
    List<PersonDTO> selectPersonByIds(List<Long> longs);

    /**
     * 查询项目项目的人员是否存在
     *
     * @param pj0101 项目ID
     * @param ps0101 人员ID
     * @return
     */
    Integer selectCountPs0101(@Param("pj0101") Long pj0101, @Param("ps0101") Long ps0101);

    Ps02ContractDTO exportContract(String ps0201);

    /**
     * 查询工人用工记录信息
     *
     * @param params
     * @return
     */
    List<Ps02EmpRecordDTO> getEmpRecordListData(Map<String, Object> params);

    /**
     * 人员信息导出
     *
     * @param params Map<String, Object>
     * @return List<Ps02Excel>
     */
    List<Ps02Excel> selectPersonExcelList(Map<String, Object> params);

    /**
     * 获取未出勤工人信息
     *
     * @param pj0101 项目id
     * @param exitdayparam 天数
     * @return
     */
    List<Ps02DTO> getNoAttendanceWorkerByDay(@Param("pj0101") Long pj0101,@Param("exitdayparam")  Integer exitdayparam);
}