package io.renren.app.enterprise.dao;

import io.renren.app.enterprise.dto.BCp01DTO;
import io.renren.app.enterprise.dto.BCp02DTO;
import io.renren.app.enterprise.dto.CommonDto;
import io.renren.app.enterprise.entity.BCp02Entity;
import io.renren.common.dao.BaseDao;

import io.renren.common.dto.DictDataDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 项目参建单位信息数据表
 *
 * <AUTHOR>
 * @since 1.0.0 2019-06-08
 */
@Mapper
public interface BCp02Dao extends BaseDao<BCp02Entity> {


    /**
     * 查询项目下企业信息
     *
     * @param params 查询条件
     * @return list
     */
    List<BCp02DTO> getList(Map<String, Object> params);

    /**
     * 查询项目下的参建单位
     *
     * @param deptId 当前登录用户的deptId
     * @return list
     */
    List<CommonDto> loadCp02Info(Long deptId);


    /**
     * 查询岗位类型
     *
     * @return
     */
    List<CommonDto> loadJobType(String corptype);


    /**
     * 企业详细信息
     *
     * @return
     */
    BCp01DTO getDetail(Long cp0201);


    /**
     * 根据区域编码获取全地址名称
     *
     * @return
     */
    List<String> getAddress(String areacode);

    List<DictDataDTO> getCompanySelect(String pj0101);

    /**
     * 根据参建单位ID,查询对应的岗位
     * @param cp0201 参建单位ID
     * @return List<CommonDto>
     */
    List<CommonDto> selectJobTypeById(Long cp0201);
}