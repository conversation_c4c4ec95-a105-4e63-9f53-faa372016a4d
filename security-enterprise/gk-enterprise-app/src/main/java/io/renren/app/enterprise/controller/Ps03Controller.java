package io.renren.app.enterprise.controller;

import io.renren.annotation.Login;
import io.renren.app.enterprise.dto.BPs03DTO;
import io.renren.app.enterprise.dto.BPs04DTO;
import io.renren.app.enterprise.dto.BPs04PageDTO;
import io.renren.app.enterprise.service.BPs03Service;
import io.renren.app.enterprise.service.BPs04Service;
import io.renren.common.constant.Constant;
import io.renren.common.exception.RenException;
import io.renren.common.page.PageData;
import io.renren.common.utils.Result;
import io.renren.common.validator.ValidatorUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;


/**
 * 合同信息
 *
 * <AUTHOR>
 * @since 1.0.0 2020-06-01
 */
@RestController
@RequestMapping("/api/ps03")
@Api(tags = "合同信息")
public class Ps03Controller {
    @Autowired
    private BPs03Service bPs03Service;

    @Login
    @PostMapping("save")
    @ApiOperation("保存")
    public Result<Long> save(@RequestBody BPs03DTO dto) {
        //效验数据
        ValidatorUtils.validateEntity(dto);

        Long ps0301 = bPs03Service.saveOrUpdatePs03Info(dto);

        return new Result<Long>().ok(ps0301);
    }

    @Login
    @GetMapping("getPs03Info")
    @ApiOperation("合同信息")
    public Result<BPs03DTO> getPs03Info(@RequestParam Map<String, Object> params) {
        Long ps0201 = Long.parseLong(params.get("ps0201").toString());
        BPs03DTO data = bPs03Service.getPs03Info(ps0201);
        return new Result<BPs03DTO>().ok(data);
    }


}