-- =====================================================
-- Oracle考勤统计存储过程 - 修复版本
-- 功能：月度考勤状态统计和工时计算
-- 修复问题：空结果集、日期格式、调试支持
-- =====================================================

-- 1. 数据验证和调试存储过程
CREATE OR REPLACE PROCEDURE SP_DEBUG_ATTENDANCE_DATA(
    P_MONTH IN VARCHAR2,
    P_RESULT OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN P_RESULT FOR
    SELECT 
        COUNT(*) as total_records,
        MIN(checkdate) as min_date,
        MAX(checkdate) as max_date,
        COUNT(DISTINCT user_id) as unique_users,
        COUNT(DISTINCT person_name) as unique_names,
        COUNT(DISTINCT TO_CHAR(checkdate, 'YYYY-MM')) as unique_months,
        COUNT(CASE WHEN checkdate IS NULL THEN 1 END) as null_dates,
        COUNT(CASE WHEN TO_CHAR(checkdate, 'YYYY-MM') = P_MONTH THEN 1 END) as matching_month_records
    FROM B_KQ02
    WHERE checkdate IS NOT NULL;
END SP_DEBUG_ATTENDANCE_DATA;
/

-- 2. 修复后的综合考勤统计存储过程
CREATE OR REPLACE PROCEDURE SP_ATTENDANCE_COMPREHENSIVE_STATISTICS_FIXED(
    P_MONTH IN VARCHAR2,           -- 月份参数，格式：'YYYY-MM'
    P_PJ0101 IN NUMBER DEFAULT NULL, -- 项目ID过滤（可选）
    P_PERSON_TYPE IN VARCHAR2 DEFAULT NULL, -- 人员类型过滤（可选）
    P_DEBUG IN NUMBER DEFAULT 0,   -- 调试模式：1-输出调试信息，0-正常模式
    P_RESULT OUT SYS_REFCURSOR    -- 返回结果集
) AS
    V_YEAR NUMBER;
    V_MONTH_NUM NUMBER;
    V_DAYS_IN_MONTH NUMBER;
    V_SQL CLOB;
    V_SELECT_COLUMNS CLOB := '';
    V_WHERE_CLAUSE VARCHAR2(2000) := '';
    V_DEBUG_SQL CLOB;
    V_COUNT NUMBER := 0;
BEGIN
    -- 参数验证
    IF P_MONTH IS NULL OR LENGTH(P_MONTH) != 7 OR SUBSTR(P_MONTH, 5, 1) != '-' THEN
        RAISE_APPLICATION_ERROR(-20001, '月份参数格式错误，请使用YYYY-MM格式，当前值：' || NVL(P_MONTH, 'NULL'));
    END IF;
    
    -- 解析年月
    BEGIN
        V_YEAR := TO_NUMBER(SUBSTR(P_MONTH, 1, 4));
        V_MONTH_NUM := TO_NUMBER(SUBSTR(P_MONTH, 6, 2));
    EXCEPTION
        WHEN OTHERS THEN
            RAISE_APPLICATION_ERROR(-20003, '月份参数解析失败：' || P_MONTH || '，错误：' || SQLERRM);
    END;
    
    -- 验证年月范围
    IF V_YEAR < 2000 OR V_YEAR > 2100 OR V_MONTH_NUM < 1 OR V_MONTH_NUM > 12 THEN
        RAISE_APPLICATION_ERROR(-20004, '月份参数超出有效范围：' || P_MONTH);
    END IF;
    
    -- 计算该月天数
    BEGIN
        V_DAYS_IN_MONTH := TO_NUMBER(TO_CHAR(LAST_DAY(TO_DATE(P_MONTH || '-01', 'YYYY-MM-DD')), 'DD'));
    EXCEPTION
        WHEN OTHERS THEN
            RAISE_APPLICATION_ERROR(-20005, '计算月份天数失败：' || P_MONTH || '，错误：' || SQLERRM);
    END;
    
    -- 检查是否有匹配的数据
    EXECUTE IMMEDIATE 
        'SELECT COUNT(*) FROM B_KQ02 WHERE checkdate IS NOT NULL AND ' ||
        'TO_CHAR(checkdate, ''YYYY-MM'') = ''' || P_MONTH || ''''
    INTO V_COUNT;
    
    IF P_DEBUG = 1 THEN
        DBMS_OUTPUT.PUT_LINE('调试信息：');
        DBMS_OUTPUT.PUT_LINE('月份参数：' || P_MONTH);
        DBMS_OUTPUT.PUT_LINE('年份：' || V_YEAR || '，月份：' || V_MONTH_NUM);
        DBMS_OUTPUT.PUT_LINE('该月天数：' || V_DAYS_IN_MONTH);
        DBMS_OUTPUT.PUT_LINE('匹配的记录数：' || V_COUNT);
    END IF;
    
    -- 如果没有数据，返回空结果集但不报错
    IF V_COUNT = 0 THEN
        IF P_DEBUG = 1 THEN
            DBMS_OUTPUT.PUT_LINE('警告：没有找到匹配的考勤数据');
        END IF;
        -- 返回空结果集，但保持结构
        V_SQL := 'SELECT CAST(NULL AS VARCHAR2(50)) as person_name, CAST(NULL AS NUMBER) as user_id';
        FOR i IN 1..V_DAYS_IN_MONTH LOOP
            V_SQL := V_SQL || ', CAST(NULL AS VARCHAR2(20)) AS DAY_' || LPAD(i, 2, '0');
        END LOOP;
        V_SQL := V_SQL || ' FROM DUAL WHERE 1=0';
        OPEN P_RESULT FOR V_SQL;
        RETURN;
    END IF;
    
    -- 构建动态列（状态+工时）
    FOR i IN 1..V_DAYS_IN_MONTH LOOP
        V_SELECT_COLUMNS := V_SELECT_COLUMNS || 
            ', CASE WHEN MAX(CASE WHEN TO_CHAR(checkdate, ''DD'') = ''' || LPAD(i, 2, '0') || 
            ''' THEN 1 END) = 1 THEN ' ||
            '''√'' || TO_CHAR(ROUND(NVL((MAX(CASE WHEN TO_CHAR(checkdate, ''DD'') = ''' || LPAD(i, 2, '0') || 
            ''' THEN checkdate END) - MIN(CASE WHEN TO_CHAR(checkdate, ''DD'') = ''' || LPAD(i, 2, '0') || 
            ''' THEN checkdate END)) * 24, 0), 2), ''FM999990.00'') ELSE ''×0'' END AS DAY_' || LPAD(i, 2, '0');
    END LOOP;
    
    -- 构建WHERE条件 - 改进的条件构建
    V_WHERE_CLAUSE := ' WHERE checkdate IS NOT NULL AND TO_CHAR(checkdate, ''YYYY-MM'') = ''' || P_MONTH || '''';
    
    IF P_PJ0101 IS NOT NULL THEN
        V_WHERE_CLAUSE := V_WHERE_CLAUSE || ' AND pj0101 = ' || P_PJ0101;
    END IF;
    
    IF P_PERSON_TYPE IS NOT NULL AND LENGTH(TRIM(P_PERSON_TYPE)) > 0 THEN
        V_WHERE_CLAUSE := V_WHERE_CLAUSE || ' AND person_type = ''' || TRIM(P_PERSON_TYPE) || '''';
    END IF;
    
    -- 构建完整SQL
    V_SQL := 'SELECT person_name, user_id' || V_SELECT_COLUMNS || 
             ' FROM B_KQ02 ' ||
             V_WHERE_CLAUSE ||
             ' GROUP BY person_name, user_id' ||
             ' HAVING COUNT(*) > 0' ||
             ' ORDER BY person_name, user_id';
    
    -- 调试模式输出SQL
    IF P_DEBUG = 1 THEN
        DBMS_OUTPUT.PUT_LINE('生成的SQL：');
        DBMS_OUTPUT.PUT_LINE(SUBSTR(V_SQL, 1, 4000));
        IF LENGTH(V_SQL) > 4000 THEN
            DBMS_OUTPUT.PUT_LINE(SUBSTR(V_SQL, 4001, 4000));
        END IF;
    END IF;
    
    -- 执行查询
    OPEN P_RESULT FOR V_SQL;
    
EXCEPTION
    WHEN OTHERS THEN
        IF P_DEBUG = 1 THEN
            DBMS_OUTPUT.PUT_LINE('错误信息：' || SQLERRM);
            DBMS_OUTPUT.PUT_LINE('错误代码：' || SQLCODE);
        END IF;
        RAISE_APPLICATION_ERROR(-20002, '综合考勤统计执行失败: ' || SQLERRM);
END SP_ATTENDANCE_COMPREHENSIVE_STATISTICS_FIXED;
/

-- 3. 简化版本用于快速测试
CREATE OR REPLACE PROCEDURE SP_SIMPLE_ATTENDANCE_TEST(
    P_MONTH IN VARCHAR2,
    P_RESULT OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN P_RESULT FOR
    SELECT 
        person_name,
        user_id,
        COUNT(*) as total_records,
        MIN(checkdate) as first_attendance,
        MAX(checkdate) as last_attendance,
        COUNT(DISTINCT TO_CHAR(checkdate, 'DD')) as attendance_days
    FROM B_KQ02
    WHERE checkdate IS NOT NULL 
      AND TO_CHAR(checkdate, 'YYYY-MM') = P_MONTH
    GROUP BY person_name, user_id
    ORDER BY person_name, user_id;
END SP_SIMPLE_ATTENDANCE_TEST;
/

-- 4. 数据格式检查存储过程
CREATE OR REPLACE PROCEDURE SP_CHECK_DATE_FORMATS(
    P_RESULT OUT SYS_REFCURSOR
) AS
BEGIN
    OPEN P_RESULT FOR
    SELECT 
        'checkdate样本' as field_name,
        TO_CHAR(checkdate, 'YYYY-MM-DD HH24:MI:SS') as formatted_date,
        TO_CHAR(checkdate, 'YYYY-MM') as year_month,
        TO_CHAR(checkdate, 'DD') as day_of_month,
        checkdate as raw_date
    FROM B_KQ02 
    WHERE checkdate IS NOT NULL 
      AND ROWNUM <= 10
    ORDER BY checkdate DESC;
END SP_CHECK_DATE_FORMATS;
/

-- 5. 创建测试数据的存储过程（如果需要）
CREATE OR REPLACE PROCEDURE SP_CREATE_TEST_DATA(
    P_MONTH IN VARCHAR2,
    P_USER_COUNT IN NUMBER DEFAULT 3
) AS
    V_BASE_DATE DATE;
    V_USER_ID NUMBER;
    V_PERSON_NAME VARCHAR2(50);
BEGIN
    -- 解析月份并创建基础日期
    V_BASE_DATE := TO_DATE(P_MONTH || '-01', 'YYYY-MM-DD');
    
    -- 清理测试数据
    DELETE FROM B_KQ02 WHERE TO_CHAR(checkdate, 'YYYY-MM') = P_MONTH AND user_id BETWEEN 9001 AND 9999;
    
    -- 创建测试数据
    FOR user_idx IN 1..P_USER_COUNT LOOP
        V_USER_ID := 9000 + user_idx;
        V_PERSON_NAME := '测试用户' || user_idx;
        
        -- 为每个用户创建随机考勤记录
        FOR day_num IN 1..15 LOOP -- 只创建前15天的数据
            IF MOD(day_num + user_idx, 3) != 0 THEN -- 模拟缺勤
                -- 上班打卡
                INSERT INTO B_KQ02 (
                    kq0201, user_id, person_name, person_type, pj0101,
                    checkdate, direction, attendtype, creator, create_date
                ) VALUES (
                    SEQ_KQ02.NEXTVAL, V_USER_ID, V_PERSON_NAME, '1', 1001,
                    V_BASE_DATE + day_num - 1 + (8 + MOD(user_idx, 2))/24, -- 8点或9点上班
                    '1', '1', 1, SYSDATE
                );
                
                -- 下班打卡
                INSERT INTO B_KQ02 (
                    kq0201, user_id, person_name, person_type, pj0101,
                    checkdate, direction, attendtype, creator, create_date
                ) VALUES (
                    SEQ_KQ02.NEXTVAL, V_USER_ID, V_PERSON_NAME, '1', 1001,
                    V_BASE_DATE + day_num - 1 + (17 + MOD(user_idx, 3))/24, -- 17-19点下班
                    '2', '1', 1, SYSDATE
                );
            END IF;
        END LOOP;
    END LOOP;
    
    COMMIT;
    DBMS_OUTPUT.PUT_LINE('已创建 ' || P_USER_COUNT || ' 个用户的测试数据，月份：' || P_MONTH);
END SP_CREATE_TEST_DATA;
/
