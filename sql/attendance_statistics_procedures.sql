-- =====================================================
-- Oracle考勤统计存储过程
-- 功能：月度考勤状态统计和工时计算
-- 作者：系统生成
-- 创建时间：2024-03
-- =====================================================

-- 1. 考勤状态统计存储过程
CREATE OR REPLACE PROCEDURE SP_ATTENDANCE_STATUS_STATISTICS(
    P_MONTH IN VARCHAR2,           -- 月份参数，格式：'YYYY-MM'
    P_PJ0101 IN NUMBER DEFAULT NULL, -- 项目ID过滤（可选）
    P_PERSON_TYPE IN VARCHAR2 DEFAULT NULL, -- 人员类型过滤（可选）
    P_RESULT OUT SYS_REFCURSOR    -- 返回结果集
) AS
    V_YEAR NUMBER;
    V_MONTH_NUM NUMBER;
    V_DAYS_IN_MONTH NUMBER;
    V_SQL CLOB;
    V_COLUMNS CLOB := '';
    V_SELECT_COLUMNS CLOB := '';
    V_WHERE_CLAUSE VARCHAR2(1000) := '';
BEGIN
    -- 参数验证
    IF P_MONTH IS NULL OR LENGTH(P_MONTH) != 7 OR SUBSTR(P_MONTH, 5, 1) != '-' THEN
        RAISE_APPLICATION_ERROR(-20001, '月份参数格式错误，请使用YYYY-MM格式');
    END IF;
    
    -- 解析年月
    V_YEAR := TO_NUMBER(SUBSTR(P_MONTH, 1, 4));
    V_MONTH_NUM := TO_NUMBER(SUBSTR(P_MONTH, 6, 2));
    
    -- 计算该月天数
    V_DAYS_IN_MONTH := TO_NUMBER(TO_CHAR(LAST_DAY(TO_DATE(P_MONTH || '-01', 'YYYY-MM-DD')), 'DD'));
    
    -- 构建动态列
    FOR i IN 1..V_DAYS_IN_MONTH LOOP
        V_COLUMNS := V_COLUMNS || ', DAY_' || LPAD(i, 2, '0') || ' VARCHAR2(10)';
        V_SELECT_COLUMNS := V_SELECT_COLUMNS || 
            ', CASE WHEN MAX(CASE WHEN TO_CHAR(checkdate, ''DD'') = ''' || LPAD(i, 2, '0') || 
            ''' THEN ''√'' END) IS NOT NULL THEN ''√'' ELSE ''×'' END AS DAY_' || LPAD(i, 2, '0');
    END LOOP;
    
    -- 构建WHERE条件
    IF P_PJ0101 IS NOT NULL THEN
        V_WHERE_CLAUSE := V_WHERE_CLAUSE || ' AND pj0101 = ' || P_PJ0101;
    END IF;
    
    IF P_PERSON_TYPE IS NOT NULL THEN
        V_WHERE_CLAUSE := V_WHERE_CLAUSE || ' AND person_type = ''' || P_PERSON_TYPE || '''';
    END IF;
    
    -- 构建完整SQL
    V_SQL := 'SELECT person_name, user_id' || V_SELECT_COLUMNS || 
             ' FROM B_KQ02 ' ||
             ' WHERE TO_CHAR(checkdate, ''YYYY-MM'') = ''' || P_MONTH || '''' ||
             V_WHERE_CLAUSE ||
             ' GROUP BY person_name, user_id' ||
             ' ORDER BY person_name';
    
    -- 执行查询
    OPEN P_RESULT FOR V_SQL;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE_APPLICATION_ERROR(-20002, '考勤状态统计执行失败: ' || SQLERRM);
END SP_ATTENDANCE_STATUS_STATISTICS;
/

-- 2. 工时计算存储过程
CREATE OR REPLACE PROCEDURE SP_ATTENDANCE_WORKHOURS_STATISTICS(
    P_MONTH IN VARCHAR2,           -- 月份参数，格式：'YYYY-MM'
    P_PJ0101 IN NUMBER DEFAULT NULL, -- 项目ID过滤（可选）
    P_PERSON_TYPE IN VARCHAR2 DEFAULT NULL, -- 人员类型过滤（可选）
    P_RESULT OUT SYS_REFCURSOR    -- 返回结果集
) AS
    V_YEAR NUMBER;
    V_MONTH_NUM NUMBER;
    V_DAYS_IN_MONTH NUMBER;
    V_SQL CLOB;
    V_COLUMNS CLOB := '';
    V_SELECT_COLUMNS CLOB := '';
    V_WHERE_CLAUSE VARCHAR2(1000) := '';
BEGIN
    -- 参数验证
    IF P_MONTH IS NULL OR LENGTH(P_MONTH) != 7 OR SUBSTR(P_MONTH, 5, 1) != '-' THEN
        RAISE_APPLICATION_ERROR(-20001, '月份参数格式错误，请使用YYYY-MM格式');
    END IF;
    
    -- 解析年月
    V_YEAR := TO_NUMBER(SUBSTR(P_MONTH, 1, 4));
    V_MONTH_NUM := TO_NUMBER(SUBSTR(P_MONTH, 6, 2));
    
    -- 计算该月天数
    V_DAYS_IN_MONTH := TO_NUMBER(TO_CHAR(LAST_DAY(TO_DATE(P_MONTH || '-01', 'YYYY-MM-DD')), 'DD'));
    
    -- 构建动态列（工时计算）
    FOR i IN 1..V_DAYS_IN_MONTH LOOP
        V_COLUMNS := V_COLUMNS || ', WORKHOURS_' || LPAD(i, 2, '0') || ' NUMBER(5,2)';
        V_SELECT_COLUMNS := V_SELECT_COLUMNS || 
            ', ROUND(NVL((MAX(CASE WHEN TO_CHAR(checkdate, ''DD'') = ''' || LPAD(i, 2, '0') || 
            ''' THEN checkdate END) - MIN(CASE WHEN TO_CHAR(checkdate, ''DD'') = ''' || LPAD(i, 2, '0') || 
            ''' THEN checkdate END)) * 24, 0), 2) AS WORKHOURS_' || LPAD(i, 2, '0');
    END LOOP;
    
    -- 构建WHERE条件
    IF P_PJ0101 IS NOT NULL THEN
        V_WHERE_CLAUSE := V_WHERE_CLAUSE || ' AND pj0101 = ' || P_PJ0101;
    END IF;
    
    IF P_PERSON_TYPE IS NOT NULL THEN
        V_WHERE_CLAUSE := V_WHERE_CLAUSE || ' AND person_type = ''' || P_PERSON_TYPE || '''';
    END IF;
    
    -- 构建完整SQL
    V_SQL := 'SELECT person_name, user_id' || V_SELECT_COLUMNS || 
             ' FROM B_KQ02 ' ||
             ' WHERE TO_CHAR(checkdate, ''YYYY-MM'') = ''' || P_MONTH || '''' ||
             V_WHERE_CLAUSE ||
             ' GROUP BY person_name, user_id' ||
             ' ORDER BY person_name';
    
    -- 执行查询
    OPEN P_RESULT FOR V_SQL;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE_APPLICATION_ERROR(-20002, '工时统计执行失败: ' || SQLERRM);
END SP_ATTENDANCE_WORKHOURS_STATISTICS;
/

-- 3. 综合考勤统计存储过程（状态+工时）
CREATE OR REPLACE PROCEDURE SP_ATTENDANCE_COMPREHENSIVE_STATISTICS(
    P_MONTH IN VARCHAR2,           -- 月份参数，格式：'YYYY-MM'
    P_PJ0101 IN NUMBER DEFAULT NULL, -- 项目ID过滤（可选）
    P_PERSON_TYPE IN VARCHAR2 DEFAULT NULL, -- 人员类型过滤（可选）
    P_RESULT OUT SYS_REFCURSOR    -- 返回结果集
) AS
    V_YEAR NUMBER;
    V_MONTH_NUM NUMBER;
    V_DAYS_IN_MONTH NUMBER;
    V_SQL CLOB;
    V_SELECT_COLUMNS CLOB := '';
    V_WHERE_CLAUSE VARCHAR2(1000) := '';
BEGIN
    -- 参数验证
    IF P_MONTH IS NULL OR LENGTH(P_MONTH) != 7 OR SUBSTR(P_MONTH, 5, 1) != '-' THEN
        RAISE_APPLICATION_ERROR(-20001, '月份参数格式错误，请使用YYYY-MM格式');
    END IF;
    
    -- 解析年月
    V_YEAR := TO_NUMBER(SUBSTR(P_MONTH, 1, 4));
    V_MONTH_NUM := TO_NUMBER(SUBSTR(P_MONTH, 6, 2));
    
    -- 计算该月天数
    V_DAYS_IN_MONTH := TO_NUMBER(TO_CHAR(LAST_DAY(TO_DATE(P_MONTH || '-01', 'YYYY-MM-DD')), 'DD'));
    
    -- 构建动态列（状态+工时）
    FOR i IN 1..V_DAYS_IN_MONTH LOOP
        V_SELECT_COLUMNS := V_SELECT_COLUMNS || 
            ', CASE WHEN MAX(CASE WHEN TO_CHAR(checkdate, ''DD'') = ''' || LPAD(i, 2, '0') || 
            ''' THEN ''√'' END) IS NOT NULL THEN ' ||
            '''√'' || ROUND(NVL((MAX(CASE WHEN TO_CHAR(checkdate, ''DD'') = ''' || LPAD(i, 2, '0') || 
            ''' THEN checkdate END) - MIN(CASE WHEN TO_CHAR(checkdate, ''DD'') = ''' || LPAD(i, 2, '0') || 
            ''' THEN checkdate END)) * 24, 0), 2) ELSE ''×0'' END AS DAY_' || LPAD(i, 2, '0');
    END LOOP;
    
    -- 构建WHERE条件
    IF P_PJ0101 IS NOT NULL THEN
        V_WHERE_CLAUSE := V_WHERE_CLAUSE || ' AND pj0101 = ' || P_PJ0101;
    END IF;
    
    IF P_PERSON_TYPE IS NOT NULL THEN
        V_WHERE_CLAUSE := V_WHERE_CLAUSE || ' AND person_type = ''' || P_PERSON_TYPE || '''';
    END IF;
    
    -- 构建完整SQL
    V_SQL := 'SELECT person_name, user_id' || V_SELECT_COLUMNS || 
             ' FROM B_KQ02 ' ||
             ' WHERE TO_CHAR(checkdate, ''YYYY-MM'') = ''' || P_MONTH || '''' ||
             V_WHERE_CLAUSE ||
             ' GROUP BY person_name, user_id' ||
             ' ORDER BY person_name';
    
    -- 执行查询
    OPEN P_RESULT FOR V_SQL;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE_APPLICATION_ERROR(-20002, '综合考勤统计执行失败: ' || SQLERRM);
END SP_ATTENDANCE_COMPREHENSIVE_STATISTICS;
/

-- 创建索引以提高查询性能
CREATE INDEX IDX_B_KQ02_CHECKDATE ON B_KQ02(checkdate);
CREATE INDEX IDX_B_KQ02_USER_CHECKDATE ON B_KQ02(user_id, checkdate);
CREATE INDEX IDX_B_KQ02_PJ_CHECKDATE ON B_KQ02(pj0101, checkdate);
