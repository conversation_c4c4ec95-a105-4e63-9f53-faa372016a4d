-- =====================================================
-- 考勤统计存储过程调试和测试脚本
-- 用于诊断和解决空结果集问题
-- =====================================================

-- 设置输出缓冲区大小
SET SERVEROUTPUT ON SIZE 1000000;

-- 1. 基础数据检查
PROMPT ========== 1. 基础数据检查 ==========
SELECT 
    '总记录数' as 检查项,
    COUNT(*) as 数值
FROM B_KQ02
UNION ALL
SELECT 
    '非空日期记录数' as 检查项,
    COUNT(*) as 数值
FROM B_KQ02 
WHERE checkdate IS NOT NULL
UNION ALL
SELECT 
    '唯一用户数' as 检查项,
    COUNT(DISTINCT user_id) as 数值
FROM B_KQ02 
WHERE checkdate IS NOT NULL
UNION ALL
SELECT 
    '最早日期' as 检查项,
    TO_NUMBER(TO_CHAR(MIN(checkdate), 'YYYYMMDD')) as 数值
FROM B_KQ02 
WHERE checkdate IS NOT NULL
UNION ALL
SELECT 
    '最晚日期' as 检查项,
    TO_NUMBER(TO_CHAR(MAX(checkdate), 'YYYYMMDD')) as 数值
FROM B_KQ02 
WHERE checkdate IS NOT NULL;

-- 2. 日期格式检查
PROMPT ========== 2. 日期格式检查 ==========
SELECT 
    checkdate as 原始日期,
    TO_CHAR(checkdate, 'YYYY-MM-DD HH24:MI:SS') as 格式化日期,
    TO_CHAR(checkdate, 'YYYY-MM') as 年月格式,
    TO_CHAR(checkdate, 'DD') as 日期格式
FROM B_KQ02 
WHERE checkdate IS NOT NULL 
  AND ROWNUM <= 5
ORDER BY checkdate DESC;

-- 3. 月份数据分布检查
PROMPT ========== 3. 月份数据分布检查 ==========
SELECT 
    TO_CHAR(checkdate, 'YYYY-MM') as 年月,
    COUNT(*) as 记录数,
    COUNT(DISTINCT user_id) as 用户数,
    MIN(checkdate) as 最早时间,
    MAX(checkdate) as 最晚时间
FROM B_KQ02 
WHERE checkdate IS NOT NULL
GROUP BY TO_CHAR(checkdate, 'YYYY-MM')
ORDER BY TO_CHAR(checkdate, 'YYYY-MM') DESC;

-- 4. 测试特定月份的数据
PROMPT ========== 4. 测试特定月份数据（请修改月份参数） ==========
DEFINE test_month = '2024-03'

SELECT 
    '指定月份记录数' as 检查项,
    COUNT(*) as 数值
FROM B_KQ02 
WHERE checkdate IS NOT NULL 
  AND TO_CHAR(checkdate, 'YYYY-MM') = '&test_month'
UNION ALL
SELECT 
    '指定月份用户数' as 检查项,
    COUNT(DISTINCT user_id) as 数值
FROM B_KQ02 
WHERE checkdate IS NOT NULL 
  AND TO_CHAR(checkdate, 'YYYY-MM') = '&test_month';

-- 5. 用户考勤详情检查
PROMPT ========== 5. 用户考勤详情检查 ==========
SELECT 
    person_name as 姓名,
    user_id as 用户ID,
    COUNT(*) as 打卡次数,
    COUNT(DISTINCT TO_CHAR(checkdate, 'YYYY-MM-DD')) as 考勤天数,
    MIN(TO_CHAR(checkdate, 'YYYY-MM-DD HH24:MI:SS')) as 最早打卡,
    MAX(TO_CHAR(checkdate, 'YYYY-MM-DD HH24:MI:SS')) as 最晚打卡
FROM B_KQ02 
WHERE checkdate IS NOT NULL 
  AND TO_CHAR(checkdate, 'YYYY-MM') = '&test_month'
  AND ROWNUM <= 10
GROUP BY person_name, user_id
ORDER BY user_id;

-- 6. 调试存储过程调用
PROMPT ========== 6. 调试存储过程调用 ==========

-- 6.1 调用数据验证存储过程
DECLARE
    v_cursor SYS_REFCURSOR;
    v_total_records NUMBER;
    v_min_date DATE;
    v_max_date DATE;
    v_unique_users NUMBER;
    v_unique_names NUMBER;
    v_unique_months NUMBER;
    v_null_dates NUMBER;
    v_matching_records NUMBER;
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== 调用数据验证存储过程 ===');
    SP_DEBUG_ATTENDANCE_DATA('&test_month', v_cursor);
    
    FETCH v_cursor INTO v_total_records, v_min_date, v_max_date, 
          v_unique_users, v_unique_names, v_unique_months, v_null_dates, v_matching_records;
    
    DBMS_OUTPUT.PUT_LINE('总记录数: ' || v_total_records);
    DBMS_OUTPUT.PUT_LINE('最早日期: ' || TO_CHAR(v_min_date, 'YYYY-MM-DD HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE('最晚日期: ' || TO_CHAR(v_max_date, 'YYYY-MM-DD HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE('唯一用户数: ' || v_unique_users);
    DBMS_OUTPUT.PUT_LINE('唯一姓名数: ' || v_unique_names);
    DBMS_OUTPUT.PUT_LINE('唯一月份数: ' || v_unique_months);
    DBMS_OUTPUT.PUT_LINE('空日期记录数: ' || v_null_dates);
    DBMS_OUTPUT.PUT_LINE('匹配月份记录数: ' || v_matching_records);
    
    CLOSE v_cursor;
END;
/

-- 6.2 调用简化测试存储过程
DECLARE
    v_cursor SYS_REFCURSOR;
    v_person_name VARCHAR2(50);
    v_user_id NUMBER;
    v_total_records NUMBER;
    v_first_attendance DATE;
    v_last_attendance DATE;
    v_attendance_days NUMBER;
    v_count NUMBER := 0;
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== 调用简化测试存储过程 ===');
    SP_SIMPLE_ATTENDANCE_TEST('&test_month', v_cursor);
    
    LOOP
        FETCH v_cursor INTO v_person_name, v_user_id, v_total_records, 
              v_first_attendance, v_last_attendance, v_attendance_days;
        EXIT WHEN v_cursor%NOTFOUND;
        
        v_count := v_count + 1;
        DBMS_OUTPUT.PUT_LINE('用户' || v_count || ': ' || v_person_name || 
                           ' (ID:' || v_user_id || ') 记录数:' || v_total_records || 
                           ' 考勤天数:' || v_attendance_days);
        
        IF v_count >= 5 THEN -- 只显示前5个用户
            EXIT;
        END IF;
    END LOOP;
    
    DBMS_OUTPUT.PUT_LINE('总共找到 ' || v_count || ' 个用户的考勤记录');
    CLOSE v_cursor;
END;
/

-- 6.3 调用修复后的综合统计存储过程（调试模式）
DECLARE
    v_cursor SYS_REFCURSOR;
    v_person_name VARCHAR2(50);
    v_user_id NUMBER;
    v_day_01 VARCHAR2(20);
    v_day_02 VARCHAR2(20);
    v_day_03 VARCHAR2(20);
    v_count NUMBER := 0;
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== 调用修复后的综合统计存储过程（调试模式） ===');
    
    -- 调用调试模式
    SP_ATTENDANCE_COMPREHENSIVE_STATISTICS_FIXED('&test_month', NULL, NULL, 1, v_cursor);
    
    LOOP
        FETCH v_cursor INTO v_person_name, v_user_id, v_day_01, v_day_02, v_day_03;
        EXIT WHEN v_cursor%NOTFOUND;
        
        v_count := v_count + 1;
        DBMS_OUTPUT.PUT_LINE('用户' || v_count || ': ' || v_person_name || 
                           ' (ID:' || v_user_id || ') 1号:' || v_day_01 || 
                           ' 2号:' || v_day_02 || ' 3号:' || v_day_03);
        
        IF v_count >= 3 THEN -- 只显示前3个用户
            EXIT;
        END IF;
    END LOOP;
    
    DBMS_OUTPUT.PUT_LINE('综合统计找到 ' || v_count || ' 个用户');
    CLOSE v_cursor;
END;
/

-- 7. 创建测试数据（如果需要）
PROMPT ========== 7. 创建测试数据选项 ==========
PROMPT 如果当前月份没有数据，可以执行以下语句创建测试数据：
PROMPT EXEC SP_CREATE_TEST_DATA('&test_month', 3);

-- 8. 手动SQL测试
PROMPT ========== 8. 手动SQL测试 ==========
PROMPT 手动执行以下SQL来验证逻辑：

SELECT 
    person_name,
    user_id,
    CASE WHEN MAX(CASE WHEN TO_CHAR(checkdate, 'DD') = '01' THEN 1 END) = 1 THEN 
        '√' || TO_CHAR(ROUND(NVL((MAX(CASE WHEN TO_CHAR(checkdate, 'DD') = '01' 
        THEN checkdate END) - MIN(CASE WHEN TO_CHAR(checkdate, 'DD') = '01' 
        THEN checkdate END)) * 24, 0), 2), 'FM999990.00') 
    ELSE '×0' END AS DAY_01,
    CASE WHEN MAX(CASE WHEN TO_CHAR(checkdate, 'DD') = '02' THEN 1 END) = 1 THEN 
        '√' || TO_CHAR(ROUND(NVL((MAX(CASE WHEN TO_CHAR(checkdate, 'DD') = '02' 
        THEN checkdate END) - MIN(CASE WHEN TO_CHAR(checkdate, 'DD') = '02' 
        THEN checkdate END)) * 24, 0), 2), 'FM999990.00') 
    ELSE '×0' END AS DAY_02
FROM B_KQ02
WHERE checkdate IS NOT NULL 
  AND TO_CHAR(checkdate, 'YYYY-MM') = '&test_month'
GROUP BY person_name, user_id
HAVING COUNT(*) > 0
ORDER BY person_name, user_id;

PROMPT ========== 调试完成 ==========
PROMPT 请检查以上输出结果，特别关注：
PROMPT 1. 是否有匹配月份的数据
PROMPT 2. 日期格式是否正确
PROMPT 3. 存储过程调用是否成功
PROMPT 4. 如果没有数据，请考虑创建测试数据或检查实际数据的月份格式
