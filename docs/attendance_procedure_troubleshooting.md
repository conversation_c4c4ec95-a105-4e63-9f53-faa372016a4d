# 考勤统计存储过程问题诊断与解决方案

## 问题描述
存储过程 `SP_ATTENDANCE_COMPREHENSIVE_STATISTICS` 执行后返回空结果集，但 B_KQ02 表中确实存在考勤数据。

## 问题分析

### 1. 可能的原因

#### A. 日期格式问题
- **问题**: `TO_CHAR(checkdate, 'YYYY-MM')` 格式与传入参数不匹配
- **症状**: 即使有数据也查询不到结果
- **检查方法**: 查看实际数据的日期格式

#### B. NULL值处理问题
- **问题**: checkdate 字段包含 NULL 值，影响查询结果
- **症状**: 部分数据丢失或查询异常
- **检查方法**: 统计 NULL 值数量

#### C. 参数传递问题
- **问题**: 月份参数格式不正确或包含特殊字符
- **症状**: 参数验证失败或查询条件错误
- **检查方法**: 验证参数格式和内容

#### D. 动态SQL构建问题
- **问题**: 动态SQL语法错误或逻辑错误
- **症状**: SQL执行失败或返回意外结果
- **检查方法**: 输出并检查生成的SQL

### 2. 主要修复点

#### A. 增强参数验证
```sql
-- 原版本
IF P_MONTH IS NULL OR LENGTH(P_MONTH) != 7 OR SUBSTR(P_MONTH, 5, 1) != '-' THEN
    RAISE_APPLICATION_ERROR(-20001, '月份参数格式错误，请使用YYYY-MM格式');
END IF;

-- 修复版本
IF P_MONTH IS NULL OR LENGTH(P_MONTH) != 7 OR SUBSTR(P_MONTH, 5, 1) != '-' THEN
    RAISE_APPLICATION_ERROR(-20001, '月份参数格式错误，请使用YYYY-MM格式，当前值：' || NVL(P_MONTH, 'NULL'));
END IF;
```

#### B. 改进WHERE条件构建
```sql
-- 原版本
' WHERE TO_CHAR(checkdate, ''YYYY-MM'') = ''' || P_MONTH || ''''

-- 修复版本
' WHERE checkdate IS NOT NULL AND TO_CHAR(checkdate, ''YYYY-MM'') = ''' || P_MONTH || ''''
```

#### C. 增加数据存在性检查
```sql
-- 新增：检查是否有匹配的数据
EXECUTE IMMEDIATE 
    'SELECT COUNT(*) FROM B_KQ02 WHERE checkdate IS NOT NULL AND ' ||
    'TO_CHAR(checkdate, ''YYYY-MM'') = ''' || P_MONTH || ''''
INTO V_COUNT;

IF V_COUNT = 0 THEN
    -- 返回空结果集但保持结构
    V_SQL := 'SELECT CAST(NULL AS VARCHAR2(50)) as person_name, CAST(NULL AS NUMBER) as user_id';
    FOR i IN 1..V_DAYS_IN_MONTH LOOP
        V_SQL := V_SQL || ', CAST(NULL AS VARCHAR2(20)) AS DAY_' || LPAD(i, 2, '0');
    END LOOP;
    V_SQL := V_SQL || ' FROM DUAL WHERE 1=0';
    OPEN P_RESULT FOR V_SQL;
    RETURN;
END IF;
```

#### D. 改进动态列生成逻辑
```sql
-- 原版本
', CASE WHEN MAX(CASE WHEN TO_CHAR(checkdate, ''DD'') = ''' || LPAD(i, 2, '0') || 
''' THEN ''√'' END) IS NOT NULL THEN ' ||

-- 修复版本
', CASE WHEN MAX(CASE WHEN TO_CHAR(checkdate, ''DD'') = ''' || LPAD(i, 2, '0') || 
''' THEN 1 END) = 1 THEN ' ||
```

## 诊断步骤

### 1. 执行基础数据检查
```sql
-- 检查表中是否有数据
SELECT COUNT(*) FROM B_KQ02;

-- 检查非空日期记录
SELECT COUNT(*) FROM B_KQ02 WHERE checkdate IS NOT NULL;

-- 检查日期格式
SELECT 
    checkdate,
    TO_CHAR(checkdate, 'YYYY-MM-DD HH24:MI:SS'),
    TO_CHAR(checkdate, 'YYYY-MM')
FROM B_KQ02 
WHERE checkdate IS NOT NULL 
  AND ROWNUM <= 5;
```

### 2. 检查特定月份数据
```sql
-- 替换 '2024-03' 为您要查询的月份
SELECT COUNT(*) 
FROM B_KQ02 
WHERE checkdate IS NOT NULL 
  AND TO_CHAR(checkdate, 'YYYY-MM') = '2024-03';
```

### 3. 运行调试脚本
```sql
-- 执行完整的调试脚本
@debug_attendance_procedures.sql
```

### 4. 测试修复后的存储过程
```sql
-- 调用修复版本（调试模式）
DECLARE
    v_cursor SYS_REFCURSOR;
BEGIN
    SP_ATTENDANCE_COMPREHENSIVE_STATISTICS_FIXED('2024-03', NULL, NULL, 1, v_cursor);
    -- 处理结果...
END;
/
```

## 解决方案

### 1. 立即解决方案
使用修复后的存储过程 `SP_ATTENDANCE_COMPREHENSIVE_STATISTICS_FIXED`：

```sql
-- 部署修复后的存储过程
@attendance_statistics_procedures_fixed.sql

-- 测试调用
DECLARE
    v_cursor SYS_REFCURSOR;
    v_person_name VARCHAR2(50);
    v_user_id NUMBER;
    -- 其他变量...
BEGIN
    SP_ATTENDANCE_COMPREHENSIVE_STATISTICS_FIXED('2024-03', NULL, NULL, 0, v_cursor);
    
    LOOP
        FETCH v_cursor INTO v_person_name, v_user_id /* 其他列 */;
        EXIT WHEN v_cursor%NOTFOUND;
        
        DBMS_OUTPUT.PUT_LINE('用户: ' || v_person_name || ', ID: ' || v_user_id);
    END LOOP;
    
    CLOSE v_cursor;
END;
/
```

### 2. 创建测试数据（如果需要）
```sql
-- 如果当前没有测试数据，可以创建一些
EXEC SP_CREATE_TEST_DATA('2024-03', 5);
```

### 3. 更新MyBatis映射
更新 `AttendanceStatisticsDao.xml` 中的存储过程调用：

```xml
<!-- 使用修复后的存储过程 -->
<select id="getAttendanceComprehensiveStatistics" statementType="CALLABLE" resultType="map">
    {CALL SP_ATTENDANCE_COMPREHENSIVE_STATISTICS_FIXED(
        #{month, mode=IN, jdbcType=VARCHAR},
        #{pj0101, mode=IN, jdbcType=NUMERIC},
        #{personType, mode=IN, jdbcType=VARCHAR},
        #{debug, mode=IN, jdbcType=NUMERIC},
        #{result, mode=OUT, jdbcType=CURSOR, resultMap=comprehensiveResultMap}
    )}
</select>
```

## 预防措施

### 1. 数据质量检查
- 定期检查 checkdate 字段的 NULL 值
- 验证日期格式的一致性
- 监控数据录入质量

### 2. 存储过程优化
- 添加详细的错误日志
- 实现参数验证和边界检查
- 提供调试模式支持

### 3. 测试策略
- 建立自动化测试用例
- 定期验证存储过程功能
- 创建测试数据集

## 常见问题FAQ

### Q1: 为什么原存储过程返回空结果？
A1: 主要原因是缺少对 NULL 值的处理和数据存在性检查。修复版本增加了这些检查。

### Q2: 如何验证修复是否成功？
A2: 运行调试脚本，检查是否能正常返回数据，并验证数据的正确性。

### Q3: 如果仍然没有数据怎么办？
A3: 检查传入的月份参数是否与实际数据匹配，可能需要调整月份格式或创建测试数据。

### Q4: 性能是否会受到影响？
A4: 修复版本增加了一些检查，但对性能影响很小。建议在生产环境中关闭调试模式。

## 联系支持
如果问题仍然存在，请提供以下信息：
1. 调试脚本的完整输出
2. 实际的月份参数值
3. B_KQ02 表的数据样本
4. Oracle数据库版本信息
