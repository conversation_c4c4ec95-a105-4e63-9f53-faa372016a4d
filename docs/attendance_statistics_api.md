# 考勤统计API使用文档

## 概述

本文档描述了考勤统计功能的API接口使用方法，包括考勤状态统计、工时计算、综合统计等功能。

## 基础信息

- **基础路径**: `/attendance/statistics`
- **数据格式**: JSON
- **字符编码**: UTF-8

## API接口列表

### 1. 考勤统计查询（通用接口）

**接口地址**: `GET /attendance/statistics/query`

**功能描述**: 根据查询参数自动选择统计类型进行查询

**请求参数**:
```json
{
  "month": "2024-03",                    // 必填，月份（YYYY-MM格式）
  "pj0101": 123456,                      // 可选，项目ID
  "personType": "1",                     // 可选，人员类型（1-建筑工人，2-管理人员）
  "personName": "张三",                   // 可选，人员姓名（模糊查询）
  "userId": 789,                         // 可选，人员ID
  "statisticsType": "COMPREHENSIVE",     // 统计类型（STATUS/WORKHOURS/COMPREHENSIVE）
  "includeSummary": true                 // 是否包含汇总信息
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "personName": "张三",
      "userId": 123,
      "dailyData": {
        "DAY_01": "√8.5",
        "DAY_02": "×0",
        "DAY_03": "√7.2",
        ...
      },
      "summary": {
        "attendanceDays": 20,
        "absentDays": 11,
        "totalWorkHours": 168.5,
        "averageWorkHours": 8.43,
        "attendanceRate": 64.52
      }
    }
  ]
}
```

### 2. 考勤状态统计

**接口地址**: `GET /attendance/statistics/status`

**功能描述**: 仅查询考勤状态（√/×），不包含工时信息

**请求参数**: 同通用接口

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "personName": "张三",
      "userId": 123,
      "dailyData": {
        "DAY_01": "√",
        "DAY_02": "×",
        "DAY_03": "√",
        ...
      }
    }
  ]
}
```

### 3. 工时统计

**接口地址**: `GET /attendance/statistics/workhours`

**功能描述**: 仅查询工时信息，不包含考勤状态

**请求参数**: 同通用接口

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "personName": "张三",
      "userId": 123,
      "dailyData": {
        "WORKHOURS_01": 8.5,
        "WORKHOURS_02": 0,
        "WORKHOURS_03": 7.2,
        ...
      }
    }
  ]
}
```

### 4. 综合考勤统计

**接口地址**: `GET /attendance/statistics/comprehensive`

**功能描述**: 查询综合信息（状态+工时）

**请求参数**: 同通用接口

### 5. 月度考勤汇总

**接口地址**: `GET /attendance/statistics/summary`

**功能描述**: 获取月度考勤汇总统计信息

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "totalPersons": 150,
    "totalAttendanceDays": 25,
    "totalRecords": 3750,
    "workerCount": 120,
    "managerCount": 30,
    "daysInMonth": 31
  }
}
```

### 6. 导出Excel

**接口地址**: `GET /attendance/statistics/export`

**功能描述**: 导出考勤统计数据为Excel文件

**请求参数**: 同通用接口

**响应**: Excel文件下载

### 7. POST方式查询

**接口地址**: `POST /attendance/statistics/query`

**功能描述**: 使用POST方式进行复杂查询

**请求体**:
```json
{
  "month": "2024-03",
  "pj0101": 123456,
  "personType": "1",
  "statisticsType": "COMPREHENSIVE",
  "includeSummary": true
}
```

### 8. 获取月份天数

**接口地址**: `GET /attendance/statistics/days/{month}`

**功能描述**: 获取指定月份的天数

**路径参数**:
- `month`: 月份（YYYY-MM格式）

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": 31
}
```

### 9. 参数验证

**接口地址**: `GET /attendance/statistics/validate`

**功能描述**: 验证查询参数是否有效

**请求参数**: 同通用接口

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": "参数验证通过"
}
```

## 参数说明

### 统计类型（statisticsType）

- `STATUS`: 仅考勤状态（√/×）
- `WORKHOURS`: 仅工时统计
- `COMPREHENSIVE`: 综合统计（状态+工时）

### 人员类型（personType）

- `1`: 建筑工人
- `2`: 管理人员

### 月份格式（month）

- 格式: `YYYY-MM`
- 示例: `2024-03`、`2023-12`

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 500 | 服务器内部错误 |

## 使用示例

### JavaScript调用示例

```javascript
// 查询2024年3月的综合考勤统计
fetch('/attendance/statistics/query?month=2024-03&statisticsType=COMPREHENSIVE&includeSummary=true')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      console.log('查询成功:', data.data);
      // 处理返回的考勤数据
      data.data.forEach(person => {
        console.log(`${person.personName}: 出勤${person.summary.attendanceDays}天`);
      });
    } else {
      console.error('查询失败:', data.msg);
    }
  })
  .catch(error => {
    console.error('请求错误:', error);
  });

// 导出Excel文件
function exportExcel() {
  const params = new URLSearchParams({
    month: '2024-03',
    statisticsType: 'COMPREHENSIVE',
    includeSummary: 'true'
  });
  
  window.open(`/attendance/statistics/export?${params.toString()}`);
}
```

### Java调用示例

```java
// 使用RestTemplate调用
AttendanceQueryDTO queryDTO = new AttendanceQueryDTO()
    .setMonth("2024-03")
    .setStatisticsType(AttendanceQueryDTO.StatisticsType.COMPREHENSIVE)
    .setIncludeSummary(true);

ResponseEntity<Result<List<AttendanceStatisticsDTO>>> response = 
    restTemplate.postForEntity("/attendance/statistics/query", queryDTO, 
        new ParameterizedTypeReference<Result<List<AttendanceStatisticsDTO>>>() {});

if (response.getBody().getCode() == 0) {
    List<AttendanceStatisticsDTO> data = response.getBody().getData();
    // 处理返回数据
}
```

## 性能优化建议

1. **索引优化**: 确保在`checkdate`、`user_id`、`pj0101`字段上创建了索引
2. **分页查询**: 对于大量数据，建议使用分页查询
3. **缓存策略**: 可以对月度汇总数据进行缓存
4. **异步导出**: 对于大量数据的Excel导出，建议使用异步处理

## 注意事项

1. 月份参数必须是有效的YYYY-MM格式
2. 项目ID必须存在于系统中
3. 人员类型只能是1或2
4. 导出Excel功能可能需要较长时间，请耐心等待
5. 建议在非高峰期进行大量数据的统计查询
